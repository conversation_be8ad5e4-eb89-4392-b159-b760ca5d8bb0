#!/usr/bin/env python3
"""
Document Inventory Script for Goali

This script scans all markdown files in the repository, extracts metadata,
and enriches it with git history information to provide a comprehensive
view of documentation maintenance patterns.
"""

import pathlib
import subprocess
import json
import re
import yaml
import frontmatter
import datetime
from collections import Counter

DEFAULT_EXCLUDE_DIRS = [
    'frontend',
    '.pytest_cache',
    r'backend\.pytest_cache',  # Raw string for regex-like pattern
    r'backend\staticfiles',   # Raw string for regex-like pattern
    '.VSCodeCounter',
    'node_modules',  # Common exclusion
    'myenv',         # Python virtual environment
    '__pycache__',
    '.git'
]

def get_files():
    """Find all markdown files in the repository, excluding specified directories."""
    all_files = list(pathlib.Path('.').glob('**/*.md'))
    filtered_files = []
    for file_path in all_files:
        # Check if any part of the path matches any exclude pattern
        # os.path.normpath is used to ensure consistent path separators
        normalized_path_parts = [part.lower() for part in pathlib.Path(file_path).parts]
        
        is_excluded = False
        for exclude_pattern in DEFAULT_EXCLUDE_DIRS:
            # Simple string matching for directory names
            # More complex patterns might require regex
            if exclude_pattern.lower() in normalized_path_parts:
                is_excluded = True
                break
        
        if not is_excluded:
            filtered_files.append(file_path)
            
    return filtered_files

def get_git_commit_history(file_path):
    """Retrieve detailed git history for a file"""
    try:
        # Get the list of commits for this file with details
        result = subprocess.run(
            ['git', 'log', '--pretty=format:%H|%an|%ae|%ad|%s', '--', str(file_path)],
            capture_output=True, text=True, check=True
        )
        
        commits = []
        for line in result.stdout.strip().split('\n'):
            if not line:
                continue
            
            parts = line.split('|', 4)
            if len(parts) >= 5:
                commit = {
                    'sha': parts[0],
                    'author_name': parts[1],
                    'author_email': parts[2],
                    'date': parts[3],
                    'message': parts[4]
                }
                commits.append(commit)
        
        # Get creation date (first commit)
        if commits:
            first_commit = commits[-1]
            last_commit = commits[0]
        else:
            return {
                'commits': [],
                'creation_date': None,
                'last_modified_date': None,
                'commit_count': 0,
                'unique_authors': [],
                'author_counts': {},
                'time_since_last_update': None,
                'update_frequency': None
            }
        
        # Calculate unique authors
        authors = [commit['author_name'] for commit in commits]
        unique_authors = list(set(authors))
        author_counts = dict(Counter(authors))
        
        # Calculate time since last update
        try:
            last_date = datetime.datetime.strptime(last_commit['date'], '%a %b %d %H:%M:%S %Y %z')
            now = datetime.datetime.now(last_date.tzinfo)
            time_since_last_update = (now - last_date).days
        except (ValueError, TypeError):
            time_since_last_update = None
            
        # Estimate update frequency (average days between commits)
        if len(commits) > 1:
            try:
                first_date = datetime.datetime.strptime(first_commit['date'], '%a %b %d %H:%M:%S %Y %z')
                total_days = (last_date - first_date).days
                if total_days > 0:
                    update_frequency = total_days / (len(commits) - 1)
                else:
                    update_frequency = 0
            except (ValueError, TypeError):
                update_frequency = None
        else:
            update_frequency = None
            
        return {
            'commits': commits,
            'creation_date': first_commit['date'] if commits else None,
            'last_modified_date': last_commit['date'] if commits else None,
            'commit_count': len(commits),
            'unique_authors': unique_authors,
            'author_counts': author_counts,
            'time_since_last_update': time_since_last_update,
            'update_frequency': update_frequency
        }
        
    except Exception as e:
        print(f"Error getting git history for {file_path}: {e}")
        return {
            'commits': [],
            'creation_date': None,
            'last_modified_date': None,
            'commit_count': 0,
            'unique_authors': [],
            'author_counts': {},
            'time_since_last_update': None,
            'update_frequency': None
        }

def get_related_code_files(file_path):
    """Identify potentially related code files based on path patterns"""
    related_files = []
    file_path_str = str(file_path)
    
    # Map documentation paths to potential code paths
    if 'docs/backend/agents/' in file_path_str:
        # Check for related agent implementation files
        agent_name = re.search(r'([a-zA-Z_]+)_agent', file_path_str, re.IGNORECASE)
        if agent_name:
            agent_code = f"backend/apps/main/agents/{agent_name.group(1).lower()}_agent.py"
            if pathlib.Path(agent_code).exists():
                related_files.append(agent_code)
                
    elif 'docs/backend/' in file_path_str:
        # Try to match with backend code files
        component = file_path_str.split('/')[-1].replace('.md', '').lower()
        potential_paths = [
            f"backend/apps/main/{component}.py",
            f"backend/apps/main/services/{component}.py",
            f"backend/apps/main/agents/{component}.py"
        ]
        for path in potential_paths:
            if pathlib.Path(path).exists():
                related_files.append(path)
    
    # Return paths of existing files only
    return related_files

def get_code_sync_status(file_path, git_history):
    """
    Compare documentation and related code last-update times
    to detect potential documentation drift
    """
    related_files = get_related_code_files(file_path)
    if not related_files or not git_history['last_modified_date']:
        return {
            'related_code_files': related_files,
            'sync_status': 'unknown',
            'days_behind': None,
            'sync_risk': None
        }
    
    # Check the last commit date of related code files
    code_last_updated = None
    for code_file in related_files:
        try:
            code_history = get_git_commit_history(code_file)
            if code_history['last_modified_date']:
                code_date = datetime.datetime.strptime(
                    code_history['last_modified_date'], 
                    '%a %b %d %H:%M:%S %Y %z'
                )
                if not code_last_updated or code_date > code_last_updated:
                    code_last_updated = code_date
        except Exception as e:
            print(f"Error comparing dates for {code_file}: {e}")
    
    if not code_last_updated:
        return {
            'related_code_files': related_files,
            'sync_status': 'unknown',
            'days_behind': None,
            'sync_risk': None
        }
    
    # Compare doc and code update times
    try:
        doc_date = datetime.datetime.strptime(
            git_history['last_modified_date'], 
            '%a %b %d %H:%M:%S %Y %z'
        )
        
        days_behind = (code_last_updated - doc_date).days if doc_date < code_last_updated else 0
        
        if days_behind <= 0:
            sync_status = 'up_to_date'
            sync_risk = 'low'
        elif days_behind <= 30:
            sync_status = 'slightly_behind'
            sync_risk = 'low'
        elif days_behind <= 90:
            sync_status = 'behind'
            sync_risk = 'medium'
        else:
            sync_status = 'significantly_behind'
            sync_risk = 'high'
            
        return {
            'related_code_files': related_files,
            'sync_status': sync_status,
            'days_behind': days_behind,
            'sync_risk': sync_risk
        }
    except Exception as e:
        print(f"Error calculating sync status: {e}")
        return {
            'related_code_files': related_files,
            'sync_status': 'error',
            'days_behind': None,
            'sync_risk': None
        }

def extract_metadata(file_path):
    """Extract metadata from a markdown file"""
    content = file_path.read_text(encoding='utf-8', errors='replace')
    
    # Parse front matter if it exists
    try:
        post = frontmatter.loads(content)
        metadata = dict(post.metadata)
    except yaml.YAMLError as e:
        print(f"Warning: Could not parse front matter for {file_path}. Error: {e}")
        return {
            'path': str(file_path),
            'title': None,
            'audience': 'unknown',
            'section_count': 0,
            'section_structure': [],
            'internal_links': [],
            'lines': len(content.splitlines()),
            'bytes': len(content),
            'has_todo': False,
            'has_obsolete': False,
            'git_history': get_git_commit_history(file_path),
            'code_sync': get_code_sync_status(file_path, {'last_modified_date': None})
        }
    
    # Extract title (first h1 heading)
    title_match = re.search(r'^# (.+)$', content, re.MULTILINE)
    title = title_match.group(1) if title_match else None
    
    # Extract section headings to understand document structure
    headings = re.findall(r'^(#{2,6}) (.+)$', content, re.MULTILINE)
    section_structure = [
        {'level': len(h[0]), 'title': h[1]} 
        for h in headings
    ]
    
    # Check for obsolescence indicators
    has_todo = bool(re.search(r'\bTODO\b', content, re.IGNORECASE))
    has_obsolete = bool(re.search(r'\bobsolete\b|\bdeprecated\b', content, re.IGNORECASE))
    
    # Check for links to other docs
    internal_links = re.findall(r'\[.+?\]\((.+?\.md)\)', content)
    
    # Get git history
    git_history = get_git_commit_history(file_path)
    
    # Check sync status with related code
    code_sync = get_code_sync_status(file_path, git_history)
    
    return {
        'path': str(file_path),
        'title': title,
        'audience': metadata.get('audience', 'unknown'),
        'section_count': len(section_structure),
        'section_structure': section_structure,
        'internal_links': internal_links,
        'lines': len(content.splitlines()),
        'bytes': len(content),
        'has_todo': has_todo,
        'has_obsolete': has_obsolete,
        'git_history': git_history,
        'code_sync': code_sync
    }

def infer_audience(metadata):
    """Infer audience from file path and title patterns"""
    path = metadata['path']
    title = metadata.get('title', '')
    
    # Rule-based classification
    if any(indicator in path.lower() for indicator in ['ai_', '_flow.md', 'agent_']):
        return 'ai'
    elif '/docs/backend/' in path and 'README_DEV' in path:
        return 'human'
    elif 'CONTEXT_FOR_LLM' in path:
        return 'ai'
    elif title and any(term in title.lower() for term in ['ai', 'assistant', 'agent', 'llm']):
        return 'ai'
    elif '/docs/users/' in path:
        return 'human'
    elif '/testing/' in path and 'GUIDE' in path:
        return 'human'
    
    # Default classification based on directory
    if '/docs/' in path:
        return 'human'
    else:
        return 'unknown'

def main():
    print("Scanning for markdown files...")
    files = get_files()
    print(f"Found {len(files)} markdown files.")
    
    results = []
    
    for i, file_path in enumerate(files):
        if i % 10 == 0:
            print(f"Processing file {i+1}/{len(files)}: {file_path}")
            
        metadata = extract_metadata(file_path)
        if metadata['audience'] == 'unknown':
            metadata['audience'] = infer_audience(metadata)
        results.append(metadata)
    
    print("Writing results to doc_index.json...")
    with open('doc_index.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print("Done!")

if __name__ == "__main__":
    main()
