#!/bin/bash

# <PERSON><PERSON>t to run the document inventory process
# This script should be executed from the root of the repository,
# or it will navigate to the root assuming it's in scripts/doc_processing/

# Get the directory where the script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
REPO_ROOT_DIR="$( cd "$SCRIPT_DIR/../.." && pwd )" # Navigate two levels up to the repo root

echo "Changing to repository root: $REPO_ROOT_DIR"
cd "$REPO_ROOT_DIR" || exit 1

echo "Running Document Inventory..."

# Activate the virtual environment
if [ -f "$REPO_ROOT_DIR/.venv/bin/activate" ]; then
    source "$REPO_ROOT_DIR/.venv/bin/activate"
    echo "Virtual environment activated."
else
    echo "Virtual environment not found at $REPO_ROOT_DIR/.venv/bin/activate. Please ensure it's set up correctly."
    exit 1
fi

python scripts/doc_processing/doc_inventory.py

echo "Document inventory script finished."
echo "Check for doc_index.json in the repository root."
