#!/usr/bin/env python3
"""
Enhanced Documentation Analysis Script for Goali

This comprehensive analyzer provides detailed insights into documentation quality,
organization, and metadata by:
1. Using parallel processing for speed
2. Implementing incremental analysis techniques
3. Using more efficient algorithms for similarity detection
4. Providing smarter, more actionable recommendations
5. Adding visualization capabilities
6. Introducing advanced metrics (readability, information density)
7. Analyzing documentation location patterns and organization
8. Providing detailed metadata analysis and recommendations
9. Classifying documentation by type and identifying gaps
10. Checking documentation against standards and best practices

Usage:
    python inventory_analyzer.py --inventory doc_index.json [options]
"""

import json
import os
import re
import pathlib
import datetime
import argparse
import logging
from collections import defaultdict, Counter
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor
from typing import Dict, List, Tuple, Any, Set, Optional
import multiprocessing
import time
import math
import functools
# dataclass removed as DocumentSummary is now in a separate file
import numpy as np
import hashlib

# --- Core imports from the new analyzer structure ---
from scripts.doc_processing.analyzer.core.constants import (
    MAX_WORKERS, MIN_DOC_BYTES, DEFAULT_EMBEDDING_MODEL,
    SIMILAR_DOCS_THRESHOLD, CONCEPTUAL_SIMILARITY_THRESHOLD,
    DEFAULT_EXCLUDE_DIRS, READABILITY_THRESHOLDS, DOC_TYPE_PATTERNS,
    EXPECTED_METADATA_FIELDS, DOC_ORG_PATTERNS
)
from scripts.doc_processing.analyzer.core.dataclasses import DocumentSummary
from scripts.doc_processing.analyzer.core.cache import CachedDocumentStore
# --- End core imports ---

# --- Analyzer module imports ---
from scripts.doc_processing.analyzer.analyzers.exact_duplicates import analyze_exact_duplicates
from scripts.doc_processing.analyzer.analyzers.exact_duplicates import analyze_exact_duplicates
from scripts.doc_processing.analyzer.analyzers.similar_content import analyze_similar_content
from scripts.doc_processing.analyzer.analyzers.exact_duplicates import analyze_exact_duplicates
from scripts.doc_processing.analyzer.analyzers.similar_content import analyze_similar_content
from scripts.doc_processing.analyzer.analyzers.conceptual_similarity import analyze_conceptual_similarity
from scripts.doc_processing.analyzer.analyzers.exact_duplicates import analyze_exact_duplicates
from scripts.doc_processing.analyzer.analyzers.similar_content import analyze_similar_content
from scripts.doc_processing.analyzer.analyzers.conceptual_similarity import analyze_conceptual_similarity
from scripts.doc_processing.analyzer.analyzers.document_clusters import analyze_document_clusters
from scripts.doc_processing.analyzer.analyzers.exact_duplicates import analyze_exact_duplicates
from scripts.doc_processing.analyzer.analyzers.similar_content import analyze_similar_content
from scripts.doc_processing.analyzer.analyzers.conceptual_similarity import analyze_conceptual_similarity
from scripts.doc_processing.analyzer.analyzers.document_clusters import analyze_document_clusters
from scripts.doc_processing.analyzer.analyzers.structure import analyze_structure
from scripts.doc_processing.analyzer.analyzers.exact_duplicates import analyze_exact_duplicates
from scripts.doc_processing.analyzer.analyzers.similar_content import analyze_similar_content
from scripts.doc_processing.analyzer.analyzers.conceptual_similarity import analyze_conceptual_similarity
from scripts.doc_processing.analyzer.analyzers.document_clusters import analyze_document_clusters
from scripts.doc_processing.analyzer.analyzers.structure import analyze_structure
from scripts.doc_processing.analyzer.analyzers.staleness import analyze_staleness
from scripts.doc_processing.analyzer.analyzers.links import analyze_links
from scripts.doc_processing.analyzer.analyzers.audience import analyze_audience
from scripts.doc_processing.analyzer.analyzers.maintenance import analyze_maintenance
# --- End analyzer module imports ---

# Optional imports - gracefully handle if not available
try:
    import matplotlib.pyplot as plt
    import networkx as nx
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.metrics.pairwise import cosine_similarity
    from sklearn.cluster import DBSCAN
    HAS_VISUALIZATION = True
except ImportError:
    HAS_VISUALIZATION = False

try:
    from textstat import flesch_reading_ease, text_standard
    HAS_READABILITY = True
except ImportError:
    HAS_READABILITY = False

try:
    import openai
    import tiktoken
    HAS_OPENAI = True
except ImportError:
    HAS_OPENAI = False

try:
    # Local embedding models (sentence-transformers is much faster than OpenAI API)
    from sentence_transformers import SentenceTransformer
    HAS_LOCAL_EMBEDDINGS = True
except ImportError:
    HAS_LOCAL_EMBEDDINGS = False

# Configure logging
# Ensure logger name is distinct if this script becomes a module within a larger app
logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger('inventory_analyzer_script') # Changed logger name

# Constants, DocumentSummary, and CachedDocumentStore are now imported from .analyzer.core

class ImprovedAnalyzer:
    """Enhanced document analyzer with performance and quality improvements"""

    def __init__(self, inventory_path: str, options: Dict = None):
        self.inventory_path = inventory_path
        self.options = options or {}
        self.docs = []
        self.doc_store = CachedDocumentStore()
        self.analysis_results = {}
        self.start_time = time.time()
        self.exclude_dirs = self.options.get('exclude_dirs', [])

        # Configure embedding model
        self.setup_embedding_model()

    def setup_embedding_model(self):
        """Set up the embedding model based on available libraries"""
        model_name = self.options.get('embedding_model', DEFAULT_EMBEDDING_MODEL)

        # Use local models if available (much faster)
        if HAS_LOCAL_EMBEDDINGS:
            try:
                logger.info(f"Loading local embedding model: {model_name} on CPU for multiprocessing compatibility")
                # Explicitly load model on CPU to avoid multiprocessing issues with MPS/GPU
                self.doc_store.embedding_model = SentenceTransformer(model_name, device='cpu')
                return
            except Exception as e:
                logger.warning(f"Failed to load local embedding model on CPU: {e}")
                logger.warning("Attempting to load on default device (may cause multiprocessing issues).")
                try:
                    self.doc_store.embedding_model = SentenceTransformer(model_name)
                    return
                except Exception as e_fallback:
                    logger.warning(f"Failed to load local embedding model on default device: {e_fallback}")

        # Fall back to OpenAI if available and configured
        if HAS_OPENAI and 'openai_api_key' in self.options:
            logger.info("Using OpenAI for embeddings")
            openai.api_key = self.options['openai_api_key']
            # We'll handle this separately in methods that need embeddings
            return

        logger.warning("No embedding model available. Conceptual similarity analysis will be limited.")

    def load_inventory(self) -> bool:
        """Load document inventory with performance tracking"""
        start = time.time()
        try:
            logger.info(f"Loading document inventory from {self.inventory_path}")
            with open(self.inventory_path, 'r') as f:
                all_docs = json.load(f)

            # Filter out documents in excluded directories
            if self.exclude_dirs:
                logger.info(f"Excluding directories: {', '.join(self.exclude_dirs)}")
                self.docs = [
                    doc for doc in all_docs
                    if not any(excluded_dir in doc['path'] for excluded_dir in self.exclude_dirs)
                ]
                logger.info(f"Filtered out {len(all_docs) - len(self.docs)} documents from excluded directories")
            else:
                self.docs = all_docs

            # Preload document summaries in parallel
            logger.info("Preloading document summaries...")
            self._preload_data()

            duration = time.time() - start
            logger.info(f"Loaded {len(self.docs)} documents in {duration:.2f}s")
            return True
        except Exception as e:
            logger.error(f"Error loading inventory: {e}")
            return False

    def _preload_data(self):
        """Preload commonly needed data in parallel to speed up analysis"""
        # First create list of valid files that can be processed
        valid_docs = []
        for doc in self.docs:
            if os.path.exists(doc['path']):
                valid_docs.append(doc)
            else:
                logger.warning(f"Skipping missing file: {doc['path']}")

        # Use valid docs only
        self.docs = valid_docs

        # Load content and generate summaries in parallel
        with ThreadPoolExecutor(max_workers=self.options.get('workers', max(4, multiprocessing.cpu_count()))) as executor:
            # Load content first
            list(executor.map(
                lambda doc: self.doc_store.get_content(doc['path']),
                self.docs
            ))

            # Then generate summaries
            list(executor.map(
                lambda doc: self.doc_store.get_summary(doc['path'], doc),
                self.docs
            ))

        logger.info(f"Preloaded {len(self.docs)} documents")

    def analyze(self):
        """Run all analyses with performance tracking"""
        if not self.docs:
            if not self.load_inventory():
                return {"error": "Failed to load document inventory"}

        logger.info("Starting analysis...")

        # Run analyses in parallel where possible
        with ThreadPoolExecutor(max_workers=self.options.get('workers', max(4, multiprocessing.cpu_count()))) as executor:
            # Start all analyses
            # Call the imported function for exact duplicates
            exact_duplicate_future = executor.submit(analyze_exact_duplicates, self.docs, self.doc_store)
            # Call the imported function for similar content
            similar_content_future = executor.submit(analyze_similar_content, self.docs, self.doc_store, self.options)
            # Call the imported function for structure analysis
            structure_future = executor.submit(analyze_structure, self.docs, self.doc_store)
            # Call the imported function for staleness analysis
            staleness_future = executor.submit(analyze_staleness, self.docs, self.options)
            # Call the imported function for links analysis
            link_future = executor.submit(analyze_links, self.docs)
            # Call the imported function for audience analysis
            audience_future = executor.submit(analyze_audience, self.docs)
            # Call the imported function for maintenance analysis
            maintenance_future = executor.submit(analyze_maintenance, self.docs)

            # Start new analyses for documentation location and metadata (if not skipped)
            doc_location_future = executor.submit(self.analyze_doc_location) if not self.options.get('skip_location') else None
            metadata_future = executor.submit(self.analyze_metadata) if not self.options.get('skip_metadata') else None
            doc_types_future = executor.submit(self.analyze_doc_types) if not self.options.get('skip_doc_types') else None
            doc_standards_future = executor.submit(self.analyze_doc_standards) if not self.options.get('skip_standards') else None

            # Start additional analyses that don't depend on others
            readability_future = executor.submit(self.analyze_readability) if HAS_READABILITY else None

            # Wait for all to complete and collect results
            self.analysis_results["exact_duplicates"] = exact_duplicate_future.result()
            self.analysis_results["similar_content"] = similar_content_future.result()
            self.analysis_results["structure"] = structure_future.result()
            self.analysis_results["staleness"] = staleness_future.result()
            self.analysis_results["links"] = link_future.result()
            self.analysis_results["audience"] = audience_future.result()
            self.analysis_results["maintenance"] = maintenance_future.result()

            # Collect results from new analyses (if not skipped)
            if doc_location_future:
                self.analysis_results["doc_location"] = doc_location_future.result()
            if metadata_future:
                self.analysis_results["metadata"] = metadata_future.result()
            if doc_types_future:
                self.analysis_results["doc_types"] = doc_types_future.result()
            if doc_standards_future:
                self.analysis_results["doc_standards"] = doc_standards_future.result()

            if readability_future:
                self.analysis_results["readability"] = readability_future.result()

        # Run conceptual similarity analysis (potentially slower, uses embeddings)
        # Call the imported function for conceptual similarity
        if self.doc_store.embedding_model is not None or HAS_OPENAI:
             self.analysis_results["conceptual_similarity"] = analyze_conceptual_similarity(
                 self.docs, self.doc_store, self.analysis_results, self.options
             )

        # Use fast TFIDF-based clustering as fallback if no embedding model
        # Note: HAS_OPENAI check is now inside analyze_conceptual_similarity
        # Call the imported function for document clusters
        if self.doc_store.embedding_model is None and HAS_VISUALIZATION:
            self.analysis_results["document_clusters"] = analyze_document_clusters(self.docs, self.doc_store, self.options)

        # Generate recommendations based on all analyses
        self.analysis_results["recommendations"] = self.generate_recommendations()

        # Record analysis time
        self.analysis_results["meta"] = {
            "analysis_time": time.time() - self.start_time,
            "document_count": len(self.docs),
            "timestamp": datetime.datetime.now().isoformat()
        }

        logger.info(f"Analysis completed in {self.analysis_results['meta']['analysis_time']:.2f}s")
        return self.analysis_results

    # analyze_exact_duplicates method removed as it's now in a separate module

    # compare_docs and analyze_similar_content methods removed as they are now in a separate module

    # analyze_conceptual_similarity method removed as it's now in a separate module

    # analyze_document_clusters method removed as it's now in a separate module

    # analyze_structure method removed as it's now in a separate module

    # analyze_staleness method removed as it's now in a separate module

    # analyze_links method removed as it's now in a separate module

    # analyze_audience method removed as it's now in a separate module

    # analyze_maintenance method removed as it's now in a separate module

    def analyze_doc_location(self):
        """Analyze documentation location patterns and organization"""
        logger.info("Analyzing documentation location patterns...")
        start = time.time()

        # Analyze directory structure
        dir_structure = defaultdict(list)
        dir_depths = defaultdict(int)
        dir_doc_types = defaultdict(Counter)
        dir_audiences = defaultdict(Counter)

        # Track organization patterns
        org_patterns = {pattern: [] for pattern in DOC_ORG_PATTERNS}

        # Track index documents
        index_docs = []

        # Track documentation gaps
        missing_indexes = set()

        # Analyze each document
        for doc in self.docs:
            path = doc["path"]
            summary = self.doc_store.get_summary(path, doc)

            # Add to directory structure
            dir_name = os.path.dirname(path)
            dir_structure[dir_name].append(summary)

            # Track directory depth
            depth = summary.location_depth
            dir_depths[dir_name] = max(dir_depths[dir_name], depth)

            # Track document types in directory
            dir_doc_types[dir_name][summary.doc_type] += 1

            # Track audiences in directory
            dir_audiences[dir_name][summary.audience] += 1

            # Check for index documents
            if summary.is_index:
                index_docs.append({
                    "path": path,
                    "directory": dir_name,
                    "title": summary.title,
                    "word_count": summary.word_count
                })

            # Check for organization patterns
            for pattern_name, pattern in DOC_ORG_PATTERNS.items():
                if re.search(pattern, path):
                    org_patterns[pattern_name].append(path)

        # Find directories without index documents
        for dir_name, docs in dir_structure.items():
            if len(docs) >= 3:  # Only consider directories with at least 3 docs
                has_index = any(doc.is_index for doc in docs)
                if not has_index:
                    missing_indexes.add(dir_name)

        # Analyze directory hierarchy
        dir_hierarchy = self._build_directory_hierarchy()

        # Calculate directory statistics
        dir_stats = {}
        for dir_name, docs in dir_structure.items():
            if len(docs) >= 2:  # Only include directories with multiple docs
                # Calculate average metadata score for the directory
                avg_metadata_score = sum(doc.metadata_score for doc in docs) / len(docs)

                # Calculate document type diversity
                doc_types = Counter(doc.doc_type for doc in docs)
                type_diversity = len(doc_types) / max(1, len(DOC_TYPE_PATTERNS) + 1)  # +1 for "index" type

                # Calculate audience consistency
                audiences = Counter(doc.audience for doc in docs)
                primary_audience = audiences.most_common(1)[0][0] if audiences else "unknown"
                audience_consistency = audiences.get(primary_audience, 0) / len(docs)

                dir_stats[dir_name] = {
                    "doc_count": len(docs),
                    "avg_metadata_score": avg_metadata_score,
                    "doc_types": dict(doc_types),
                    "type_diversity": type_diversity,
                    "primary_audience": primary_audience,
                    "audience_consistency": audience_consistency,
                    "has_index": dir_name not in missing_indexes
                }

        # Identify documentation organization patterns
        detected_patterns = {}
        for pattern_name, paths in org_patterns.items():
            if paths:
                detected_patterns[pattern_name] = {
                    "count": len(paths),
                    "examples": paths
                }

        duration = time.time() - start
        logger.info(f"Analyzed documentation location patterns in {duration:.2f}s")

        return {
            "dir_stats": dir_stats,
            "dir_hierarchy": dir_hierarchy,
            "index_docs": index_docs,
            "missing_indexes": list(missing_indexes),
            "org_patterns": detected_patterns,
            "analysis_time": duration
        }

    def _build_directory_hierarchy(self):
        """Build a hierarchical representation of the documentation directory structure"""
        # Create a tree structure
        hierarchy = {}

        # Get all unique directories
        all_dirs = set()
        for doc in self.docs:
            path = doc["path"]
            dir_path = os.path.dirname(path)

            # Add all parent directories
            parts = dir_path.split(os.sep)
            current = ""
            for part in parts:
                if part:
                    current = os.path.join(current, part) if current else part
                    all_dirs.add(current)

        # Build hierarchy
        for dir_path in sorted(all_dirs):
            parts = dir_path.split(os.sep)

            # Navigate to the right spot in the hierarchy
            current = hierarchy
            for i, part in enumerate(parts[:-1]):
                if part:
                    if part not in current:
                        current[part] = {}
                    current = current[part]

            # Add the final directory
            if parts and parts[-1]:
                if parts[-1] not in current:
                    current[parts[-1]] = {}

        return hierarchy

    def analyze_metadata(self):
        """Analyze document metadata completeness and patterns"""
        logger.info("Analyzing document metadata...")
        start = time.time()

        # Track metadata statistics
        metadata_stats = {
            "complete": [],
            "partial": [],
            "missing": [],
            "field_presence": defaultdict(int),
            "avg_score": 0.0
        }

        # Track metadata by document type
        metadata_by_type = defaultdict(list)

        # Analyze each document
        total_score = 0.0
        for doc in self.docs:
            path = doc["path"]
            summary = self.doc_store.get_summary(path, doc)

            # Add to appropriate category based on metadata score
            if summary.metadata_score >= 0.75:
                metadata_stats["complete"].append({
                    "path": path,
                    "title": summary.title,
                    "score": summary.metadata_score,
                    "fields": list(summary.metadata_fields.keys())
                })
            elif summary.metadata_score >= 0.3:
                metadata_stats["partial"].append({
                    "path": path,
                    "title": summary.title,
                    "score": summary.metadata_score,
                    "fields": list(summary.metadata_fields.keys()),
                    "missing_fields": [field for field in EXPECTED_METADATA_FIELDS
                                      if field not in summary.metadata_fields]
                })
            else:
                metadata_stats["missing"].append({
                    "path": path,
                    "title": summary.title,
                    "score": summary.metadata_score,
                    "fields": list(summary.metadata_fields.keys()) if summary.metadata_fields else []
                })

            # Track field presence
            for field in summary.metadata_fields:
                metadata_stats["field_presence"][field] += 1

            # Track by document type
            metadata_by_type[summary.doc_type].append(summary.metadata_score)

            # Add to total score
            total_score += summary.metadata_score

        # Calculate average metadata score
        if self.docs:
            metadata_stats["avg_score"] = total_score / len(self.docs)

        # Calculate average score by document type
        avg_by_type = {}
        for doc_type, scores in metadata_by_type.items():
            avg_by_type[doc_type] = sum(scores) / len(scores)

        # Calculate field presence percentages
        field_presence_pct = {}
        for field, count in metadata_stats["field_presence"].items():
            field_presence_pct[field] = count / len(self.docs) * 100

        duration = time.time() - start
        logger.info(f"Analyzed document metadata in {duration:.2f}s")

        return {
            "complete_count": len(metadata_stats["complete"]),
            "partial_count": len(metadata_stats["partial"]),
            "missing_count": len(metadata_stats["missing"]),
            "complete_docs": metadata_stats["complete"],
            "partial_docs": metadata_stats["partial"],
            "missing_docs": metadata_stats["missing"],
            "field_presence": field_presence_pct,
            "avg_score": metadata_stats["avg_score"],
            "avg_by_type": avg_by_type,
            "analysis_time": duration
        }

    def analyze_doc_types(self):
        """Analyze document type distribution and gaps"""
        logger.info("Analyzing document types...")
        start = time.time()

        # Track document types
        doc_type_counts = Counter()
        doc_type_examples = defaultdict(list)
        doc_type_by_dir = defaultdict(Counter)

        # Analyze each document
        for doc in self.docs:
            path = doc["path"]
            summary = self.doc_store.get_summary(path, doc)
            dir_name = os.path.dirname(path)

            # Count document types
            doc_type_counts[summary.doc_type] += 1

            # Track examples of each type
            if len(doc_type_examples[summary.doc_type]) < 5:  # Limit to 5 examples per type
                doc_type_examples[summary.doc_type].append({
                    "path": path,
                    "title": summary.title,
                    "audience": summary.audience
                })

            # Track document types by directory
            doc_type_by_dir[dir_name][summary.doc_type] += 1

        # Identify directories with missing document types
        dir_type_gaps = {}
        for dir_name, type_counts in doc_type_by_dir.items():
            # Only consider directories with at least 5 documents
            if sum(type_counts.values()) >= 5:
                # Check for missing important document types
                missing_types = []
                for doc_type in ['tutorial', 'reference', 'guide', 'concept']:
                    if doc_type not in type_counts:
                        missing_types.append(doc_type)

                if missing_types:
                    dir_type_gaps[dir_name] = missing_types

        # Calculate document type diversity
        type_diversity = len(doc_type_counts) / (len(DOC_TYPE_PATTERNS) + 1)  # +1 for "index" type

        duration = time.time() - start
        logger.info(f"Analyzed document types in {duration:.2f}s")

        return {
            "type_counts": dict(doc_type_counts),
            "type_examples": dict(doc_type_examples),
            "type_by_dir": {dir_name: dict(counts) for dir_name, counts in doc_type_by_dir.items()},
            "dir_type_gaps": dir_type_gaps,
            "type_diversity": type_diversity,
            "analysis_time": duration
        }

    def analyze_doc_standards(self):
        """Analyze documentation against standards and best practices"""
        logger.info("Analyzing documentation standards...")
        start = time.time()

        # Define standards checks
        standards = {
            "has_title": 0,
            "has_description": 0,
            "has_headings": 0,
            "has_examples": 0,
            "has_links": 0,
            "has_toc": 0,
            "proper_heading_hierarchy": 0
        }

        # Track documents failing standards
        failing_standards = defaultdict(list)

        # Analyze each document
        for doc in self.docs:
            path = doc["path"]
            summary = self.doc_store.get_summary(path, doc)
            content = self.doc_store.get_content(path)

            # Skip tiny documents
            if summary.size < MIN_DOC_BYTES:
                continue

            # Check for title
            has_title = bool(summary.title and summary.title != os.path.basename(path))
            if has_title:
                standards["has_title"] += 1
            else:
                failing_standards["has_title"].append(path)

            # Check for description
            has_description = bool(summary.description)
            if has_description:
                standards["has_description"] += 1
            else:
                failing_standards["has_description"].append(path)

            # Check for headings
            has_headings = len(summary.headings) >= 2
            if has_headings:
                standards["has_headings"] += 1
            else:
                failing_standards["has_headings"].append(path)

            # Check for examples (code blocks)
            has_examples = bool(re.search(r'```', content))
            if has_examples:
                standards["has_examples"] += 1
            else:
                failing_standards["has_examples"].append(path)

            # Check for links
            has_links = bool(re.search(r'\[.+?\]\(.+?\)', content))
            if has_links:
                standards["has_links"] += 1
            else:
                failing_standards["has_links"].append(path)

            # Check for table of contents
            has_toc = bool(re.search(r'(table of contents|toc|contents)', content, re.IGNORECASE))
            if has_toc:
                standards["has_toc"] += 1
            else:
                failing_standards["has_toc"].append(path)

            # Check for proper heading hierarchy
            proper_hierarchy = True
            if summary.headings:
                # Extract heading levels
                heading_levels = [len(h[0]) for h in re.findall(r'^(#{1,6}) (.+)$', content, re.MULTILINE)]

                # Check if levels are sequential (no skipping, e.g., h1 -> h3)
                if heading_levels:
                    for i in range(1, len(heading_levels)):
                        if heading_levels[i] > heading_levels[i-1] + 1:
                            proper_hierarchy = False
                            break
            else:
                proper_hierarchy = False

            if proper_hierarchy:
                standards["proper_heading_hierarchy"] += 1
            else:
                failing_standards["proper_heading_hierarchy"].append(path)

        # Calculate compliance percentages
        total_docs = len(self.docs)
        compliance = {}
        for standard, count in standards.items():
            compliance[standard] = count / total_docs * 100

        # Identify best and worst standards
        sorted_compliance = sorted(compliance.items(), key=lambda x: x[1])
        worst_standards = sorted_compliance[:3]
        best_standards = sorted_compliance[-3:]

        duration = time.time() - start
        logger.info(f"Analyzed documentation standards in {duration:.2f}s")

        return {
            "compliance": compliance,
            "worst_standards": worst_standards,
            "best_standards": best_standards,
            "failing_docs": {standard: paths for standard, paths in failing_standards.items()},
            "analysis_time": duration
        }

    def analyze_readability(self):
        """Analyze document readability if textstat is available"""
        if not HAS_READABILITY:
            return {
                "readability_scores": [],
                "analysis_time": 0
            }

        logger.info("Analyzing document readability...")
        start = time.time()

        readability_scores = []

        for doc in self.docs:
            path = doc["path"]
            content = self.doc_store.get_content(path)

            # Skip tiny or empty documents
            if not content or len(content) < MIN_DOC_BYTES:
                continue

            try:
                # Calculate readability scores
                flesch_score = flesch_reading_ease(content)
                grade_level = text_standard(content, float_output=True)

                # Determine readability level based on thresholds
                if flesch_score >= READABILITY_THRESHOLDS["excellent"]:
                    readability_level = "excellent"
                elif flesch_score >= READABILITY_THRESHOLDS["good"]:
                    readability_level = "good"
                elif flesch_score >= READABILITY_THRESHOLDS["acceptable"]:
                    readability_level = "acceptable"
                elif flesch_score >= READABILITY_THRESHOLDS["difficult"]:
                    readability_level = "difficult"
                else:
                    readability_level = "very_difficult"

                readability_scores.append({
                    "path": path,
                    "title": doc.get("title", os.path.basename(path)),
                    "audience": doc.get("audience", "unknown"),
                    "flesch_score": flesch_score,
                    "grade_level": grade_level,
                    "readability_level": readability_level
                })
            except Exception as e:
                logger.warning(f"Error calculating readability for {path}: {e}")

        # Sort by readability (Flesch score)
        readability_scores.sort(key=lambda x: x["flesch_score"], reverse=True)

        # Group scores by audience
        audience_readability = defaultdict(list)
        for score in readability_scores:
            audience_readability[score["audience"]].append(score["flesch_score"])

        # Calculate average readability by audience
        audience_avg_readability = {}
        for audience, scores in audience_readability.items():
            if scores:
                audience_avg_readability[audience] = sum(scores) / len(scores)

        duration = time.time() - start
        logger.info(f"Analyzed readability for {len(readability_scores)} documents in {duration:.2f}s")

        return {
            "readability_scores": readability_scores,
            "audience_avg_readability": audience_avg_readability,
            "analysis_time": duration
        }

    def generate_recommendations(self):
        """Generate actionable recommendations based on analysis results"""
        logger.info("Generating recommendations...")
        start = time.time()

        # Add debugging
        for i, doc in enumerate(self.docs):
            if "git_history" in doc and "time_since_last_update" in doc.get("git_history", {}):
                val = doc["git_history"]["time_since_last_update"]
                if val is None:
                    logger.debug(f"Document {i}: {doc['path']} has None time_since_last_update")

        # Fix all potential instances of the comparison issue
        def safe_compare(value, threshold):
            """Safely compare a value that might be None"""
            if value is None:
                return False
            try:
                return float(value) > threshold
            except (TypeError, ValueError):
                return False

        recommendations = {
            "high_priority": [],
            "medium_priority": [],
            "low_priority": [],
            "directory_specific": {},
            "audience_specific": {},
            "metadata_recommendations": [],
            "location_recommendations": [],
            "doc_type_recommendations": [],
            "standards_recommendations": []
        }

        # Function to add recommendation with correct formatting
        def add_recommendation(priority, category, title, description, affected_paths=None, action_items=None):
            recommendations[priority].append({
                "category": category,
                "title": title,
                "description": description,
                "affected_paths": affected_paths or [],
                "action_items": action_items or []
            })

        # Function to add specialized recommendation
        def add_specialized_recommendation(rec_type, title, description, affected_items=None, action_items=None):
            recommendations[rec_type].append({
                "title": title,
                "description": description,
                "affected_items": affected_items or [],
                "action_items": action_items or []
            })

        # 1. High priority recommendations

        # Exact duplicates
        exact_duplicates = self.analysis_results.get("exact_duplicates", {}).get("duplicate_groups", [])
        if exact_duplicates:
            add_recommendation(
                "high_priority",
                "duplicates",
                f"Remove {len(exact_duplicates)} exact duplicate documents",
                "These documents have identical content and should be consolidated to avoid confusion.",
                affected_paths=[doc["path"] for group in exact_duplicates for doc in group],
                action_items=[
                    "Choose one canonical version of each document",
                    "Update all references to point to the canonical version",
                    "Add redirects or delete the duplicates"
                ]
            )

        # Severely out-of-sync documentation
        sync_issues = self.analysis_results.get("staleness", {}).get("sync_issues", [])
        severe_sync_issues = [doc for doc in sync_issues if doc["days_behind"] > 180]
        if severe_sync_issues:
            add_recommendation(
                "high_priority",
                "sync",
                f"Update {len(severe_sync_issues)} severely out-of-sync documents",
                "These documents are significantly behind their related code and likely contain outdated information.",
                affected_paths=[doc["path"] for doc in severe_sync_issues],
                action_items=[
                    "Compare each document with current code",
                    "Update technical details to match implementation",
                    "Consider implementing documentation tests or checks in CI"
                ]
            )

        # Documents with obsolete markers
        obsolete_docs = self.analysis_results.get("structure", {}).get("categories", {}).get("obsolete", [])
        if obsolete_docs:
            add_recommendation(
                "high_priority",
                "maintenance",
                f"Review {len(obsolete_docs)} documents with obsolete markers",
                "These documents are explicitly marked as obsolete or deprecated and need review.",
                affected_paths=[doc["path"] for doc in obsolete_docs],
                action_items=[
                    "Determine if each document should be updated or removed",
                    "For truly obsolete content, either remove or clearly mark as archived",
                    "Update relevant content with current information"
                ]
            )

        # Broken links
        broken_links = self.analysis_results.get("links", {}).get("broken_links", [])
        if broken_links:
            add_recommendation(
                "high_priority",
                "links",
                f"Fix {len(broken_links)} broken internal links",
                "These links within documentation point to non-existent files.",
                affected_paths=[link["source"]["path"] for link in broken_links],
                action_items=[
                    "Update links to point to existing documents",
                    "Consider using relative paths consistently",
                    "Implement link checking in CI"
                ]
            )

        # 2. Medium priority recommendations

        # Similar content
        similar_docs = self.analysis_results.get("similar_content", {}).get("similar_pairs", [])
        if similar_docs:
            add_recommendation(
                "medium_priority",
                "duplicates",
                f"Consolidate {len(similar_docs)} pairs of similar documents",
                "These documents have substantially similar content and should be consolidated or cross-referenced.",
                affected_paths=[doc["doc1"]["path"] for doc in similar_docs] + [doc["doc2"]["path"] for doc in similar_docs],
                action_items=[
                    "Review each pair to determine if they should be merged",
                    "For documents serving different audiences, add cross-references",
                    "Create a more comprehensive document combining the unique aspects of each"
                ]
            )

        # Messy directory docs
        messy_docs = self.analysis_results.get("structure", {}).get("categories", {}).get("messy_docs", [])
        if messy_docs:
            add_recommendation(
                "medium_priority",
                "structure",
                f"Reorganize {len(messy_docs)} documents from 'messy' directory",
                "These documents are explicitly stored in the 'messy' directory and need proper organization.",
                affected_paths=[doc["path"] for doc in messy_docs],
                action_items=[
                    "Review each document for its proper category",
                    "Move to appropriate directories based on content",
                    "Update any references to these files"
                ]
            )

        # Documents with TODO markers
        todo_docs = self.analysis_results.get("structure", {}).get("categories", {}).get("todos", [])
        if todo_docs:
            add_recommendation(
                "medium_priority",
                "maintenance",
                f"Address TODOs in {len(todo_docs)} documents",
                "These documents contain TODO markers indicating incomplete content.",
                affected_paths=[doc["path"] for doc in todo_docs],
                action_items=[
                    "Review each TODO and determine required actions",
                    "Complete missing or incomplete sections",
                    "Remove TODO markers once addressed"
                ]
            )

        # Orphaned documents
        orphaned_docs = self.analysis_results.get("links", {}).get("orphaned_docs", [])
        if orphaned_docs:
            add_recommendation(
                "medium_priority",
                "links",
                f"Improve discoverability of {len(orphaned_docs)} orphaned documents",
                "These documents have no incoming links from other documentation, making them hard to discover.",
                affected_paths=[doc["path"] for doc in orphaned_docs],
                action_items=[
                    "Add links from relevant topic pages or indexes",
                    "Consider whether some should be consolidated with other docs",
                    "Update navigation to include orphaned but valuable content"
                ]
            )

        # 3. Low priority recommendations

        # Thin documents (almost empty)
        thin_docs = self.analysis_results.get("structure", {}).get("categories", {}).get("thin_docs", [])
        if thin_docs:
            add_recommendation(
                "low_priority",
                "structure",
                f"Enhance or remove {len(thin_docs)} nearly-empty documents",
                "These documents have minimal content and may be placeholders or stubs.",
                affected_paths=[doc["path"] for doc in thin_docs],
                action_items=[
                    "Expand content for valuable stub documents",
                    "Remove or consolidate truly empty documents",
                    "Consider whether these represent documentation gaps"
                ]
            )

        # Documents without headings
        no_headings_docs = self.analysis_results.get("structure", {}).get("categories", {}).get("no_headings", [])
        if no_headings_docs:
            add_recommendation(
                "low_priority",
                "structure",
                f"Improve structure of {len(no_headings_docs)} documents without headings",
                "These documents lack section headings, making them harder to navigate.",
                affected_paths=[doc["path"] for doc in no_headings_docs],
                action_items=[
                    "Add appropriate section headings to improve readability",
                    "Consider breaking up long documents into logical sections",
                    "Ensure proper markdown hierarchy (h1, h2, h3, etc.)"
                ]
            )

        # Documents with unknown audience
        no_audience_docs = self.analysis_results.get("structure", {}).get("categories", {}).get("no_audience", [])
        if no_audience_docs:
            add_recommendation(
                "low_priority",
                "metadata",
                f"Add audience tags to {len(no_audience_docs)} documents",
                "These documents have no audience specified, making it harder to target content appropriately.",
                affected_paths=[doc["path"] for doc in no_audience_docs],
                action_items=[
                    "Add front matter audience tags to each document",
                    "Consider whether content should be adapted for specific audiences",
                    "Standardize on consistent audience definitions"
                ]
            )

        # Inconsistent naming
        inconsistent_naming = self.analysis_results.get("structure", {}).get("categories", {}).get("inconsistent_naming", [])
        if inconsistent_naming:
            add_recommendation(
                "low_priority",
                "structure",
                f"Standardize naming in {len(inconsistent_naming)} directories",
                "These directories contain files with inconsistent naming patterns.",
                affected_paths=[item["directory"] for item in inconsistent_naming],
                action_items=[
                    "Choose a consistent naming convention (e.g., kebab-case)",
                    "Rename files to follow the chosen convention",
                    "Update any references to renamed files"
                ]
            )

        # 4. Directory-specific recommendations

        # Gather directories
        directories = set()
        for doc in self.docs:
            dir_name = os.path.dirname(doc["path"])
            directories.add(dir_name)

        # Analyze each directory
        for directory in directories:
            # Skip top-level directory
            if not directory:
                continue

            # Get docs in this directory
            dir_docs = [doc for doc in self.docs if os.path.dirname(doc["path"]) == directory]

            # Skip if too few documents
            if len(dir_docs) < 3:
                continue

            # Check for specific issues in this directory
            dir_issues = []

            # Check for inconsistent audiences in directory
            audiences = set(doc.get("audience", "unknown") for doc in dir_docs)
            if len(audiences) > 1 and "unknown" not in audiences:
                dir_issues.append({
                    "type": "mixed_audiences",
                    "description": f"Mixed audiences: {', '.join(audiences)}",
                    "affected_paths": [doc["path"] for doc in dir_docs],
                    "action_items": [
                        "Consider reorganizing by audience",
                        "Create audience-specific versions if needed",
                        "Ensure metadata clearly indicates intended audience"
                    ]
                })

            # Check for staleness in directory
            stale_dir_docs = [
                doc["path"] for doc in dir_docs
                if safe_compare(doc.get("git_history", {}).get("time_since_last_update"), 180)
            ]
            if stale_dir_docs and len(stale_dir_docs) > len(dir_docs) // 2:
                dir_issues.append({
                    "type": "mostly_stale",
                    "description": f"{len(stale_dir_docs)}/{len(dir_docs)} documents are stale",
                    "affected_paths": stale_dir_docs,
                    "action_items": [
                        "Conduct a comprehensive review of this directory",
                        "Update or archive outdated content",
                        "Consider whether this area needs expanded documentation"
                    ]
                })

            # Add directory recommendations if issues found
            if dir_issues:
                recommendations["directory_specific"][directory] = dir_issues

        # 5. Audience-specific recommendations
        audiences = self.analysis_results.get("audience", {}).get("audience_counts", {})

        for audience, _ in audiences.items():
            # Skip unknown audience
            if audience == "unknown":
                continue

            # Count docs for this audience (used in calculations below)
            audience_doc_count = sum(1 for doc in self.docs if doc.get("audience") == audience)

            # Check for audience-specific issues
            audience_issues = []

            # Check readability for audience
            if "readability" in self.analysis_results:
                avg_readability = self.analysis_results["readability"].get("audience_avg_readability", {}).get(audience)
                if avg_readability is not None:
                    if audience == "ai" and avg_readability < READABILITY_THRESHOLDS["acceptable"]:
                        audience_issues.append({
                            "type": "readability",
                            "description": f"AI documentation has low readability (Flesch score: {avg_readability:.1f})",
                            "action_items": [
                                "Simplify language in AI-targeted docs",
                                "Use more straightforward sentence structure",
                                "Break down complex concepts into clearer explanations"
                            ]
                        })
                    elif audience == "human" and avg_readability < READABILITY_THRESHOLDS["good"]:
                        audience_issues.append({
                            "type": "readability",
                            "description": f"Human documentation has mediocre readability (Flesch score: {avg_readability:.1f})",
                            "action_items": [
                                "Improve clarity with simpler language",
                                "Use more examples and illustrations",
                                "Break down complex sentences into shorter ones"
                            ]
                        })

            # Check link patterns for audience
            audience_orphans = [
                doc["path"] for doc in self.analysis_results.get("links", {}).get("orphaned_docs", [])
                if next((d for d in self.docs if d["path"] == doc["path"]), {}).get("audience") == audience
            ]
            if audience_orphans and len(audience_orphans) > audience_doc_count // 3:
                audience_issues.append({
                    "type": "discoverability",
                    "description": f"Many {audience} docs are orphaned ({len(audience_orphans)}/{audience_doc_count})",
                    "action_items": [
                        f"Create an index page for {audience} documentation",
                        "Improve navigation between related documents",
                        "Review information architecture for this audience"
                    ]
                })

            # Add audience recommendations if issues found
            if audience_issues:
                recommendations["audience_specific"][audience] = audience_issues

        # Add metadata recommendations
        metadata_results = self.analysis_results.get("metadata", {})
        if metadata_results:
            # Check for missing metadata
            missing_count = metadata_results.get("missing_count", 0)
            if missing_count > 0:
                add_specialized_recommendation(
                    "metadata_recommendations",
                    f"Add metadata to {missing_count} documents with missing metadata",
                    "These documents have little to no metadata, making them harder to categorize and find.",
                    affected_items=[doc["path"] for doc in metadata_results.get("missing_docs", [])],
                    action_items=[
                        "Add YAML frontmatter with essential metadata fields",
                        "Include at minimum: title, description, audience, and tags",
                        "Consider creating a metadata template for consistency"
                    ]
                )

            # Check for most commonly missing fields
            field_presence = metadata_results.get("field_presence", {})
            if field_presence:
                missing_fields = []
                for field in EXPECTED_METADATA_FIELDS:
                    presence = field_presence.get(field, 0)
                    if presence < 50:  # Less than 50% of docs have this field
                        missing_fields.append((field, presence))

                if missing_fields:
                    missing_fields.sort(key=lambda x: x[1])  # Sort by presence (ascending)
                    field_list = ", ".join([f"{field} ({presence:.1f}%)" for field, presence in missing_fields[:3]])

                    add_specialized_recommendation(
                        "metadata_recommendations",
                        f"Add commonly missing metadata fields: {field_list}",
                        "These metadata fields are frequently missing across the documentation.",
                        action_items=[
                            f"Add {field} field to documents" for field, _ in missing_fields[:3]
                        ] + ["Create a metadata style guide for documentation contributors"]
                    )

        # Add documentation location recommendations
        location_results = self.analysis_results.get("doc_location", {})
        if location_results:
            # Check for missing index documents
            missing_indexes = location_results.get("missing_indexes", [])
            if missing_indexes:
                add_specialized_recommendation(
                    "location_recommendations",
                    f"Create index documents for {len(missing_indexes)} directories",
                    "These directories lack index documents (README.md or index.md), making navigation difficult.",
                    affected_items=missing_indexes,
                    action_items=[
                        "Create README.md or index.md files for each directory",
                        "Include an overview of the directory's purpose",
                        "Add a table of contents linking to contained documents"
                    ]
                )

            # Check for organization patterns
            org_patterns = location_results.get("org_patterns", {})
            detected_patterns = list(org_patterns.keys())

            if len(detected_patterns) > 1:
                # Multiple organization patterns detected
                add_specialized_recommendation(
                    "location_recommendations",
                    f"Standardize documentation organization patterns",
                    f"Multiple organization patterns detected: {', '.join(detected_patterns)}",
                    action_items=[
                        "Choose a single organization pattern for consistency",
                        "Reorganize documentation to follow the chosen pattern",
                        "Document the organization pattern in a style guide"
                    ]
                )
            elif not detected_patterns:
                # No clear organization pattern
                add_specialized_recommendation(
                    "location_recommendations",
                    "Implement a clear documentation organization pattern",
                    "No clear organization pattern was detected in the documentation.",
                    action_items=[
                        "Choose an organization pattern (by topic, audience, or version)",
                        "Reorganize documentation to follow the chosen pattern",
                        "Create a documentation map or guide explaining the structure"
                    ]
                )

        # Add document type recommendations
        doc_type_results = self.analysis_results.get("doc_types", {})
        if doc_type_results:
            # Check for document type gaps
            dir_type_gaps = doc_type_results.get("dir_type_gaps", {})
            if dir_type_gaps:
                # Find the most common missing type
                missing_type_counts = Counter()
                for dir_name, missing_types in dir_type_gaps.items():
                    for doc_type in missing_types:
                        missing_type_counts[doc_type] += 1

                most_missing = missing_type_counts.most_common(1)[0][0]
                dirs_missing_type = [dir_name for dir_name, types in dir_type_gaps.items() if most_missing in types]

                add_specialized_recommendation(
                    "doc_type_recommendations",
                    f"Add missing '{most_missing}' documentation to {len(dirs_missing_type)} directories",
                    f"These directories lack {most_missing} documentation, which is important for a complete documentation set.",
                    affected_items=dirs_missing_type,
                    action_items=[
                        f"Create {most_missing} documentation for each affected directory",
                        f"Ensure {most_missing} docs follow a consistent format",
                        "Link new documents from existing documentation"
                    ]
                )

            # Check for unknown document types
            type_counts = doc_type_results.get("type_counts", {})
            unknown_count = type_counts.get("unknown", 0)
            if unknown_count > len(self.docs) * 0.3:  # More than 30% unknown
                add_specialized_recommendation(
                    "doc_type_recommendations",
                    f"Classify {unknown_count} documents with unknown type",
                    "A significant portion of documents couldn't be classified by type, making it harder to ensure complete documentation.",
                    action_items=[
                        "Add clear type indicators in document titles or metadata",
                        "Organize documents into type-specific directories",
                        "Create a document type guide for contributors"
                    ]
                )

        # Add documentation standards recommendations
        standards_results = self.analysis_results.get("doc_standards", {})
        if standards_results:
            # Check for worst standards compliance
            worst_standards = standards_results.get("worst_standards", [])
            if worst_standards:
                standards_to_improve = [f"{standard} ({compliance:.1f}%)" for standard, compliance in worst_standards]

                add_specialized_recommendation(
                    "standards_recommendations",
                    f"Improve compliance with documentation standards: {', '.join(standards_to_improve)}",
                    "These documentation standards have the lowest compliance rates.",
                    action_items=[
                        f"Add {standard} to non-compliant documents" for standard, _ in worst_standards
                    ] + ["Create a documentation standards checklist for contributors"]
                )

            # Check for specific standards with very low compliance
            compliance = standards_results.get("compliance", {})
            for standard, rate in compliance.items():
                if rate < 30:  # Less than 30% compliance
                    failing_docs = standards_results.get("failing_docs", {}).get(standard, [])

                    add_specialized_recommendation(
                        "standards_recommendations",
                        f"Critical standard issue: {standard} ({rate:.1f}% compliance)",
                        f"This documentation standard has extremely low compliance, affecting {len(failing_docs)} documents.",
                        affected_items=failing_docs,
                        action_items=[
                            f"Add {standard} to all affected documents",
                            "Create templates that include this standard",
                            "Add this standard to documentation review checklist"
                        ]
                    )

        duration = time.time() - start
        logger.info(f"Generated recommendations in {duration:.2f}s")

        return recommendations

    def create_visualization(self, output_dir="./visualizations"):
        """Create visualizations of the documentation structure and relationships"""
        if not HAS_VISUALIZATION:
            logger.warning("Visualizations not available: Missing matplotlib, networkx or scikit-learn")
            return False

        logger.info("Generating visualizations...")

        # Create output directory if needed
        os.makedirs(output_dir, exist_ok=True)

        # 1. Create document relationship graph
        self._create_document_graph(output_dir)

        # 2. Create audience distribution chart
        self._create_audience_chart(output_dir)

        # 3. Create staleness heatmap
        self._create_staleness_heatmap(output_dir)

        # 4. Create cluster visualization (if available)
        if "document_clusters" in self.analysis_results:
            self._create_cluster_visualization(output_dir)

        return True

    def _create_document_graph(self, output_dir):
        """Create a graph visualization of document relationships"""
        logger.info("Creating document relationship graph...")

        # Create a new graph
        G = nx.DiGraph()

        # Add nodes (documents)
        for doc in self.docs:
            path = doc["path"]
            title = doc.get("title", os.path.basename(path))
            audience = doc.get("audience", "unknown")

            # Add node with attributes
            G.add_node(path, title=title, audience=audience)

        # Add edges (links between documents)
        for doc in self.docs:
            source_path = doc["path"]
            for link in doc.get("internal_links", []):
                # Normalize the link path (simplified)
                target_path = link

                # Add edge if target exists in the graph
                if target_path in G:
                    G.add_edge(source_path, target_path)

        # Create figure
        plt.figure(figsize=(12, 10))

        # Set node colors based on audience
        audience_colors = {
            "human": "skyblue",
            "ai": "lightgreen",
            "unknown": "lightgray"
        }

        node_colors = [audience_colors.get(G.nodes[n]["audience"], "lightgray") for n in G.nodes]

        # Create positions (layout)
        pos = nx.spring_layout(G, k=0.3)

        # Draw the graph
        nx.draw_networkx_nodes(G, pos, node_color=node_colors, alpha=0.8, node_size=100)
        nx.draw_networkx_edges(G, pos, edge_color="gray", alpha=0.5, arrows=True, arrowsize=10)

        # Draw labels for important nodes only (hubs and authorities)
        important_nodes = set()

        # Add hubs (nodes with many outgoing edges)
        for node, out_degree in sorted(G.out_degree, key=lambda x: x[1], reverse=True)[:10]:
            if out_degree > 1:
                important_nodes.add(node)

        # Add authorities (nodes with many incoming edges)
        for node, in_degree in sorted(G.in_degree, key=lambda x: x[1], reverse=True)[:10]:
            if in_degree > 1:
                important_nodes.add(node)

        # Create a subset of labels for important nodes
        labels = {node: G.nodes[node]["title"] for node in important_nodes}
        nx.draw_networkx_labels(G, pos, labels=labels, font_size=8)

        # Add legend
        legend_elements = [
            plt.Line2D([0], [0], marker='o', color='w', markerfacecolor=color, label=audience, markersize=10)
            for audience, color in audience_colors.items()
        ]
        plt.legend(handles=legend_elements, title="Audience")

        # Set title and remove axes
        plt.title("Document Relationship Graph")
        plt.axis("off")

        # Save figure
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, "document_graph.png"), dpi=300)
        plt.close()

    def _create_audience_chart(self, output_dir):
        """Create a chart showing audience distribution"""
        logger.info("Creating audience distribution chart...")

        # Get audience data
        audience_counts = self.analysis_results.get("audience", {}).get("audience_counts", {})

        if not audience_counts:
            return

        # Create figure
        plt.figure(figsize=(10, 6))

        # Create pie chart
        labels = list(audience_counts.keys())
        sizes = list(audience_counts.values())
        colors = ['skyblue', 'lightgreen', 'lightgray', 'gold', 'lightcoral']

        plt.pie(sizes, labels=labels, colors=colors[:len(labels)], autopct='%1.1f%%', startangle=90)
        plt.axis('equal')
        plt.title('Documentation by Audience')

        # Save figure
        plt.savefig(os.path.join(output_dir, "audience_distribution.png"), dpi=300)
        plt.close()

        # Create directory-specific audience charts
        directory_audience = self.analysis_results.get("audience", {}).get("directory_audience", {})

        if directory_audience:
            # Filter to directories with multiple documents
            significant_dirs = {dir_name: counts for dir_name, counts in directory_audience.items()
                             if sum(counts.values()) >= 5}

            if significant_dirs:
                plt.figure(figsize=(12, 8))

                # Determine number of subplots
                n_dirs = len(significant_dirs)
                n_cols = min(3, n_dirs)
                n_rows = (n_dirs + n_cols - 1) // n_cols

                # Create subplots
                for i, (dir_name, counts) in enumerate(significant_dirs.items(), 1):
                    plt.subplot(n_rows, n_cols, i)

                    labels = list(counts.keys())
                    sizes = list(counts.values())

                    plt.pie(sizes, labels=labels, colors=colors[:len(labels)], autopct='%1.1f%%', startangle=90)
                    plt.axis('equal')
                    plt.title(dir_name, fontsize=10)

                plt.tight_layout()
                plt.savefig(os.path.join(output_dir, "directory_audience.png"), dpi=300)
                plt.close()

    def _create_staleness_heatmap(self, output_dir):
        """Create a heatmap showing document staleness"""
        logger.info("Creating document staleness heatmap...")

        # Collect staleness data by directory
        dir_staleness = defaultdict(list)

        for doc in self.docs:
            path = doc["path"]
            dir_name = os.path.dirname(path)
            days_stale = doc.get("git_history", {}).get("time_since_last_update", 0)

            if days_stale is not None:
                dir_staleness[dir_name].append(days_stale)

        # Filter to directories with multiple documents
        significant_dirs = {dir_name: staleness for dir_name, staleness in dir_staleness.items()
                         if len(staleness) >= 3}

        if not significant_dirs:
            return

        # Calculate average staleness by directory
        avg_staleness = {dir_name: sum(days)/len(days) for dir_name, days in significant_dirs.items()}

        # Sort directories by average staleness
        sorted_dirs = sorted(avg_staleness.items(), key=lambda x: x[1], reverse=True)

        # Create figure
        plt.figure(figsize=(12, 8))

        # Create bar chart
        labels = [dir_name for dir_name, _ in sorted_dirs]
        values = [staleness for _, staleness in sorted_dirs]

        # Shorten directory names for display
        display_labels = [dir_name[-20:] if len(dir_name) > 20 else dir_name for dir_name in labels]

        # Create colormap based on staleness
        colors = plt.cm.YlOrRd(np.array(values) / max(values))

        # Create bar chart
        bars = plt.barh(display_labels, values, color=colors)

        # Add value labels
        for bar, value in zip(bars, values):
            plt.text(bar.get_width() + 5, bar.get_y() + bar.get_height()/2,
                    f"{int(value)} days", va='center')

        # Set labels and title
        plt.xlabel('Average Days Since Last Update')
        plt.title('Documentation Staleness by Directory')

        # Invert y-axis to have most stale at top
        plt.gca().invert_yaxis()

        # Save figure
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, "staleness_heatmap.png"), dpi=300)
        plt.close()

    def _create_cluster_visualization(self, output_dir):
        """Create a visualization of document clusters"""
        logger.info("Creating document cluster visualization...")

        # Get cluster data
        clusters = self.analysis_results.get("document_clusters", {}).get("clusters", [])

        if not clusters:
            return

        # Create figure
        plt.figure(figsize=(12, 8))

        # Create scatter plot
        x = []
        y = []
        colors = []
        labels = []

        # Generate a color map
        cmap = plt.cm.get_cmap('tab10', len(clusters))

        for i, cluster in enumerate(clusters):
            # For each document in the cluster
            for doc in cluster["documents"]:
                # For visualization purposes, use random positions (these will be adjusted by layout algorithm)
                x.append(np.random.random())
                y.append(np.random.random())
                colors.append(cmap(i))
                labels.append(os.path.basename(doc["path"]))

        # Create scatter plot
        plt.scatter(x, y, c=colors, alpha=0.7)

        # Add labels for a sample of points
        if len(x) > 20:
            # Show only some labels to avoid overcrowding
            sample_indices = np.random.choice(range(len(x)), size=20, replace=False)
            for i in sample_indices:
                plt.annotate(labels[i], (x[i], y[i]), fontsize=8)
        else:
            # Show all labels if there are few points
            for i in range(len(x)):
                plt.annotate(labels[i], (x[i], y[i]), fontsize=8)

        # Set title
        plt.title(f"Document Clusters ({len(clusters)} clusters)")

        # Remove axes
        plt.axis("off")

        # Save figure
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, "document_clusters.png"), dpi=300)
        plt.close()

    def generate_report(self, output_format="markdown"):
        """Generate a comprehensive report of the analysis"""
        if not self.analysis_results:
            if not self.analyze():
                return "Error: Analysis failed"

        # Generate different formats
        if output_format == "markdown":
            return self._generate_markdown_report()
        elif output_format == "json":
            return self._generate_json_report()
        else:
            return "Error: Unsupported output format"

    def _generate_markdown_report(self):
        """Generate a markdown report of the analysis"""
        logger.info("Generating markdown report...")

        # Get analysis results
        results = self.analysis_results

        # Build report
        report = [
            "# Goali Documentation Analysis Report",
            f"\nAnalysis Date: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"Total Documents Analyzed: {len(self.docs)}",
            f"Analysis Time: {results.get('meta', {}).get('analysis_time', 0):.2f} seconds",

            "\n## Executive Summary",
        ]

        # Add summary metrics
        exact_duplicates = results.get("exact_duplicates", {}).get("count", 0)
        similar_docs = results.get("similar_content", {}).get("count", 0)
        conceptual_duplicates = results.get("conceptual_similarity", {}).get("count", 0)
        stale_docs = len(results.get("staleness", {}).get("stale_docs", []))
        sync_issues = len(results.get("staleness", {}).get("sync_issues", []))
        messy_docs = len(results.get("structure", {}).get("categories", {}).get("messy_docs", []))
        no_audience = len(results.get("structure", {}).get("categories", {}).get("no_audience", []))
        todos = len(results.get("structure", {}).get("categories", {}).get("todos", []))
        broken_links = len(results.get("links", {}).get("broken_links", []))
        orphaned_docs = len(results.get("links", {}).get("orphaned_docs", []))

        # Get metrics from new analyses
        missing_indexes = len(results.get("doc_location", {}).get("missing_indexes", []))
        org_patterns = len(results.get("doc_location", {}).get("org_patterns", {}))
        missing_metadata = results.get("metadata", {}).get("missing_count", 0)
        partial_metadata = results.get("metadata", {}).get("partial_count", 0)
        unknown_types = results.get("doc_types", {}).get("type_counts", {}).get("unknown", 0)
        dir_type_gaps = len(results.get("doc_types", {}).get("dir_type_gaps", {}))

        # Get standards compliance
        compliance = results.get("doc_standards", {}).get("compliance", {})
        avg_compliance = sum(compliance.values()) / len(compliance) if compliance else 0

        report.extend([
            f"- **Duplicates**: {exact_duplicates} exact, {similar_docs} similar, {conceptual_duplicates} conceptual",
            f"- **Staleness**: {stale_docs} stale documents, {sync_issues} out of sync with code",
            f"- **Structure Issues**: {messy_docs} in messy directory, {no_audience} with unknown audience, {todos} with TODOs",
            f"- **Link Issues**: {broken_links} broken links, {orphaned_docs} orphaned documents",
            f"- **Location Issues**: {missing_indexes} directories without index documents, {org_patterns} organization patterns detected",
            f"- **Metadata Issues**: {missing_metadata} documents with missing metadata, {partial_metadata} with partial metadata",
            f"- **Document Types**: {unknown_types} documents with unknown type, {dir_type_gaps} directories missing important document types",
            f"- **Standards Compliance**: {avg_compliance:.1f}% average compliance with documentation standards",
        ])

        # Add audience distribution
        audience_counts = results.get("audience", {}).get("audience_counts", {})
        if audience_counts:
            report.append(f"- **Audience Distribution**: " + ", ".join([f"{audience}: {count}" for audience, count in audience_counts.items()]))

        # Add recommendations summary
        recommendations = results.get("recommendations", {})
        high_priority = len(recommendations.get("high_priority", []))
        medium_priority = len(recommendations.get("medium_priority", []))
        low_priority = len(recommendations.get("low_priority", []))
        metadata_recs = len(recommendations.get("metadata_recommendations", []))
        location_recs = len(recommendations.get("location_recommendations", []))
        doc_type_recs = len(recommendations.get("doc_type_recommendations", []))
        standards_recs = len(recommendations.get("standards_recommendations", []))

        report.extend([
            f"- **Recommendations**: {high_priority} high priority, {medium_priority} medium priority, {low_priority} low priority",
            f"- **Specialized Recommendations**: {metadata_recs} metadata, {location_recs} location, {doc_type_recs} document type, {standards_recs} standards",
            "\n## Key Findings and Recommendations"
        ])

        # Add high priority recommendations
        if recommendations.get("high_priority"):
            report.append("\n### High Priority Recommendations")

            for rec in recommendations["high_priority"]:
                report.append(f"\n#### {rec['title']}")
                report.append(f"{rec['description']}")

                if rec.get("action_items"):
                    report.append("\n**Action Items:**")
                    for item in rec["action_items"]:
                        report.append(f"- {item}")

                if rec.get("affected_paths"):
                    report.append("\n**Affected Documents:**")
                    for path in rec["affected_paths"]:
                        report.append(f"- {path}")

        # Add medium priority recommendations
        if recommendations.get("medium_priority"):
            report.append("\n### Medium Priority Recommendations")

            for rec in recommendations["medium_priority"]:
                report.append(f"\n#### {rec['title']}")
                report.append(f"{rec['description']}")

                if rec.get("action_items"):
                    report.append("\n**Action Items:**")
                    for item in rec["action_items"]:
                        report.append(f"- {item}")

        # Add specialized recommendations
        specialized_sections = [
            ("metadata_recommendations", "Metadata Recommendations"),
            ("location_recommendations", "Documentation Location Recommendations"),
            ("doc_type_recommendations", "Document Type Recommendations"),
            ("standards_recommendations", "Documentation Standards Recommendations")
        ]

        for rec_key, section_title in specialized_sections:
            if recommendations.get(rec_key):
                report.append(f"\n### {section_title}")

                for rec in recommendations[rec_key]:
                    report.append(f"\n#### {rec['title']}")
                    report.append(f"{rec['description']}")

                    if rec.get("affected_items"):
                        report.append("\n**Affected Items:**")
                        for item in rec["affected_items"]:
                            report.append(f"- {item}")

                    if rec.get("action_items"):
                        for item in rec["action_items"]:
                            report.append(f"- {item}")

        # Add detailed analysis sections
        report.append("\n## Detailed Analysis")

        # Add documentation location analysis
        doc_location = results.get("doc_location", {})
        if doc_location:
            report.append("\n### Documentation Location Analysis")

            # Add organization patterns
            org_patterns = doc_location.get("org_patterns", {})
            if org_patterns:
                report.append("\n#### Documentation Organization Patterns")
                for pattern_name, pattern_info in org_patterns.items():
                    report.append(f"- **{pattern_name}**: {pattern_info['count']} documents")
                    if pattern_info.get("examples"):
                        report.append("  Examples:")
                        for example in pattern_info["examples"]:
                            report.append(f"  - {example}")

            # Add missing indexes
            missing_indexes = doc_location.get("missing_indexes", [])
            if missing_indexes:
                report.append(f"\n#### Directories Missing Index Documents ({len(missing_indexes)})")
                for dir_name in missing_indexes:
                    report.append(f"- {dir_name}")

        # Add metadata analysis
        metadata = results.get("metadata", {})
        if metadata:
            report.append("\n### Metadata Analysis")

            # Add metadata completeness
            report.append("\n#### Metadata Completeness")
            report.append(f"- **Complete metadata**: {metadata.get('complete_count', 0)} documents")
            report.append(f"- **Partial metadata**: {metadata.get('partial_count', 0)} documents")
            report.append(f"- **Missing metadata**: {metadata.get('missing_count', 0)} documents")
            report.append(f"- **Average metadata score**: {metadata.get('avg_score', 0):.2f} (0-1 scale)")

            # Add field presence
            field_presence = metadata.get("field_presence", {})
            if field_presence:
                report.append("\n#### Metadata Field Presence")
                sorted_fields = sorted(field_presence.items(), key=lambda x: x[1], reverse=True)
                for field, presence in sorted_fields[:10]:
                    report.append(f"- **{field}**: {presence:.1f}%")

        # Add document type analysis
        doc_types = results.get("doc_types", {})
        if doc_types:
            report.append("\n### Document Type Analysis")

            # Add document type distribution
            type_counts = doc_types.get("type_counts", {})
            if type_counts:
                report.append("\n#### Document Type Distribution")
                sorted_types = sorted(type_counts.items(), key=lambda x: x[1], reverse=True)
                for doc_type, count in sorted_types:
                    report.append(f"- **{doc_type}**: {count} documents")

            # Add document type gaps
            dir_type_gaps = doc_types.get("dir_type_gaps", {})
            if dir_type_gaps:
                report.append(f"\n#### Directories with Missing Document Types ({len(dir_type_gaps)})")
                for dir_name, missing_types in dir_type_gaps.items():
                    report.append(f"- **{dir_name}**: Missing {', '.join(missing_types)}")

        # Add documentation standards analysis
        doc_standards = results.get("doc_standards", {})
        if doc_standards:
            report.append("\n### Documentation Standards Analysis")

            # Add standards compliance
            compliance = doc_standards.get("compliance", {})
            if compliance:
                report.append("\n#### Standards Compliance")
                sorted_compliance = sorted(compliance.items(), key=lambda x: x[1], reverse=True)
                for standard, rate in sorted_compliance:
                    report.append(f"- **{standard}**: {rate:.1f}% compliance")

            # Add worst standards
            worst_standards = doc_standards.get("worst_standards", [])
            if worst_standards:
                report.append("\n#### Areas for Improvement")
                for standard, rate in worst_standards:
                    report.append(f"- **{standard}**: {rate:.1f}% compliance")
                    failing_docs = doc_standards.get("failing_docs", {}).get(standard, [])
                    if failing_docs:
                        report.append(f"  - {len(failing_docs)} documents need improvement")

        # Add duplication analysis
        report.append("\n### Duplication Analysis")

        if exact_duplicates:
            report.append(f"\n#### Exact Duplicates ({exact_duplicates} groups)")

            for i, group in enumerate(results.get("exact_duplicates", {}).get("duplicate_groups", [])):
                report.append(f"\n**Duplicate Group {i+1}:**")
                for doc in group:
                    report.append(f"- {doc['title']} ({doc['path']})")

        if similar_docs:
            report.append(f"\n#### Similar Content ({similar_docs} pairs)")

            for i, pair in enumerate(results.get("similar_content", {}).get("similar_pairs", [])):
                report.append(f"\n**Similar Pair {i+1} ({pair['similarity']:.1%} similar):**")
                report.append(f"- {pair['doc1']['title']} ({pair['doc1']['path']})")
                report.append(f"- {pair['doc2']['title']} ({pair['doc2']['path']})")

        # Add staleness analysis
        report.append("\n### Staleness Analysis")

        if stale_docs:
            report.append(f"\n#### Stale Documents ({stale_docs} documents)")

            stale_docs_list = results.get("staleness", {}).get("stale_docs", [])
            for i, doc in enumerate(stale_docs_list):
                report.append(f"- {doc['title']} ({doc['path']}) - {doc['days_stale']} days since update")

        if sync_issues:
            report.append(f"\n#### Code Sync Issues ({sync_issues} documents)")

            sync_issues_list = results.get("staleness", {}).get("sync_issues", [])
            for i, doc in enumerate(sync_issues_list):
                report.append(f"- {doc['title']} ({doc['path']}) - {doc['days_behind']} days behind code")
                if doc.get("related_code"):
                    report.append(f"  Related code: {', '.join(doc['related_code'])}")

        # Add link analysis
        report.append("\n### Link Analysis")

        if broken_links:
            report.append(f"\n#### Broken Links ({broken_links} links)")

            broken_links_list = results.get("links", {}).get("broken_links", [])
            for i, link in enumerate(broken_links_list):
                report.append(f"- {link['source']['title']} ({link['source']['path']}) → {link['target']}")

        # Add audience analysis
        report.append("\n### Audience Analysis")

        if audience_counts:
            report.append(f"\n#### Documentation by Audience")

            for audience, count in audience_counts.items():
                report.append(f"- {audience}: {count} documents ({count/len(self.docs)*100:.1f}%)")

        # Add audience-specific recommendations
        audience_specific = recommendations.get("audience_specific", {})
        if audience_specific:
            report.append("\n#### Audience-Specific Recommendations")

            for audience, issues in audience_specific.items():
                report.append(f"\n**For {audience} documentation:**")

                for issue in issues:
                    report.append(f"- {issue['description']}")
                    if issue.get("action_items"):
                        for item in issue["action_items"]:
                            report.append(f"  - {item}")

        # Add implementation plan
        report.append("\n## Implementation Plan")
        report.append("""
1. **Week 1: High Priority Issues**
   - Remove exact duplicates
   - Fix broken links
   - Update severely out-of-sync documentation
   - Address documents with obsolete markers
   - Create missing index documents for key directories

2. **Week 2: Medium Priority Issues**
   - Consolidate similar documents
   - Reorganize documents from the messy directory
   - Address TODOs
   - Improve discoverability of orphaned documents
   - Standardize documentation organization patterns

3. **Week 3: Metadata and Standards**
   - Add missing metadata to documents
   - Create metadata templates and style guide
   - Improve compliance with documentation standards
   - Add audience tags to documents
   - Standardize naming conventions

4. **Week 4: Document Types and Structure**
   - Add missing document types to directories
   - Classify documents with unknown types
   - Improve structure of documents without headings
   - Enhance or remove thin documents
   - Create documentation type templates

5. **Week 5: Continuous Improvement**
   - Implement documentation health checks in CI
   - Create directory-specific indexes
   - Document audience-specific guidelines
   - Set up a regular documentation review process
   - Create documentation standards checklist
""")

        return "\n".join(report)

    def _generate_json_report(self):
        """Generate a JSON report for programmatic use"""
        if not self.analysis_results:
            if not self.analyze():
                return {"error": "Analysis failed"}

        return self.analysis_results

def main():
    """Main entry point for the script"""
    # Set global worker limit
    global MAX_WORKERS

    parser = argparse.ArgumentParser(description='Improved documentation analyzer')
    parser.add_argument('--inventory', default='doc_index.json', help='Path to document inventory JSON file')
    parser.add_argument('--output', default='analysis_report', help='Output file base name (without extension)')
    parser.add_argument('--format', choices=['md', 'json'], default='md', help='Output format: markdown or JSON')
    parser.add_argument('--visualize', action='store_true', help='Generate visualizations')
    parser.add_argument('--visual-dir', default='./visualizations', help='Directory for visualizations')
    parser.add_argument('--openai-key', help='OpenAI API key for embedding-based similarity (optional)')
    parser.add_argument('--stale-days', type=int, default=180, help='Days threshold for stale documents')
    parser.add_argument('--sync-days', type=int, default=90, help='Days threshold for out-of-sync documents')
    parser.add_argument('--workers', type=int, default=MAX_WORKERS, help='Maximum number of worker processes/threads')
    parser.add_argument('--debug', action='store_true', help='Enable debug logging')
    parser.add_argument('--skip-metadata', action='store_true', help='Skip metadata analysis')
    parser.add_argument('--skip-location', action='store_true', help='Skip documentation location analysis')
    parser.add_argument('--skip-doc-types', action='store_true', help='Skip document type analysis')
    parser.add_argument('--skip-standards', action='store_true', help='Skip documentation standards analysis')
    # Create a formatted example string from DEFAULT_EXCLUDE_DIRS
    exclude_examples = ' '.join(DEFAULT_EXCLUDE_DIRS[:3])  # Use first 3 examples
    parser.add_argument('--exclude-dirs', nargs='+', default=[],
                      help=f'Directories to exclude from analysis (e.g., {exclude_examples})')
    parser.add_argument('--use-default-excludes', action='store_true',
                      help='Use predefined list of common directories to exclude')
    # Enhanced topic analysis options
    parser.add_argument('--use-ollama', action='store_true',
                      help='Enable Ollama integration for enhanced topic analysis')
    parser.add_argument('--ollama-host', default='http://localhost:11434',
                      help='Ollama server host URL')
    parser.add_argument('--ollama-model', default='mistral',
                      help='Ollama model to use for topic analysis')

    args = parser.parse_args()

    # Configure logging level
    if args.debug:
        logger.setLevel(logging.DEBUG)

    MAX_WORKERS = args.workers

    # Configure exclude directories
    exclude_dirs = args.exclude_dirs.copy()
    exclude_dirs.extend(DEFAULT_EXCLUDE_DIRS)
    logger.info(f"Using default exclude directories: {', '.join(DEFAULT_EXCLUDE_DIRS)}")

    # Configure analyzer options
    options = {
        'stale_days': args.stale_days,
        'sync_days': args.sync_days,
        'exclude_dirs': exclude_dirs,
        'workers': args.workers,
        'skip_metadata': args.skip_metadata,
        'skip_location': args.skip_location,
        'skip_doc_types': args.skip_doc_types,
        'skip_standards': args.skip_standards,
        # Enhanced topic analysis options
        'use_ollama': args.use_ollama,
        'ollama_host': args.ollama_host,
        'ollama_model': args.ollama_model
    }

    if args.openai_key:
        options['openai_api_key'] = args.openai_key

    # Initialize analyzer
    analyzer = ImprovedAnalyzer(args.inventory, options)

    # Run analysis
    analyzer.analyze()

    # Generate report
    if args.format == 'md':
        report = analyzer.generate_report(output_format="markdown")
        output_file = f"{args.output}.md"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(report)
    else:
        report = analyzer.generate_report(output_format="json")
        output_file = f"{args.output}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2)

    logger.info(f"Report written to {output_file}")

    # Generate visualizations if requested
    if args.visualize:
        analyzer.create_visualization(args.visual_dir)

if __name__ == "__main__":
    main()
