# This file makes the 'core' directory a Python sub-package.

# Expose key components for easier import
from .constants import (
    MAX_WORKERS, MIN_DOC_BYTES, DEFAULT_EMBEDDING_MODEL,
    SIMILAR_DOCS_THRESHOLD, CONCEPTUAL_SIMILARITY_THRESHOLD,
    DEFAULT_EXCLUDE_DIRS, READABILITY_THRESHOLDS, DOC_TYPE_PATTERNS,
    EXPECTED_METADATA_FIELDS, DOC_ORG_PATTERNS,
    TOPIC_SIMILARITY_THRESHOLD, DEFAULT_OLLAMA_MODEL, DEFAULT_OLLAMA_HOST,
    DOCUMENT_ANGLES, COMPREHENSIVE_ANALYSIS_PROMPT
)
from .dataclasses import DocumentSummary
from .cache import CachedDocumentStore

__all__ = [
    "MAX_WORKERS", "MIN_DOC_BYTES", "DEFAULT_EMBEDDING_MODEL",
    "<PERSON>IM<PERSON><PERSON>_DOCS_THRESHOLD", "CONCEPTUAL_SIMILARITY_THRESHOLD",
    "DEFAULT_EXCLUDE_DIRS", "READABILITY_THRESHOLDS", "DOC_TYPE_PATTERNS",
    "EXPECTED_METADATA_FIELDS", "DOC_ORG_PATTERNS",
    "TOPIC_SIMILARITY_THRESHOLD", "DEFAULT_OLLAMA_MODEL", "DEFAULT_OLLAMA_HOST",
    "DOCUMENT_ANGLES", "COMPREHENSIVE_ANALYSIS_PROMPT",
    "DocumentSummary",
    "CachedDocumentStore"
]
