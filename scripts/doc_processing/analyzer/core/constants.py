"""
Global constants for the documentation analyzer.
"""

import multiprocessing

# Constants
MAX_WORKERS = max(4, multiprocessing.cpu_count())
MIN_DOC_BYTES = 100  # Ignore tiny docs for some comparisons
DEFAULT_EMBEDDING_MODEL = "all-MiniLM-L6-v2"  # Small, fast, good quality
SIMILAR_DOCS_THRESHOLD = 0.75  # Threshold for document similarity (0-1)
CONCEPTUAL_SIMILARITY_THRESHOLD = 0.8  # Threshold for semantic similarity
# Common directories to exclude from analysis
DEFAULT_EXCLUDE_DIRS = [
    'frontend',
    '.pytest_cache',
    r'backend\.pytest_cache',  # Raw string for regex-like pattern
    r'backend\staticfiles',   # Raw string for regex-like pattern
    '.VSCodeCounter',
    'node_modules',  # Common exclusion
    'myenv',         # Python virtual environment
    '__pycache__',
    '.git'
]


READABILITY_THRESHOLDS = {
    'excellent': 70,  # Very easy to read
    'good': 60,       # Easy to read
    'acceptable': 50, # Fairly easy to read
    'difficult': 30,  # Difficult to read
    'very_difficult': 0  # Very difficult to read
}

# Documentation type patterns (for classification)
DOC_TYPE_PATTERNS = {
    'tutorial': [r'tutorial', r'getting started', r'quickstart', r'step by step', r'how to'],
    'reference': [r'reference', r'api', r'specification', r'schema', r'parameters'],
    'guide': [r'guide', r'best practices', r'guidelines', r'recommendations'],
    'concept': [r'concept', r'overview', r'introduction', r'understanding'],
    'example': [r'example', r'sample', r'demo', r'showcase'],
    'troubleshooting': [r'troubleshoot', r'debug', r'problem', r'issue', r'error', r'faq']
}

# Common metadata fields that should be present
EXPECTED_METADATA_FIELDS = [
    'title',
    'description',
    'author',
    'created_date',
    'updated_date',
    'version',
    'audience',
    'tags'
]

# Documentation organization patterns
DOC_ORG_PATTERNS = {
    'by_audience': r'docs/[^/]+/(developer|user|admin|api)/',
    'by_version': r'docs/v\d+/',
    'by_topic': r'docs/(getting-started|concepts|reference|guides|tutorials)/',
    'by_language': r'docs/(python|javascript|java|csharp|go)/'
}

# Topic analysis constants
TOPIC_SIMILARITY_THRESHOLD = 0.7  # Threshold for topic similarity
DEFAULT_OLLAMA_MODEL = "mistral"  # Default Ollama model for topic analysis
DEFAULT_OLLAMA_HOST = "http://localhost:11434"  # Default Ollama host

# Document angle categories for topic analysis
DOCUMENT_ANGLES = {
    'conceptual': ['concept', 'theory', 'overview', 'introduction', 'understanding', 'philosophy', 'principles'],
    'technical': ['implementation', 'code', 'api', 'technical', 'architecture', 'system', 'development'],
    'practical': ['tutorial', 'guide', 'how-to', 'example', 'walkthrough', 'step-by-step', 'hands-on'],
    'reference': ['reference', 'documentation', 'specification', 'parameters', 'options', 'configuration'],
    'troubleshooting': ['troubleshoot', 'debug', 'problem', 'issue', 'error', 'fix', 'solution'],
    'strategic': ['strategy', 'planning', 'roadmap', 'vision', 'goals', 'objectives', 'business']
}

# Comprehensive document analysis prompt for optimization
COMPREHENSIVE_ANALYSIS_PROMPT = """
Perform a comprehensive analysis of this document for documentation optimization:

Document Title: {title}
Document Path: {path}
Content: {content}

Analyze and respond with a JSON object containing:
STRICTLY ONLY RETURN THE JSON OBJECT.

1. "content_analysis": {{
   "main_subject": "Primary subject/topic (be specific)",
   "secondary_subjects": ["List of secondary topics"],
   "key_concepts": ["5-10 key concepts or terms"],
   "content_type": "Type (tutorial, guide, reference, overview, specification, etc.)",
   "document_purpose": "What this document aims to achieve"
}}

2. "quality_assessment": {{
   "content_depth": "1-5 scale (1=surface, 5=very detailed)",
   "completeness": "1-5 scale (1=incomplete, 5=comprehensive)",
   "clarity": "1-5 scale (1=confusing, 5=very clear)",
   "accuracy": "1-5 scale (1=outdated/wrong, 5=accurate)",
   "usefulness": "1-5 scale (1=not useful, 5=very useful)",
   "structure_quality": "1-5 scale (1=poor structure, 5=well organized)"
}}

3. "audience_analysis": {{
   "primary_audience": "Who this is written for",
   "technical_level": "1-5 scale (1=beginner, 5=expert)",
   "prerequisites": ["What readers need to know first"],
   "audience_appropriateness": "1-5 scale (1=wrong audience, 5=perfect fit)"
}}

4. "content_issues": {{
   "outdated_information": ["List specific outdated content"],
   "missing_information": ["What important info is missing"],
   "redundant_sections": ["Sections that repeat other content"],
   "unclear_sections": ["Parts that need clarification"],
   "broken_references": ["Links or references that seem broken"],
   "formatting_issues": ["Formatting problems found"]
}}

5. "optimization_opportunities": {{
   "merge_candidates": ["Other docs this could merge with"],
   "split_recommendations": ["If this should be split into multiple docs"],
   "content_improvements": ["Specific ways to improve content"],
   "structure_improvements": ["How to better organize this doc"],
   "missing_examples": ["Where examples would help"],
   "update_priorities": ["What needs updating most urgently"]
}}

6. "relationships": {{
   "depends_on": ["Docs this one references or builds upon"],
   "supports": ["Docs that reference or build upon this one"],
   "overlaps_with": ["Docs with similar content"],
   "conflicts_with": ["Docs with contradictory information"]
}}

7. "metadata_assessment": {{
   "title_quality": "1-5 scale (1=poor title, 5=excellent title)",
   "tags_needed": ["Suggested tags for this document"],
   "category_suggestion": "Best category/section for this doc",
   "maintenance_frequency": "How often this should be reviewed (daily/weekly/monthly/quarterly/yearly)"
}}

8. "actionable_recommendations": {{
   "immediate_actions": ["Things to fix right now"],
   "short_term_improvements": ["Improvements for next month"],
   "long_term_strategy": ["Strategic changes for this doc"],
   "deletion_consideration": "Should this doc be deleted? (yes/no/maybe) and why"
}}

Example response:
{{
  "content_analysis": {{
    "main_subject": "OAuth 2.0 implementation for web applications",
    "secondary_subjects": ["security best practices", "token management"],
    "key_concepts": ["OAuth 2.0", "access tokens", "authorization code flow"],
    "content_type": "implementation guide",
    "document_purpose": "Help developers implement OAuth 2.0 authentication"
  }},
  "quality_assessment": {{
    "content_depth": 4,
    "completeness": 3,
    "clarity": 4,
    "accuracy": 5,
    "usefulness": 4,
    "structure_quality": 3
  }}
}}
"""
