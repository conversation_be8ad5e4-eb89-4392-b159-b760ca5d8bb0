"""
Analyzes documents to find similar (but not identical) content with enhanced topic analysis.
"""
import time
import logging
import re
import random
import multiprocessing
from collections import defaultdict, Counter
from typing import List, Dict, Any, Optional
from concurrent.futures import ThreadPoolExecutor
import numpy as np

# Import constants that are directly used.
from ..core.constants import (
    MIN_DOC_BYTES, SIMILAR_DOCS_THRESHOLD, TOPIC_SIMILARITY_THRESHOLD,
    DEFAULT_OLLAMA_MODEL, DEFAULT_OLLAMA_HOST, DOCUMENT_ANGLES
)
from ..core.ollama_client import OllamaClient

logger = logging.getLogger(__name__)

def _extract_document_topics_with_llm(doc_summary: Any, doc_store: Any, ollama_client: OllamaClient) -> Optional[Dict[str, Any]]:
    """
    Extract topics from a document using LLM analysis.

    Args:
        doc_summary: Document summary object
        doc_store: Document store for content access
        ollama_client: Ollama client for LLM analysis

    Returns:
        Dictionary with topic analysis or None if failed
    """
    try:
        content = doc_store.get_content(doc_summary.path)
        if not content or len(content.strip()) < 100:
            return None

        # Extract topics using LLM
        topic_analysis = ollama_client.extract_topics_from_document(
            title=doc_summary.title or doc_summary.path,
            content=content
        )

        if topic_analysis:
            # Add document path for reference
            topic_analysis['document_path'] = doc_summary.path
            topic_analysis['document_title'] = doc_summary.title

        return topic_analysis

    except Exception as e:
        logger.warning(f"Failed to extract topics for {doc_summary.path}: {e}")
        return None

def _classify_document_angle_heuristic(doc_summary: Any, doc_store: Any) -> str:
    """
    Classify document angle using heuristic analysis (fallback when LLM is not available).

    Args:
        doc_summary: Document summary object
        doc_store: Document store for content access

    Returns:
        Document angle classification
    """
    try:
        content = doc_store.get_content(doc_summary.path).lower()
        title = (doc_summary.title or "").lower()
        path = doc_summary.path.lower()

        # Combine text sources for analysis
        text_to_analyze = f"{title} {path} {content[:1000]}"  # First 1000 chars

        # Score each angle based on keyword presence
        angle_scores = {}
        for angle, keywords in DOCUMENT_ANGLES.items():
            score = sum(1 for keyword in keywords if keyword in text_to_analyze)
            if score > 0:
                angle_scores[angle] = score

        # Return the angle with highest score, or 'reference' as default
        if angle_scores:
            return max(angle_scores.items(), key=lambda x: x[1])[0]
        else:
            return 'reference'

    except Exception as e:
        logger.warning(f"Failed to classify angle for {doc_summary.path}: {e}")
        return 'reference'

def _calculate_topic_overlap(topics1: List[str], topics2: List[str]) -> float:
    """
    Calculate topic overlap using simple set intersection.

    Args:
        topics1: Topics from first document
        topics2: Topics from second document

    Returns:
        Overlap score between 0 and 1
    """
    if not topics1 or not topics2:
        return 0.0

    set1 = set(topic.lower().strip() for topic in topics1)
    set2 = set(topic.lower().strip() for topic in topics2)

    intersection = len(set1.intersection(set2))
    union = len(set1.union(set2))

    return intersection / union if union > 0 else 0.0

def _calculate_embedding_similarity(doc1_path: str, doc2_path: str, doc_store: Any) -> float:
    """
    Calculate similarity using embeddings if available.

    Args:
        doc1_path: Path to first document
        doc2_path: Path to second document
        doc_store: Document store with embedding capability

    Returns:
        Similarity score between 0 and 1
    """
    try:
        if not hasattr(doc_store, 'embedding_model') or doc_store.embedding_model is None:
            return 0.0

        emb1 = doc_store.get_embedding(doc1_path)
        emb2 = doc_store.get_embedding(doc2_path)

        if emb1 is None or emb2 is None:
            return 0.0

        # Calculate cosine similarity
        norm1 = np.linalg.norm(emb1)
        norm2 = np.linalg.norm(emb2)

        if norm1 > 1e-6 and norm2 > 1e-6:
            similarity = np.dot(emb1, emb2) / (norm1 * norm2)
            return max(0.0, min(1.0, float(similarity)))  # Clamp to [0,1]
        else:
            return 0.0

    except Exception as e:
        logger.warning(f"Failed to calculate embedding similarity: {e}")
        return 0.0

# Helper function, was previously a static method
def _compare_docs_for_similarity(doc1_summary: Any, doc2_summary: Any, doc_store: Any,
                                topic_cache: Dict[str, Dict[str, Any]] = None,
                                ollama_client: OllamaClient = None) -> Optional[Dict[str, Any]]:
    """
    Compare two document summaries for similarity based on multiple factors including topic analysis.
    Helper for analyze_similar_content.
    """
    # Skip if documents are exactly the same
    if doc1_summary.content_digest == doc2_summary.content_digest:
        return None

    # Skip if documents are in completely different areas (example logic)
    if (('/docs/frontend/' in doc1_summary.path and '/docs/backend/' in doc2_summary.path) or
        ('/docs/frontend/' in doc2_summary.path and '/docs/backend/' in doc1_summary.path)):
        return None

    # 1. Title similarity
    title_similarity = 0
    if doc1_summary.title and doc2_summary.title:
        title_words1 = set(doc1_summary.title.lower().split())
        title_words2 = set(doc2_summary.title.lower().split())
        if title_words1 and title_words2: # Check if sets are not empty
            union_size = len(title_words1.union(title_words2))
            if union_size > 0: # Avoid division by zero
                title_similarity = len(title_words1.intersection(title_words2)) / union_size

    # 2. Heading similarity
    heading_similarity = 0
    if doc1_summary.headings and doc2_summary.headings:
        headings1 = set(h.lower() for h in doc1_summary.headings)
        headings2 = set(h.lower() for h in doc2_summary.headings)
        if headings1 and headings2: # Check if sets are not empty
            union_size = len(headings1.union(headings2))
            if union_size > 0: # Avoid division by zero
                heading_similarity = len(headings1.intersection(headings2)) / union_size

    # 3. Basic content similarity using tokens
    content1 = doc_store.get_content(doc1_summary.path)
    content2 = doc_store.get_content(doc2_summary.path)

    words1 = set(re.findall(r'\b[a-zA-Z0-9_]+\b', content1.lower()))
    words2 = set(re.findall(r'\b[a-zA-Z0-9_]+\b', content2.lower()))

    token_similarity = 0
    if words1 and words2:
        important_words1 = {w for w in words1 if len(w) > 4}
        important_words2 = {w for w in words2 if len(w) > 4}
        if important_words1 and important_words2: # Check if sets are not empty
            union_size = len(important_words1.union(important_words2))
            if union_size > 0: # Avoid division by zero
                token_similarity = len(important_words1.intersection(important_words2)) / union_size

    # 4. Enhanced topic similarity analysis
    topic_similarity = 0.0
    topic_analysis_1 = None
    topic_analysis_2 = None

    # Get topic analysis from cache or compute it
    if topic_cache is not None:
        topic_analysis_1 = topic_cache.get(doc1_summary.path)
        topic_analysis_2 = topic_cache.get(doc2_summary.path)

    if topic_analysis_1 and topic_analysis_2:
        # Use LLM-based topic similarity if available
        if ollama_client and ollama_client.is_available():
            topic_similarity = ollama_client.analyze_topic_similarity(
                topic_analysis_1.get('main_topics', []),
                topic_analysis_2.get('main_topics', [])
            )
        else:
            # Fallback to simple topic overlap
            topic_similarity = _calculate_topic_overlap(
                topic_analysis_1.get('main_topics', []),
                topic_analysis_2.get('main_topics', [])
            )

    # 5. Embedding-based similarity (if available)
    embedding_similarity = _calculate_embedding_similarity(
        doc1_summary.path, doc2_summary.path, doc_store
    )

    # Calculate weighted similarity with enhanced factors
    if topic_similarity > 0 or embedding_similarity > 0:
        # Use enhanced weighting when we have topic/embedding data
        weighted_similarity = (
            title_similarity * 0.15 +
            heading_similarity * 0.15 +
            token_similarity * 0.25 +
            topic_similarity * 0.25 +
            embedding_similarity * 0.20
        )
    else:
        # Fallback to original weighting
        weighted_similarity = (
            title_similarity * 0.3 +
            heading_similarity * 0.3 +
            token_similarity * 0.4
        )

    if weighted_similarity >= SIMILAR_DOCS_THRESHOLD:
        result = {
            "doc1": {"path": doc1_summary.path, "title": doc1_summary.title},
            "doc2": {"path": doc2_summary.path, "title": doc2_summary.title},
            "similarity": weighted_similarity,
            "title_similarity": title_similarity,
            "heading_similarity": heading_similarity,
            "token_similarity": token_similarity,
            "topic_similarity": topic_similarity,
            "embedding_similarity": embedding_similarity
        }

        # Add topic analysis information if available
        if topic_analysis_1 and topic_analysis_2:
            result["doc1_topics"] = topic_analysis_1.get('main_topics', [])
            result["doc2_topics"] = topic_analysis_2.get('main_topics', [])
            result["doc1_angle"] = topic_analysis_1.get('document_angle', 'unknown')
            result["doc2_angle"] = topic_analysis_2.get('document_angle', 'unknown')
            result["doc1_technical_depth"] = topic_analysis_1.get('technical_depth', 0)
            result["doc2_technical_depth"] = topic_analysis_2.get('technical_depth', 0)

        return result
    return None

def analyze_similar_content(
    docs_data: List[Dict[str, Any]],
    doc_store: Any,
    options: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Find similar but not identical documents with enhanced topic analysis.

    Args:
        docs_data: A list of document dictionaries.
        doc_store: An instance of CachedDocumentStore.
        options: Dictionary of analyzer options, including 'workers', 'ollama_model', 'ollama_host'.

    Returns:
        A dictionary containing the analysis results including topic coverage analysis.
    """
    logger.info("Finding similar content with enhanced topic analysis...")
    start_time = time.time()

    # Initialize Ollama client if configured
    ollama_client = None
    if options.get('use_ollama', True):  # Default to True, can be disabled
        ollama_host = options.get('ollama_host', DEFAULT_OLLAMA_HOST)
        ollama_model = options.get('ollama_model', DEFAULT_OLLAMA_MODEL)

        try:
            ollama_client = OllamaClient(host=ollama_host, model=ollama_model)
            if not ollama_client.is_available():
                logger.warning(f"Ollama not available at {ollama_host} with model {ollama_model}")
                ollama_client = None
            else:
                logger.info(f"Using Ollama for topic analysis: {ollama_model} at {ollama_host}")
        except Exception as e:
            logger.warning(f"Failed to initialize Ollama client: {e}")
            ollama_client = None

    summaries = []
    for doc_item in docs_data:
        path = doc_item['path']
        summary = doc_store.get_summary(path, doc_item)
        if summary.size < MIN_DOC_BYTES: # MIN_DOC_BYTES imported from core.constants
            continue
        summaries.append(summary)

    # Extract topics for all documents
    logger.info("Extracting topics from documents...")
    topic_cache = {}

    # Use ThreadPoolExecutor for topic extraction
    num_workers = options.get('workers', max(4, multiprocessing.cpu_count()))

    if ollama_client:
        # Extract topics using LLM
        with ThreadPoolExecutor(max_workers=min(4, num_workers)) as executor:  # Limit concurrent LLM calls
            topic_futures = {
                executor.submit(_extract_document_topics_with_llm, summary, doc_store, ollama_client): summary.path
                for summary in summaries[:50]  # Limit to first 50 docs to avoid overwhelming LLM
            }

            for future in topic_futures:
                try:
                    result = future.result(timeout=60)  # 60 second timeout per document
                    if result:
                        path = topic_futures[future]
                        topic_cache[path] = result
                except Exception as e:
                    path = topic_futures[future]
                    logger.warning(f"Failed to extract topics for {path}: {e}")

    # For documents without LLM analysis, use heuristic classification
    for summary in summaries:
        if summary.path not in topic_cache:
            angle = _classify_document_angle_heuristic(summary, doc_store)
            topic_cache[summary.path] = {
                'document_path': summary.path,
                'document_title': summary.title,
                'document_angle': angle,
                'main_topics': [],  # Empty for heuristic analysis
                'technical_depth': 3,  # Default medium depth
                'topic_coverage': {}
            }

    dir_groups = defaultdict(list)
    for summary in summaries:
        dir_groups[summary.dirname].append(summary)

    results = []
    # Use max_workers from options, defaulting to a reasonable number if not specified
    num_workers = options.get('workers', max(4, multiprocessing.cpu_count()))

    logger.info("Comparing documents for similarity...")
    with ThreadPoolExecutor(max_workers=num_workers) as executor:
        futures = []
        for dir_path, dir_summaries in dir_groups.items():
            if len(dir_summaries) > 1:
                pairs = [(s1, s2) for i, s1 in enumerate(dir_summaries) for s2 in dir_summaries[i+1:]]
                for pair in pairs:
                    futures.append(executor.submit(
                        _compare_docs_for_similarity,
                        pair[0], pair[1], doc_store, topic_cache, ollama_client
                    ))

        all_summaries_for_seeding = [s for group in dir_groups.values() for s in group]
        sample_size = min(50, len(all_summaries_for_seeding))

        if sample_size > 10 and len(all_summaries_for_seeding) > 0: # Ensure all_summaries_for_seeding is not empty
            seeds = random.sample(all_summaries_for_seeding, sample_size)
            for seed in seeds:
                other_dirs = [d for d in dir_groups.keys() if d != seed.dirname]
                if other_dirs:
                    comparison_docs_sample = []
                    # Limit comparison to a few other directories to manage complexity
                    for other_dir in random.sample(other_dirs, min(3, len(other_dirs))):
                        other_dir_docs = dir_groups[other_dir]
                        if other_dir_docs:
                            samples = random.sample(other_dir_docs, min(5, len(other_dir_docs)))
                            comparison_docs_sample.extend(samples)

                    for other_doc_summary in comparison_docs_sample:
                        # Ensure we don't compare a doc with itself if it ended up in seeds and comparison_docs_sample
                        if seed.path != other_doc_summary.path:
                             futures.append(executor.submit(
                                 _compare_docs_for_similarity,
                                 seed, other_doc_summary, doc_store, topic_cache, ollama_client
                             ))

        for future in futures:
            result = future.result()
            if result:
                results.append(result)

    results.sort(key=lambda x: x["similarity"], reverse=True)

    # Generate topic coverage analysis
    logger.info("Analyzing topic coverage...")
    topic_coverage = _analyze_topic_coverage(topic_cache, summaries)

    duration = time.time() - start_time
    logger.info(f"Found {len(results)} similar documents in {duration:.2f}s")

    return {
        "similar_pairs": results,
        "count": len(results),
        "analysis_time": duration,
        "topic_coverage": topic_coverage,
        "documents_analyzed": len(summaries),
        "documents_with_topics": len([t for t in topic_cache.values() if t.get('main_topics')]),
        "ollama_used": ollama_client is not None and ollama_client.is_available()
    }

def _analyze_topic_coverage(topic_cache: Dict[str, Dict[str, Any]], summaries: List[Any] = None) -> Dict[str, Any]:
    """
    Analyze how topics are covered across documents and from which angles.

    Args:
        topic_cache: Cache of topic analysis results
        summaries: List of document summaries

    Returns:
        Dictionary with topic coverage analysis
    """
    # Collect all topics and their coverage
    all_topics = Counter()
    topic_to_docs = defaultdict(list)
    angle_distribution = Counter()
    topic_by_angle = defaultdict(lambda: defaultdict(list))

    for path, analysis in topic_cache.items():
        if not analysis:
            continue

        angle = analysis.get('document_angle', 'unknown')
        angle_distribution[angle] += 1

        topics = analysis.get('main_topics', [])
        for topic in topics:
            topic_lower = topic.lower().strip()
            all_topics[topic_lower] += 1
            topic_to_docs[topic_lower].append({
                'path': path,
                'title': analysis.get('document_title', ''),
                'angle': angle,
                'technical_depth': analysis.get('technical_depth', 0),
                'coverage_score': analysis.get('topic_coverage', {}).get(topic, 0)
            })
            topic_by_angle[topic_lower][angle].append(path)

    # Find topics covered from multiple angles
    multi_angle_topics = {}
    for topic, angle_docs in topic_by_angle.items():
        if len(angle_docs) > 1:  # Topic covered from multiple angles
            multi_angle_topics[topic] = {
                'total_docs': sum(len(docs) for docs in angle_docs.values()),
                'angles': dict(angle_docs),
                'angle_count': len(angle_docs)
            }

    # Find potential gaps (topics covered from only one angle)
    single_angle_topics = {}
    for topic, angle_docs in topic_by_angle.items():
        if len(angle_docs) == 1:
            angle = list(angle_docs.keys())[0]
            single_angle_topics[topic] = {
                'angle': angle,
                'doc_count': len(list(angle_docs.values())[0]),
                'docs': list(angle_docs.values())[0]
            }

    # Calculate coverage statistics
    total_docs_with_topics = len([a for a in topic_cache.values() if a.get('main_topics')])

    return {
        'total_unique_topics': len(all_topics),
        'most_common_topics': dict(all_topics.most_common(10)),
        'angle_distribution': dict(angle_distribution),
        'multi_angle_topics': multi_angle_topics,
        'single_angle_topics': single_angle_topics,
        'topic_coverage_gaps': [
            topic for topic, data in single_angle_topics.items()
            if data['doc_count'] == 1 and data['angle'] not in ['reference', 'practical']
        ],
        'well_covered_topics': [
            topic for topic, data in multi_angle_topics.items()
            if data['angle_count'] >= 3
        ],
        'total_documents_analyzed': total_docs_with_topics
    }
