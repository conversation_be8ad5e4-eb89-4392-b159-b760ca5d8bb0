# Agent Reference Guide

*Last Updated: 2025-04-24*

This document provides a comprehensive overview of all agents in the Goali system, extracted and organized from various documentation sources.

## Core Agents

### Mentor Agent
**Role/Instructions:**
- Serves as the primary user-facing conversational interface within workflow graphs
- Builds trust and relationship with the user through consistent, supportive interactions
- Processes conversational context passed from the ConversationDispatcher
- Presents activities, explanations, and feedback in user-friendly language
- Maintains the philosophical framing of the game throughout all interactions
- Adjusts tone and framing based on user-specific communication guidelines

**Special Architectural Position:**
- Operates in a dual role as both a workflow participant and a system-level component
- Maintains privileged communication channel with the ConversationDispatcher
- Serves as the exclusive formatter of user-facing messages across all workflows
- Detects workflow transition needs during ongoing conversations
- Preserves conversation continuity and personality consistency across workflow boundaries
- Acts as the persistent "face" of the system regardless of backend processing

### Orchestrator Agent
**Role:**
- Coordinates the flow of information between all specialized agents
- Determines which agents to invoke and in what sequence based on current task
- Evaluates and makes decisions between specialized agent recommendations
- Implements a priority framework for decision-making
- Manages error handling and recovery for the entire agent system

### Resource & Capacity Agent
**Role:**
- Analyzes user's available physical resources, time, and environmental context
- Identifies constraints and limitations that affect activity feasibility
- Maps resource requirements to user's current inventory and capabilities
- Validates environmental compatibility with activities
- Provides feasibility assessments for potential activities

### Engagement & Pattern Analytics Agent
**Role:**
- Integrates quantitative engagement metrics with qualitative user feedback
- Analyzes historical interaction patterns alongside real-time sentiment
- Identifies recurring behavioral and emotional themes to guide system improvements
- Provides data-driven view of user engagement and satisfaction
- Supports targeted adjustments to activities based on pattern insights

### Psychological Monitoring Agent
**Role:**
- Assesses user's current psychological state and trust level
- Analyzes relationship between activities and user's belief system
- Evaluates appropriate challenge levels based on user traits
- Identifies growth opportunities and psychological readiness
- Monitors emotional vulnerabilities and safety boundaries

### Strategy Agent
**Role:**
- Formulates comprehensive activity selection strategy based on multi-agent inputs
- Performs gap analysis between user traits and activity requirements
- Balances challenge calibration with domain distribution
- Aligns activity strategy with user's growth trajectory
- Establishes boundary conditions and constraints for activity selection

### Wheel/Activity Agent
**Role:**
- Selects concrete activities based on strategy framework
- Tailors generic activities to user's specific context
- Constructs the wheel with appropriate probability distributions
- Customizes activity instructions and resource requirements
- Provides clear value propositions for selected activities

### Ethical Oversight Agent
**Role:**
- Reviews all proposed activities for ethical alignment
- Ensures respect for user autonomy and well-being
- Validates appropriate challenge calibration
- Verifies transparency in activity descriptions
- Implements the ethical framework of benevolence, fairness, and transparency

### Error Handler Agent
**Role:**
- Provides graceful error recovery when other agents encounter issues
- Maintains user experience continuity despite system failures
- Analyzes error types and determines appropriate recovery strategies
- Creates appropriate fallback responses when recovery isn't possible
- Implements the system's resilience and fault tolerance layer

## Agent Workflows
For detailed information on agent interactions in specific workflows, see:

- [Onboarding Flow](./flows/onboarding_flow.md)
- [Pre-Spin Flow](./flows/pre_spin_FLOW.md)
- [Post-Spin Flow](./flows/post_spin_FLOW.md)
- [Post-Activity Flow](./flows/post_activity_FLOW.md)
- [Discussion Flow](./flows/discussion_flow.md)
- [Wheel Generation Flow](./flows/wheel_generation_FLOW.md)

## Agent Testing
For information on how to test agents, see [Agent Testing Guide](../agent_testing_guide.md).
