# Game of Life AI System: Enhanced Data Flow Architecture

This document provides a comprehensive overview of the enhanced data flow architecture in the Game of Life AI system, focusing on how data moves between components with the new MentorService singleton integration, enhanced metadata collection, and improved user experience.

## 1. Architecture Overview

The Game of Life AI system follows an enhanced multi-agent architecture with real-time communication and persistent user state management. The system is designed to process user inputs through a sophisticated routing hub, maintain user context across sessions, execute workflows through specialized agent teams, and deliver personalized results back to the user.

### 1.1 Core Components (Enhanced Architecture v2.0.0)

- **Frontend WebSocket Client**: Sends user messages and receives enhanced responses with metadata
- **UserSessionConsumer**: WebSocket handler managing connections and message routing with enhanced error handling
- **ConversationDispatcher (v2.0.0)**: Central routing hub with MentorService integration and enhanced context processing
- **MentorService (Singleton)**: Per-user persistent state management and communication enhancement
- **Agent Workflow System**: Processes requests through specialized agent workflows with enhanced metadata collection
- **WorkflowResultHandler**: Processes completed workflow results with MentorService post-processing
- **Database**: Stores user profiles, activities, historical data, and enhanced metadata

### 1.2 Data Flow Diagram

```
┌─────────────┐         ┌─────────────────┐         ┌───────────────────┐         ┌───────────────┐
│             │         │                 │         │                   │         │               │
│   Frontend  │◄───────►│ WebSocket       │◄───────►│ Conversation      │────────►│ Agent         │
│   Client    │         │ Consumer        │         │ Dispatcher        │         │ Workflows     │
│             │         │                 │         │                   │         │               │
└─────────────┘         └─────────────────┘         └───────────────────┘         └─────┬─────────┘
                                ▲                                                       │
                                │                                                       │
                                │                                                       │
                                │                                                       │
                        ┌───────┴───────┐                                        ┌─────▼─────────┐
                        │               │                                        │               │
                        │ Workflow      │◄───────────────────────────────────────┤ Celery Task   │
                        │ Result        │                                        │ System        │
                        │ Handler       │                                        │               │
                        └───────────────┘                                        └───────────────┘
```

## 2. Component Responsibilities

### 2.1 Frontend WebSocket Client

- Establishes and maintains WebSocket connection
- Sends user messages in standardized format
- Processes and displays responses
- Renders UI elements like the wheel and activity details

### 2.2 UserSessionConsumer (WebSocket Handler)

- Manages WebSocket connections for individual users
- Routes incoming messages to appropriate handlers
- Formats and forwards responses to clients
- Tracks active workflows for each client
- Provides real-time status updates

### 2.3 ConversationDispatcher (Enhanced v2.0.0)

**Central Message Routing Hub with Enhanced Architecture Integration**

- **Enhanced Message Processing Pipeline**:
  - Routes all messages through MentorService for pre-processing with user context
  - Extracts context with enhanced metadata collection support
  - Classifies messages using intelligent LLM + rule-based hybrid approach
  - Builds comprehensive context packets with MentorService integration
  - Initiates workflows with enhanced monitoring and error handling

- **Robust Error Handling & Fallbacks**:
  - Graceful degradation when MentorService or LLM clients fail
  - Comprehensive fallback mechanisms for all processing steps
  - Enhanced debug information and monitoring throughout pipeline
  - Structured error responses with detailed metadata

- **Enhanced Context Packet Construction**:
  - Integrates MentorService context (trust level, communication preferences)
  - Includes workflow classification metadata and confidence scoring
  - Adds system metadata for enhanced monitoring and debugging
  - Supports enhanced architecture flags for downstream components

- **Performance & Monitoring**:
  - Processing time tracking and performance metrics
  - Comprehensive debug information emission via EventService
  - Enhanced logging with correlation IDs and context information
  - Real-time monitoring of component availability and health

### 2.3.1 MentorService (Singleton per User)

- **Maintains per-user state and context across sessions**
- Pre-processes incoming user messages with user-specific context
- Post-processes workflow results to enhance user communication
- Generates personalized user messages based on:
  - User trust level and psychological profile
  - Workflow execution quality and results
  - Communication style preferences
  - Safety and ethical considerations
- **Provides enhanced metadata for improved user experience**

### 2.4 Agent Workflow System

- Processes requests through specialized agent nodes
- Coordinates multi-agent workflows
- Generates personalized responses and activities
- Accesses and updates user profiles and models
- Records workflow execution in the database

### 2.5 WorkflowResultHandler

- Processes completed workflow results
- Formats results according to workflow type
- Delivers formatted messages to appropriate WebSocket clients
- Handles error conditions and notifications
- Maintains consistent API contract compliance

## 3. Message Types and Data Formats

### 3.1 Client → Server Messages

| Type | Purpose | Key Fields |
|------|---------|------------|
| `chat_message` | User sends text message | `message`, `user_profile_id` |
| `spin_result` | User selects activity from wheel | `activity_tailored_id`, `name` |
| `workflow_status_request` | Request status update | `workflow_id` |

### 3.2 Server → Client Messages

| Type | Purpose | Key Fields |
|------|---------|------------|
| `chat_message` | Text response from agent | `content`, `is_user` |
| `system_message` | System notifications | `content` |
| `processing_status` | Processing state updates | `status` |
| `wheel_data` | Activity wheel configuration | `wheel` object |
| `error` | Error notifications | `content` |
| `workflow_status` | Workflow execution status | `workflow_id`, `status` |
| `activity_details` | Selected activity details | `details` object |

### 3.3 Enhanced Context Packet Format (v2.0.0)

Context packets are comprehensive data containers with enhanced metadata support:

```json
{
  "user_id": "user-profile-uuid",
  "user_profile_id": "user-profile-uuid",
  "session_timestamp": "2023-10-15T14:30:00Z",
  "reported_mood": "focused",
  "reported_environment": "home",
  "reported_time_availability": "30 minutes",
  "reported_focus": "creative activities",
  "extraction_confidence": 0.85,
  "user_ws_session_name": "client_session_uuid",

  "mentor_context": {
    "trust_level": 0.65,
    "communication_preferences": {
      "verbosity": "moderate",
      "formality": "casual",
      "encouragement_level": "high"
    },
    "conversation_context": {
      "last_message": "I'm feeling creative today",
      "last_message_time": "2023-10-15T14:29:45Z"
    },
    "mentor_assessment": {
      "emotional_tone": "positive",
      "urgency_level": "low",
      "support_needs": ["activity_suggestions"]
    },
    "service_id": "user-123_session-456"
  },

  "workflow_metadata": {
    "intended_workflow": "wheel_generation",
    "classification_confidence": 0.87,
    "classification_reason": "Activity-related keywords detected",
    "llm_classification_used": true
  },

  "system_metadata": {
    "dispatcher_version": "2.0.0",
    "mentor_service_available": true,
    "llm_client_available": true,
    "processing_timestamp": "2023-10-15T14:30:00.123Z",
    "enhanced_architecture": true
  }
}
```

## 4. Workflow Types and Processing

### 4.1 Primary Workflow Types

| Workflow Type | Purpose | Typical Duration |
|---------------|---------|------------------|
| `wheel_generation` | Create activity suggestions | ~15 seconds |
| `activity_feedback` | Process activity completion feedback | ~10 seconds |
| `pre_spin_feedback` | Process context before spinning (optional, context-dependent) | ~5 seconds |
| `post_spin` | Handle activity selection | ~8 seconds |
| `user_onboarding` | Guide new user setup | ~20 seconds |
| `discussion` | Handle general chat or unclassified intent | Variable |

### 4.2 Workflow Classification Process

1. **Initial Analysis**: Extract context from user message
2. **Pattern Matching**: Apply keyword and pattern recognition
3. **Confidence Scoring**: Assign confidence to potential classifications
4. **Rule Application**: Apply business rules to refine classification
5. **Final Determination**: Select workflow type with highest confidence

## 5. Detailed Data Flow Examples

### 5.1 Chat Message Flow

1. User sends chat message via WebSocket
2. WebSocket handler echoes message back to user (immediate feedback)
3. ConversationDispatcher analyzes message and extracts context
4. Message is classified to determine appropriate workflow
5. Workflow is initiated via Celery task with context packet
6. Agent team processes the request through workflow graph
7. Workflow completes and results are passed to WorkflowResultHandler
8. Results are formatted and sent to client via WebSocket
9. Client displays the response(s)

### 5.2 Wheel Generation Flow (Enhanced with MentorService)

1. User requests activity suggestions
2. **MentorService pre-processes message with user context**
3. ConversationDispatcher classifies as `wheel_generation`
4. Agent workflow proceeds through stages:
   - Resource assessment
   - Engagement analysis
   - Psychological assessment
   - Strategy formulation
   - Activity selection
   - Ethical validation
5. **Enhanced metadata collection for Mentor processing**
6. **MentorService post-processes workflow results**:
   - Analyzes user insights (trust level, mood, traits)
   - Extracts wheel characteristics (activity count, domains, personalization)
   - Generates communication guidance (style, tone, encouragement level)
   - Crafts personalized user message based on context
7. WorkflowResultHandler processes the enhanced workflow results
8. Enhanced messages sent to client:
   - **Mentor-crafted chat message** with personalized introduction
   - **Wheel data with enhanced metadata** for improved visualization
   - **Workflow insights** for better user understanding

### 5.3 Activity Selection (Spin Result) Flow

1. User selects activity from wheel
2. Selection is sent via WebSocket as `spin_result`
3. ConversationDispatcher processes as `post_spin` workflow
4. Agent workflow processes the selection
5. WorkflowResultHandler formats results
6. Client receives:
   - Chat message with activity introduction
   - Activity details for display

## 6. Error Handling and Recovery

### 6.1 Error Types and Handling

| Error Type | Handling Approach |
|------------|-------------------|
| Connection error | Exponential backoff reconnection |
| Task failure | Notification to user, fallback response |
| Classification failure | Default to wheel generation |
| Missing context | Request additional information |
| Timeout | Status update and retry option |

### 6.2 Recovery Mechanisms

- Connection monitoring and automatic reconnection
- Database transaction integrity
- Fallback responses for workflow failures
- System status notifications
- Error logging for diagnostics

## 7. Implementation Details

### 7.1 WorkflowResultHandler Implementation

```python
def process_result(self, workflow_id, result, task_id, workflow_type):
    """Process workflow result and send to client"""
    # Extract WebSocket session
    user_ws_session = result.get('user_ws_session_name')
    
    # Format results based on workflow type
    formatted_messages = self._format_result_messages(result, workflow_type)
    
    # Send all formatted messages to client
    for message in formatted_messages:
        async_to_sync(self.channel_layer.group_send)(
            user_ws_session,
            message
        )
```

### 7.2 ConversationDispatcher Implementation

```python
def _launch_workflow(self, workflow_type, context_packet, workflow_id):
    """Launch workflow with WebSocket session information"""
    initial_input = {
        "task_type": workflow_type,
        "context_packet": context_packet,
        "workflow_type": workflow_type,
        "user_ws_session_name": self.user_ws_session_name
    }
    
    # Launch Celery task
    execute_graph_workflow.delay(
        workflow_id=workflow_id,
        user_profile_id=self.user_profile_id,
        initial_input=initial_input
    )
```

### 7.3 Celery Signal Handling

```python
@task_success.connect
def handle_task_success(sender=None, result=None, **kwargs):
    """Handle successful task completion"""
    task_name = sender.name
    
    if 'execute_graph_workflow' in task_name:
        # Extract workflow information
        workflow_id = result.get('workflow_id')
        workflow_result = result.get('result', {})
        
        # Process with result handler
        workflow_result_handler.process_result(
            workflow_id=workflow_id,
            result=workflow_result,
            task_id=sender.request.id,
            workflow_type=workflow_result.get('workflow_type', 'unknown')
        )
```

## 8. Security Considerations

- Authentication required before establishing WebSocket connections
- User profile ID validation before processing messages
- Rate limiting to prevent abuse
- Input validation for all incoming messages
- HTTPS/WSS protocol enforcement
- Cross-origin resource sharing (CORS) configuration

## 9. Performance Optimization

- Channel layer backend optimization (Redis recommended)
- Worker process configuration for handling asynchronous tasks
- Database connection pooling
- WebSocket connection management
- Load balancing for horizontal scaling
- Result caching where appropriate

## 10. Monitoring and Diagnostics

### 10.1 Key Metrics to Track

- WebSocket connection count and duration
- Message throughput by type
- Workflow execution times
- Error rates by component
- Task queue length
- Database query performance

### 10.2 Logging Strategy

- Structured logging with correlation IDs
- Workflow and task identifiers in all logs
- Error stack traces with context information
- Performance timing markers
- User session tracking

## 11. Future Enhancements

- WebSocket event publishing from agent workflows
- Message queues for workflow result delivery
- WebRTC for voice interface integration
- Streaming responses for long-running workflows
- Client-side state management improvements
- Predictive pre-processing of common requests
