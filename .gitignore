*_generated.*
__pycache__/
myenv/
.git/
backend/docs/build/
backend/apps/main/tests/test_data/
frontend/node_modules/
TASK_OLD.md
.langgraph_api/
.VSCodeCounter/
backend/staticfiles/
backend/coverage.lcov
.coverage
backend/benchmark_results/
backend/scripts/codebase.json
backend/test-results/junit.xml
backend/backend/error_report.txt
backend/test-db.sqlite3

frontend_mock/
frontend_enhanced/node_modules/
frontend_enhanced/dist/
frontend_enhanced/.env*
codebase_export.json

backend/coverage.json
monitoring/grafana/plugins/
node_modules/
package.json
package-lock.json
.idea/
bmad-agent/
codebase-analysis/venv/
tools/graphiti/analysis/venv/
**/.DS_Store
.DS_Store
tools/mock_server/mock_server.log
