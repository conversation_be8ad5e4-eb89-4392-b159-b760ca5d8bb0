# backend/config/admin.py
from django.contrib import admin
from django.contrib.admin import AdminSite
from django.urls import path, include, reverse_lazy # Import path and include
from django.utils.translation import gettext_lazy as _

# Import admin_tools views directly
from apps.admin_tools import views as admin_tools_views
from apps.admin_tools.benchmark import views as benchmark_views

class GameOfLifeAdminSite(AdminSite):
    # Text to put at the end of each page's <title>
    site_title = _('Game of Life Admin')

    # Text to put in each page's <h1> (and above login form)
    site_header = _('Game of Life Administration')

    # Text to put at the top of the admin index page
    index_title = _('Game of Life Management')

    # URL for the "View site" link at the top of each admin page
    site_url = '/'

    # Default empty value display for all admin pages
    empty_value_display = '-'

    def get_urls(self):
        """
        Override get_urls to add custom admin tool views.
        """
        urls = super().get_urls()
        custom_urls = [
            # WebSocket Tester
            path('websocket-tester/', self.admin_view(admin_tools_views.websocket_tester_view), name='websocket_tester'),
            # Benchmark Dashboard
            path('benchmarks/', self.admin_view(admin_tools_views.dashboard), name='benchmark_dashboard'),
            # Benchmark History
            path('benchmarks/history/', self.admin_view(benchmark_views.benchmark_history), name='benchmark_history'),
            path('benchmarks/history/<str:agent_role>/', self.admin_view(benchmark_views.benchmark_history), name='benchmark_history_by_agent'),
            # Benchmark Management
            path('benchmarks/manage/', self.admin_view(benchmark_views.benchmark_management), name='benchmark_management'),
            # Benchmark API Endpoints (Remove admin_view for async views)
            path('benchmarks/api/run/', admin_tools_views.BenchmarkRunView.as_view(), name='benchmark_runs_api'),
            # Use path converter for both UUID and integer IDs
            path('benchmarks/api/run/<str:run_id>/', admin_tools_views.BenchmarkRunView.as_view(), name='benchmark_runs_detail_api'),
            # Stop benchmark task endpoint
            path('benchmarks/api/run/<str:task_id>/stop/', admin_tools_views.BenchmarkRunStopView.as_view(), name='benchmark_stop_api'),
            # Check benchmark task status endpoint
            path('benchmarks/api/task/<str:task_id>/status/', admin_tools_views.BenchmarkTaskStatusView.as_view(), name='benchmark_task_status_api'),
            # Benchmark Scenario API Endpoints
            path('benchmarks/api/scenarios/', benchmark_views.BenchmarkScenarioView.as_view(), name='benchmark_scenarios_api'),
            path('benchmarks/api/scenarios/<int:scenario_id>/', benchmark_views.BenchmarkScenarioView.as_view(), name='benchmark_scenario_detail_api'),
            # Workflow Type API Endpoint
            path('benchmarks/api/workflow-types/', benchmark_views.WorkflowTypeView.as_view(), name='workflow_types_api'),
            # Evaluation Criteria Template API Endpoints
            path('benchmarks/api/templates/', benchmark_views.EvaluationCriteriaTemplateView.as_view(), name='evaluation_templates_api'),
            path('benchmarks/api/templates/<int:template_id>/', benchmark_views.EvaluationCriteriaTemplateView.as_view(), name='evaluation_template_detail_api'),
            # Benchmark Validation API Endpoint
            path('benchmarks/api/validate/', benchmark_views.BenchmarkValidationView.as_view(), name='benchmark_validation_api'),
            path('benchmarks/api/validate/<int:scenario_id>/', benchmark_views.BenchmarkValidationView.as_view(), name='benchmark_scenario_validation_api'),
            # Benchmark Import/Export
            path('benchmarks/export/', self.admin_view(benchmark_views.export_scenarios), name='export_scenarios'),
            path('benchmarks/import/', self.admin_view(benchmark_views.import_scenarios), name='import_scenarios'),
            # Run All Benchmarks API Endpoint (Async, returns JSON)
            path('benchmarks/api/run-all/', benchmark_views.run_all_benchmarks_view, name='run_all_benchmarks'),
            # User Profile API Endpoint
            path('benchmarks/api/user-profiles/', benchmark_views.UserProfileView.as_view(), name='user_profiles_api')
        ]
        # Prepend custom URLs to ensure they are checked before default admin patterns
        return custom_urls + urls

    def index(self, request, extra_context=None):
        """
        Override the default index to add our custom dashboard data and correct tool URLs.
        """
        # Remove delayed import from here
        from apps.activity.models import GenericActivity, ActivityTailored, GenericDomain # Renamed import
        from apps.user.models import UserProfile, GenericTrait, UserGoal
        from apps.main.models import Wheel, WheelItem, GenericAgent, CustomAgent, HistoryEvent, UserFeedback

        # Prepare context data for the dashboard
        app_counts = {
            'generic_activities_count': GenericActivity.objects.count(),
            'tailored_activities_count': ActivityTailored.objects.count(),
            'activity_domains_count': GenericDomain.objects.count(), # Use renamed model
            'user_profiles_count': UserProfile.objects.count(),
            'generic_traits_count': GenericTrait.objects.count(),
            'user_goals_count': UserGoal.objects.count(),
            'wheels_count': Wheel.objects.count(),
            'wheel_items_count': WheelItem.objects.count(),
            'generic_agents_count': GenericAgent.objects.count(),
            'custom_agents_count': CustomAgent.objects.count(),
            'history_events_count': HistoryEvent.objects.count(),
            'user_feedback_count': UserFeedback.objects.count(),

            # Get the latest items
            'latest_generic_activities': GenericActivity.objects.all().order_by('-created_on')[:5],
            'latest_tailored_activities': ActivityTailored.objects.all().order_by('-created_on')[:5],
            'latest_history_events': HistoryEvent.objects.all().order_by('-timestamp')[:5],

            # Add coverage dashboard link
            'coverage_dashboard_url': '/coverage/', # Assuming this is still separate
            # Add links using the admin site's namespace ('game_of_life_admin' or default 'admin')
            'websocket_tester_url': reverse_lazy('admin:websocket_tester'),
            'benchmark_dashboard_url': reverse_lazy('admin:benchmark_dashboard'),
        }

        # Update the context
        # Call the superclass index first to get the base context (including app_list)
        response = super().index(request, extra_context)

        # Check if the response has context_data (it should for TemplateResponse)
        if hasattr(response, 'context_data'):
            # Update the context generated by the superclass with our custom counts
            response.context_data.update(app_counts)
        else:
             # Fallback or logging if context_data isn't available (unexpected)
             # For now, we'll just proceed, but this might indicate another issue
             pass # Or log a warning

        return response

# Instantiate the custom admin site
admin_site = GameOfLifeAdminSite(name='game_of_life_admin')
