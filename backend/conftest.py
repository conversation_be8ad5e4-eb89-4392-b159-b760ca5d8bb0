# backend/conftest.py
import os
import sys
import asyncio
import functools
import signal
import threading
import time
import pytest
from typing import Dict, Any, Optional, Callable, Union, TypeVar, cast
from _pytest.nodes import Item
from _pytest.runner import TestReport
import logging
import importlib
from contextlib import contextmanager

# Imports deferred until Django is ready
from django.core.management import call_command
from django.db import connection
from django.apps import apps
from apps.main.agents.tools.tools_util import sync_tool_registry_with_database
from apps.main.models import GenericAgent, LLMConfig, BenchmarkScenario # Import models for cleanup


logger = logging.getLogger(__name__)

# Flag to ensure session-scoped setup runs only once
_session_setup_complete = False

def pytest_configure(config):
    """Setup environment variables early."""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.test')
    os.environ.setdefault('TESTING', 'true')
    os.environ.setdefault('MISTRAL_API_KEY', 'test-api-key-for-testing')
    logger.info("pytest_configure: Set TESTING=true, DJANGO_SETTINGS_MODULE, and MISTRAL_API_KEY.")

    # Configure comprehensive logging to prevent closed file stream errors
    import logging
    import sys
    from io import StringIO

    # Create a safe null handler that won't fail on closed streams
    class SafeNullHandler(logging.NullHandler):
        """A null handler that safely handles all logging operations."""
        def emit(self, record):
            pass

        def handle(self, record):
            pass

        def close(self):
            pass

    # Set up safe null handlers for all problematic loggers
    problematic_loggers = [
        'mistralai',
        'mistralai.httpclient',
        'httpx',
        'httpcore',
        'httpx._client',
        'httpcore._async',
        'httpcore._sync',
        'asyncio'
    ]

    for logger_name in problematic_loggers:
        target_logger = logging.getLogger(logger_name)
        target_logger.handlers.clear()  # Remove existing handlers
        target_logger.addHandler(SafeNullHandler())
        target_logger.propagate = False  # Prevent propagation to root logger
        target_logger.setLevel(logging.CRITICAL + 1)  # Effectively disable logging

    # Check for pytest-asyncio (optional, for warning)
    try:
        importlib.import_module('pytest_asyncio')
    except ImportError:
        import warnings
        warnings.warn("pytest-asyncio plugin not found. Async tests may not run correctly.")


def pytest_sessionfinish(session, exitstatus):
    """
    Clean up MistralAI clients before session ends to prevent logging errors.

    This hook runs at the very end of the test session, before the logging
    system is shut down, ensuring proper cleanup of async HTTP clients.
    """
    try:
        # Import here to avoid issues if mistralai is not available
        import mistralai.httpclient

        # Check if there are any clients to close
        if hasattr(mistralai.httpclient, 'close_clients'):
            logger.info("Cleaning up MistralAI HTTP clients...")

            # Try to run the cleanup in a controlled way
            import asyncio
            try:
                # Get or create an event loop
                try:
                    loop = asyncio.get_running_loop()
                except RuntimeError:
                    # No running loop, create a new one
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                # Run the cleanup
                loop.run_until_complete(mistralai.httpclient.close_clients())
                logger.info("Successfully cleaned up MistralAI HTTP clients")

            except Exception as cleanup_error:
                # If cleanup fails, just log it but don't fail the tests
                logger.warning(f"Failed to clean up MistralAI clients: {cleanup_error}")

    except ImportError:
        # mistralai not available, nothing to clean up
        pass
    except Exception as e:
        # Any other error during cleanup, log but don't fail
        logger.warning(f"Error during MistralAI cleanup: {e}")

@pytest.fixture(autouse=True)
def test_metadata(request: pytest.FixtureRequest) -> None:
    """Attach metadata to test items (example)."""
    marker = request.node.get_closest_marker('test_type')
    test_type = marker.args[0] if marker else 'unit'
    request.node.user_properties.append(('test_type', test_type))


@pytest.fixture(autouse=True)
def mistral_client_cleanup():
    """
    Automatically clean up MistralAI clients after each test.

    This fixture ensures that any MistralAI clients created during a test
    are properly cleaned up, preventing logging errors during teardown.
    """
    # Setup: nothing needed before test
    yield

    # Teardown: clean up any MistralAI clients
    try:
        import mistralai.httpclient

        # Check if there are any clients to close
        if hasattr(mistralai.httpclient, 'close_clients'):
            import asyncio

            try:
                # Try to get the current event loop
                loop = asyncio.get_running_loop()

                # Create a task to close clients
                task = loop.create_task(mistralai.httpclient.close_clients())

                # Don't wait for it to complete, just schedule it
                # This prevents blocking the test teardown

            except RuntimeError:
                # No running event loop, try to create one briefly
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    loop.run_until_complete(mistralai.httpclient.close_clients())
                    loop.close()
                except Exception:
                    # If this fails, just ignore it
                    pass

    except (ImportError, Exception):
        # If anything fails, just ignore it - we don't want to break tests
        pass

@pytest.fixture(scope='session')
def django_db_setup(django_db_setup, django_db_blocker):
    """
    Centralized session-scoped fixture for test database setup.

    Ensures migrations, table creation for non-migrated models,
    seeding, and tool registration happen exactly once per session
    after pytest-django initializes the database connection.
    """
    global _session_setup_complete
    if _session_setup_complete:
        logger.debug("django_db_setup (session): Setup already complete, skipping.")
        return

    logger.info("--- Starting conftest.py django_db_setup (session scope) ---")

    with django_db_blocker.unblock():
        # Imports deferred until Django is ready
        from django.core.management import call_command
        from django.db import connection
        from django.apps import apps
        from apps.main.agents.tools.tools_util import sync_tool_registry_with_database
        from apps.main.models import GenericAgent, LLMConfig # For default agent creation

        # 1. Apply Migrations (Handled by pytest-django's default django_db_setup)
        logger.info("Migrations applied by pytest-django.")

        # 2. Create tables for models without migrations
        logger.info("Creating tables for non-migrated models...")
        try:
            for app_config in apps.get_app_configs():
                logger.debug(f"Processing app: {app_config.label}")
                for model in app_config.get_models():
                    table_name = model._meta.db_table
                    with connection.cursor() as cursor:
                        cursor.execute("SELECT to_regclass(%s)", [table_name])
                        table_exists = cursor.fetchone()[0] is not None
                    if not table_exists:
                        try:
                            logger.info(f"Creating table {table_name}")
                            with connection.schema_editor() as schema_editor:
                                schema_editor.create_model(model)
                        except Exception as e:
                            logger.warning(f"Error creating table {table_name}: {e}")
            logger.info("Finished creating tables for non-migrated models.")
        except Exception as e:
            logger.error(f"Error during non-migrated table creation: {e}", exc_info=True)
            # Decide if this is fatal or not
            # raise # Re-raise if fatal

        # 3. Seed minimal test data
        logger.info("Seeding minimal test data...")
        seed_commands = [
            'seed_db_10_hexacos',
            'seed_db_20_limitations',
            'seed_db_30_domains',
            'seed_db_40_envs',
        ]
        for cmd in seed_commands:
            try:
                call_command(cmd, verbosity=0) # Use verbosity 0 for cleaner logs
                logger.info(f"Successfully seeded {cmd}")
            except Exception as e:
                logger.warning(f"Failed to seed {cmd}: {e}")

        # 4. Register tools (Import modules + Sync DB)
        logger.info("Registering tools directly...")
        tool_modules = [
            'apps.main.agents.tools.tools',
            'apps.main.agents.tools.extra_tools',
            'apps.main.agents.tools.get_user_profile_tool',
            'apps.main.agents.tools.dispatcher_tools',
            'apps.main.agents.tools.mentor_tools',
            'apps.main.agents.tools.update_current_mood_tool',
        ]
        for module_name in tool_modules:
            try:
                logger.debug(f"Importing tool module {module_name}")
                if module_name in sys.modules:
                    importlib.reload(sys.modules[module_name])
                else:
                    importlib.import_module(module_name)
            except ImportError as e:
                logger.warning(f"Could not import tool module {module_name}: {e}")
            except Exception as e:
                logger.error(f"Unexpected error importing tool module {module_name}: {e}", exc_info=True)

        try:
            logger.info("Synchronizing tool registry with database...")
            sync_tool_registry_with_database()
            logger.info("Successfully synchronized tools with database.")
        except Exception as e:
            logger.error(f"Error synchronizing tools: {e}", exc_info=True)

        # 5. Connect tools (Optional, if needed after registration)
        logger.info("Connecting tools...")
        try:
            call_command('cmd_tool_connect', reset=True, verbosity=0)
            logger.info("Successfully connected tools.")
        except Exception as e:
            logger.warning(f"Error connecting tools: {e}")

        # 6. Ensure default agents exist
        logger.info("Ensuring default agents exist...")
        try:
            # Create default LLM config if it doesn't exist
            default_llm_config, _ = LLMConfig.objects.get_or_create(
                name="default-llm-config",
                defaults={"model_name": "mistral-small-latest", "temperature": 0.7, "is_default": True, "is_evaluation": False}
            )

            # Define a common schema for all agents
            default_schema = {"type": "object", "properties": {}, "additionalProperties": True}

            # Define agent roles and their class paths
            agent_definitions = {
                "mentor": {
                    "description": "Default mentor agent for testing",
                    "system_instructions": "Provide empathetic and helpful responses.",
                    "langgraph_node_class": "apps.main.agents.mentor_agent.MentorAgent",
                },
                "orchestrator": {
                    "description": "Default orchestrator agent for testing",
                    "system_instructions": "Coordinate the workflow between specialized agents.",
                    "langgraph_node_class": "apps.main.agents.orchestrator_agent.OrchestratorAgent",
                },
                "resource": {
                    "description": "Default resource agent for testing",
                    "system_instructions": "Analyze user's resources, environment and constraints.",
                    "langgraph_node_class": "apps.main.agents.resource_agent.ResourceAgent",
                },
                "engagement": {
                    "description": "Default engagement agent for testing",
                    "system_instructions": "Analyze user engagement patterns and preferences.",
                    "langgraph_node_class": "apps.main.agents.engagement_agent.EngagementAndPatternAgent",
                },
                "psychological": {
                    "description": "Default psychological agent for testing",
                    "system_instructions": "Analyze user's psychological state and needs.",
                    "langgraph_node_class": "apps.main.agents.psy_agent.PsychologicalMonitoringAgent",
                },
                "strategy": {
                    "description": "Default strategy agent for testing",
                    "system_instructions": "Formulate strategies for activity selection.",
                    "langgraph_node_class": "apps.main.agents.strategy_agent.StrategyAgent",
                },
                "wheel_activity": {
                    "description": "Default wheel activity agent for testing",
                    "system_instructions": "Generate wheel of life activities.",
                    "langgraph_node_class": "apps.main.agents.wheel_activity_agent.WheelAndActivityAgent",
                },
                "ethical": {
                    "description": "Default ethical agent for testing",
                    "system_instructions": "Validate ethical considerations of activities.",
                    "langgraph_node_class": "apps.main.agents.ethical_agent.EthicalAgent",
                },
                "dispatcher": {
                    "description": "Default dispatcher agent for testing",
                    "system_instructions": "Analyze user input and route to appropriate workflows.",
                    "langgraph_node_class": "apps.main.agents.dispatcher_agent.DispatcherAgent",
                }
            }

            # Create all agent definitions
            for role, definition in agent_definitions.items():
                # Add common fields to all agent definitions
                defaults = {
                    "description": definition["description"],
                    "system_instructions": definition["system_instructions"],
                    "input_schema": default_schema,
                    "output_schema": default_schema,
                    "state_schema": {},
                    "memory_schema": {},
                    "llm_config": default_llm_config,
                    "version": "1.0.0",
                    "is_active": True,
                    "langgraph_node_class": definition["langgraph_node_class"],
                    "processing_timeout": 30
                }

                # Create or update the agent definition
                agent, created = GenericAgent.objects.get_or_create(
                    role=role,
                    defaults=defaults
                )

                # If the agent already exists but some fields are missing or incorrect, update them
                if not created:
                    # Check if langgraph_node_class is empty or None
                    if not agent.langgraph_node_class:
                        agent.langgraph_node_class = defaults["langgraph_node_class"]
                        agent.save(update_fields=["langgraph_node_class"])
                        logger.info(f"Updated langgraph_node_class for existing GenericAgent '{role}'")

                    # Check if llm_config is None
                    if not agent.llm_config:
                        agent.llm_config = default_llm_config
                        agent.save(update_fields=["llm_config"])
                        logger.info(f"Updated llm_config for existing GenericAgent '{role}'")

                if created:
                    logger.info(f"Created default GenericAgent for role '{role}'")
        except Exception as e:
            logger.error(f"Error ensuring default GenericAgents exist: {e}", exc_info=True)

    _session_setup_complete = True
    logger.info("--- conftest.py django_db_setup (session scope) finished ---")

def clean_test_database():
    """
    Helper function to clean the test database using Django's flush command.
    Uses options appropriate for Django 5.2.
    """
    logger.info("Cleaning test database...")
    try:
        # In Django 5.2, the flush command options have changed
        # Valid options are: allow_cascade, inhibit_post_migrate, reset_sequences
        call_command('flush', reset_sequences=True, allow_cascade=True)
        logger.info("Test database cleaned successfully.")
    except Exception as e:
        logger.error(f"Error cleaning test database: {e}", exc_info=True)
        # Depending on severity, you might want to re-raise or fail the test
        # pytest.fail(f"Database cleanup failed: {e}")


@pytest.fixture(scope='class')
def clean_db_for_class(django_db_blocker):
    """
    Fixture to clean the database before each test class.
    Ensures a clean slate for tests within a class.
    """
    logger.info("--- Cleaning database before test class ---")
    with django_db_blocker.unblock():
        clean_test_database()
    yield
    logger.info("--- Database cleanup after test class ---")


# Timeout utilities for both sync and async tests
@contextmanager
def sync_timeout(seconds, error_message="Test timed out"):
    """
    Context manager for timing out synchronous code.
    Uses signal.alarm for POSIX systems and threading.Timer for Windows.
    """
    def _timeout_handler(signum, frame):
        raise TimeoutError(error_message)

    # Windows doesn't support signal.SIGALRM, use threading.Timer instead
    if os.name == 'nt':
        timer = threading.Timer(seconds, lambda: _timeout_handler(None, None))
        timer.daemon = True
        timer.start()
        try:
            yield
        finally:
            timer.cancel()
    else:
        # POSIX systems can use signal.alarm
        old_handler = signal.signal(signal.SIGALRM, _timeout_handler)
        old_alarm = signal.alarm(seconds)
        try:
            yield
        finally:
            signal.alarm(0)  # Disable the alarm
            signal.signal(signal.SIGALRM, old_handler)
            if old_alarm > 0:
                signal.alarm(old_alarm)  # Restore previous alarm if there was one

async def cancel_and_cleanup_tasks(tasks=None):
    """
    Cancel all running tasks except the current one and clean up.

    Args:
        tasks: Optional list of tasks to cancel. If None, cancels all tasks except current.
    """
    current_task = asyncio.current_task()

    if tasks is None:
        tasks = [t for t in asyncio.all_tasks() if t is not current_task]
    else:
        # Filter out current task from the provided list
        tasks = [t for t in tasks if t is not current_task and not t.done()]

    if not tasks:
        return

    logger.warning(f"Cancelling {len(tasks)} pending tasks")
    for task in tasks:
        task_name = task.get_name() if hasattr(task, 'get_name') else str(task)
        task_status = "Running" if not task.done() else (
            "Cancelled" if task.cancelled() else (
                "Done with exception" if task.exception() else "Done successfully"
            )
        )
        logger.warning(f"Cancelling task: {task_name} (Status: {task_status})")
        if not task.done():
            task.cancel()

    # Wait for tasks to acknowledge cancellation
    try:
        await asyncio.gather(*tasks, return_exceptions=True)
    except Exception as e:
        logger.error(f"Error during task cleanup: {e}")

    logger.info("All pending tasks have been cancelled")

def get_task_info(task):
    """
    Get detailed information about an asyncio task.

    Args:
        task: The asyncio task to get information about

    Returns:
        dict: Dictionary containing task information
    """
    task_name = task.get_name() if hasattr(task, 'get_name') else "Unnamed Task"

    # Get task status
    if task.done():
        if task.cancelled():
            status = "Cancelled"
        elif task.exception() is not None:
            status = f"Failed: {type(task.exception()).__name__}"
        else:
            status = "Completed"
    else:
        status = "Running"

    # Get stack frames if task is still running
    stack_info = None
    if not task.done() and hasattr(task, '_coro'):
        try:
            # This is implementation-specific and might change in future Python versions
            import inspect
            stack = inspect.getstack(task._coro) if hasattr(inspect, 'getstack') else []
            stack_info = [f"{frame.filename}:{frame.lineno} in {frame.function}"
                         for frame in stack[:5]]  # Limit to first 5 frames
        except Exception:
            stack_info = ["<stack info unavailable>"]

    return {
        "name": task_name,
        "id": id(task),
        "status": status,
        "stack": stack_info,
        "task_obj": task  # Keep reference to the actual task
    }

# Fixture for applying timeouts to tests
@pytest.fixture
def test_timeout(request):
    """
    Fixture that applies a timeout to a test.

    Usage:
        @pytest.mark.timeout(120)  # 120 seconds timeout
        def test_something(test_timeout):
            # Test code here

    Or use the default timeout (60 seconds):
        def test_something(test_timeout):
            # Test code here
    """
    # Get timeout value from marker or use default
    marker = request.node.get_closest_marker("timeout")
    timeout_seconds = marker.args[0] if marker else 60  # Default: 60 seconds

    # Store the timeout value for reporting
    request.node.user_properties.append(("timeout", timeout_seconds))

    # For async tests, we'll handle timeout in the pytest_pyfunc_call hook
    # For sync tests, we'll use the context manager here
    if not request.node.get_closest_marker("asyncio"):
        try:
            with sync_timeout(timeout_seconds, f"Test timed out after {timeout_seconds} seconds"):
                yield timeout_seconds
        except TimeoutError as e:
            logger.error(f"TIMEOUT in {request.node.nodeid}: {e}")
            # Clean up resources if needed
            pytest.fail(f"Test timed out after {timeout_seconds} seconds")
    else:
        # For async tests, just yield the timeout value
        # The actual timeout handling is in pytest_pyfunc_call
        yield timeout_seconds

# Hook to handle async test timeouts
@pytest.hookimpl(tryfirst=True)
def pytest_pyfunc_call(pyfuncitem):
    """
    Hook to handle timeouts for async tests.
    This is needed because signal-based timeouts don't work well with asyncio.
    """
    # Only handle async tests
    if not pyfuncitem.get_closest_marker("asyncio"):
        return None

    # Get timeout value from marker or use default
    marker = pyfuncitem.get_closest_marker("timeout")
    timeout_seconds = marker.args[0] if marker else 60  # Default: 60 seconds

    # Get the test function
    func = pyfuncitem.obj

    # If it's already an async function, wrap it with timeout
    if asyncio.iscoroutinefunction(func):
        async def timeout_wrapper(*args, **kwargs):
            try:
                # Use asyncio.wait_for for async functions
                return await asyncio.wait_for(func(*args, **kwargs), timeout=timeout_seconds)
            except asyncio.TimeoutError:
                logger.error(f"ASYNC TIMEOUT in {pyfuncitem.nodeid} after {timeout_seconds} seconds")
                # Clean up any pending tasks
                await cancel_and_cleanup_tasks()
                pytest.fail(f"Async test timed out after {timeout_seconds} seconds")

        # Replace the original function with our wrapped version
        pyfuncitem.obj = timeout_wrapper

    # Let pytest handle the actual call
    return None

# Task monitoring fixture
@pytest.fixture
def task_monitor():
    """
    Fixture that monitors asyncio tasks during test execution.

    This fixture tracks all asyncio tasks at the start of a test,
    compares them with tasks at the end, and logs and cancels any orphaned tasks.

    Usage:
        def test_something(task_monitor):
            # Test code here

    Returns:
        dict: A dictionary with task monitoring functions:
            - get_task_snapshot(): Get a snapshot of current tasks
            - compare_snapshots(before, after): Compare two task snapshots
            - cleanup_orphaned_tasks(before, after): Cancel orphaned tasks
    """
    # Store initial task state
    initial_tasks = {}

    # Get a snapshot of current tasks
    def get_task_snapshot():
        """
        Get a snapshot of all current asyncio tasks.

        This function safely gets all asyncio tasks, handling the case where
        there is no running event loop.

        Returns:
            dict: Dictionary of task information keyed by task ID
        """
        tasks = {}

        try:
            # Try to get the running event loop
            loop = asyncio.get_event_loop()

            # Get all tasks in the loop
            for task in asyncio.all_tasks(loop=loop):
                task_info = get_task_info(task)
                tasks[task_info["id"]] = task_info

        except RuntimeError as e:
            # Handle the case where there is no running event loop
            if "no running event loop" in str(e):
                logger.info("No running event loop found. Task monitoring will be limited.")
            else:
                # Re-raise other RuntimeErrors
                raise

        return tasks

    # Compare two task snapshots
    def compare_snapshots(before, after):
        """
        Compare two task snapshots and return new, completed, and ongoing tasks.

        Args:
            before: Task snapshot from before
            after: Task snapshot from after

        Returns:
            tuple: (new_tasks, completed_tasks, ongoing_tasks)
        """
        # Find new tasks (in after but not in before)
        new_task_ids = set(after.keys()) - set(before.keys())
        new_tasks = {task_id: after[task_id] for task_id in new_task_ids}

        # Find completed tasks (in before but not in after)
        completed_task_ids = set(before.keys()) - set(after.keys())
        completed_tasks = {task_id: before[task_id] for task_id in completed_task_ids}

        # Find ongoing tasks (in both before and after)
        ongoing_task_ids = set(before.keys()) & set(after.keys())
        ongoing_tasks = {task_id: after[task_id] for task_id in ongoing_task_ids}

        return new_tasks, completed_tasks, ongoing_tasks

    # Cleanup orphaned tasks
    async def cleanup_orphaned_tasks(before, after):
        """
        Cancel tasks that were created during the test but not completed.

        Args:
            before: Task snapshot from before the test
            after: Task snapshot from after the test

        Returns:
            int: Number of tasks cancelled
        """
        # Find orphaned tasks (created during test and still running)
        new_task_ids = set(after.keys()) - set(before.keys())
        orphaned_tasks = []

        for task_id in new_task_ids:
            task_info = after[task_id]
            if task_info["status"] == "Running":
                orphaned_tasks.append(task_info["task_obj"])

        if orphaned_tasks:
            logger.warning(f"Found {len(orphaned_tasks)} orphaned tasks. Cancelling...")
            await cancel_and_cleanup_tasks(orphaned_tasks)
            return len(orphaned_tasks)

        return 0

    # Create the monitor object
    monitor = {
        "get_task_snapshot": get_task_snapshot,
        "compare_snapshots": compare_snapshots,
        "cleanup_orphaned_tasks": cleanup_orphaned_tasks
    }

    # Get initial task snapshot
    initial_tasks = get_task_snapshot()
    logger.info(f"Task monitor initialized with {len(initial_tasks)} existing tasks")

    yield monitor

    # Try to get final task snapshot, but handle the case where there is no event loop
    try:
        final_tasks = get_task_snapshot()

        # Compare snapshots
        new_tasks, completed_tasks, ongoing_tasks = compare_snapshots(initial_tasks, final_tasks)

        # Log task statistics
        logger.info(f"Task monitor summary: {len(new_tasks)} new, {len(completed_tasks)} completed, {len(ongoing_tasks)} ongoing")

        # Log details about new tasks that are still running
        orphaned_count = 0
        for task_id, task_info in new_tasks.items():
            if task_info["status"] == "Running":
                orphaned_count += 1
                logger.warning(f"Orphaned task: {task_info['name']} (ID: {task_id})")
                if task_info["stack"]:
                    logger.warning(f"Stack trace: {task_info['stack']}")

        if orphaned_count > 0:
            logger.warning(f"Found {orphaned_count} orphaned tasks")

            # We can't use asyncio.run here because we're in a sync context
            # and the event loop might already be running
            # Instead, we'll log a warning and recommend using the fixture's cleanup function
            logger.warning("Cannot automatically cancel orphaned tasks in teardown (sync context)")
            logger.warning("Use 'await task_monitor['cleanup_orphaned_tasks'](before, after)' in your test")
    except RuntimeError as e:
        # Handle the case where there is no event loop in the teardown phase
        if "no current event loop" in str(e) or "no running event loop" in str(e):
            logger.info("No event loop available in teardown phase. Skipping task monitoring cleanup.")
        else:
            # Re-raise other RuntimeErrors
            raise

# Signal handler for runtime debugging
def setup_signal_handlers():
    """Set up signal handlers for runtime debugging."""
    if os.name != 'nt':  # Skip on Windows
        try:
            import signal

            def dump_stacks(signum, frame):
                """Dump stack traces of all threads and asyncio tasks."""
                logger.info("=== STACK TRACE DUMP (SIGUSR1) ===")

                # Dump thread stacks
                import threading
                import traceback

                logger.info("--- Thread Stacks ---")
                for thread_id, frame in sys._current_frames().items():
                    thread_name = None
                    for t in threading.enumerate():
                        if t.ident == thread_id:
                            thread_name = t.name
                            break

                    logger.info(f"Thread {thread_id} ({thread_name or 'unknown'}):")
                    logger.info(''.join(traceback.format_stack(frame)))

                # Dump asyncio task stacks
                logger.info("--- Asyncio Task Stacks ---")
                try:
                    # Try to get the running event loop
                    loop = asyncio.get_event_loop()

                    # Get all tasks in the loop
                    for task in asyncio.all_tasks(loop=loop):
                        task_info = get_task_info(task)
                        logger.info(f"Task {task_info['name']} (ID: {task_info['id']}, Status: {task_info['status']})")
                        if task_info["stack"]:
                            logger.info(f"Stack trace: {task_info['stack']}")
                except RuntimeError as e:
                    # Handle the case where there is no running event loop
                    if "no running event loop" in str(e):
                        logger.info("No running event loop found. Cannot dump asyncio tasks.")
                    else:
                        # Log other RuntimeErrors
                        logger.error(f"Error dumping asyncio tasks: {e}")

                logger.info("=== END STACK TRACE DUMP ===")

            # Register signal handler for SIGUSR1
            signal.signal(signal.SIGUSR1, dump_stacks)
            logger.info("Signal handler registered for SIGUSR1 (use 'kill -SIGUSR1 <pid>' to dump stacks)")

        except Exception as e:
            logger.warning(f"Failed to set up signal handlers: {e}")

# Set up signal handlers at module load time
setup_signal_handlers()

# Database connection tracking fixture
@pytest.fixture
def db_connection_monitor():
    """
    Fixture that monitors database connections during test execution.

    This fixture tracks all database connections at the start of a test,
    compares them with connections at the end, and logs and closes any leaked connections.

    Usage:
        def test_something(db_connection_monitor):
            # Test code here

    Returns:
        dict: A dictionary with connection monitoring functions:
            - get_connection_snapshot(): Get a snapshot of current connections
            - compare_snapshots(before, after): Compare two connection snapshots
            - cleanup_leaked_connections(before, after): Close leaked connections
            - get_connection_source(connection): Get source information for a connection
            - add_timeout_to_connections(): Add timeout parameters to all database connections
    """
    # Import the DBConnectionMonitor utility class
    from apps.main.utils.db_connection_monitor import DBConnectionMonitor

    # Create the monitor object
    monitor = {
        "get_connection_snapshot": DBConnectionMonitor.get_connection_snapshot,
        "compare_snapshots": DBConnectionMonitor.compare_snapshots,
        "cleanup_leaked_connections": DBConnectionMonitor.cleanup_leaked_connections,
        "get_connection_source": DBConnectionMonitor.get_connection_source,
        "add_timeout_to_connections": DBConnectionMonitor.add_timeout_to_connections
    }

    # Get initial connection snapshot
    initial_connections = DBConnectionMonitor.get_connection_snapshot()
    logger.info(f"DB connection monitor initialized with {sum(1 for c in initial_connections.values() if c['is_open'])} open connections")

    # Add timeout to connections
    DBConnectionMonitor.add_timeout_to_connections()

    yield monitor

    # Get final connection snapshot
    final_connections = DBConnectionMonitor.get_connection_snapshot()

    # Log connection statistics and get summary
    stats = DBConnectionMonitor.log_connection_statistics(initial_connections, final_connections)

    # Try to close leaked connections
    if stats["leaked_connections"] > 0:
        closed_count = DBConnectionMonitor.cleanup_leaked_connections(initial_connections, final_connections)
        if closed_count > 0:
            logger.info(f"Closed {closed_count} leaked connections")

# Pytest hook to add comprehensive logging for test failures
@pytest.hookimpl(tryfirst=True, hookwrapper=True)
def pytest_runtest_makereport(item: Item, call: pytest.CallInfo):
    """
    Hook to add comprehensive logging for test failures.
    Logs test outcome and details on failure.
    """
    # Execute all other hooks first to obtain the report
    outcome = yield
    report: TestReport = outcome.get_result()

    # Log test start and end
    if call.when == "setup":
        logger.info(f"--- START TEST: {item.nodeid} ---")

        # Log timeout information if available
        timeout_prop = next((prop for prop in item.user_properties if prop[0] == "timeout"), None)
        if timeout_prop:
            logger.info(f"Test timeout set to {timeout_prop[1]} seconds")

    elif call.when == "teardown":
        logger.info(f"--- END TEST: {item.nodeid} [{report.outcome.upper()}] ---")

    # Log details on failure
    if report.when == "call" and report.failed:
        logger.error(f"!!! TEST FAILED: {item.nodeid} !!!")
        logger.error(f"Outcome: {report.outcome}")
        logger.error(f"Duration: {report.duration:.2f} seconds")

        # Check if it's a timeout failure
        if "timed out after" in str(report.longrepr):
            logger.error(f"!!! TEST TIMED OUT !!!")

        if report.longrepr:
            logger.error(f"Failure Details:\n{report.longrepr}")

        # Log any custom user properties attached to the test item
        if item.user_properties:
            logger.error("User Properties:")
            for prop_name, prop_value in item.user_properties:
                logger.error(f"  {prop_name}: {prop_value}")
