# ACTIVE_FILE - 29-05-2025
"""
Benchmark management views for admin tools.

This module contains views for managing benchmark scenarios, evaluation criteria templates,
and running benchmarks through the admin interface.
"""

import json
import logging
import io
from datetime import datetime
from django.http import JsonResponse, HttpResponse, Http404, HttpResponseForbidden
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib import messages
from django.db import transaction
from django.views import View
from django.db.models import Count, Q, Avg, F
from django.core.management import call_command
from asgiref.sync import async_to_sync, sync_to_async
from channels.db import database_sync_to_async
import traceback
from decimal import Decimal
import asyncio
import uuid

# Import models and services needed for benchmarking
from apps.main.models import (
    BenchmarkScenario, BenchmarkRun, GenericAgent, BenchmarkTag,
    EvaluationCriteriaTemplate, LLMConfig
)
from apps.main.services.benchmark_manager import AgentBenchmarker
from django.utils.dateparse import parse_date
from celery import current_app as celery_app

logger = logging.getLogger(__name__)


# Helper function for permission checks
async def _check_staff_permissions(request):
    """Async-safe permission check for staff members."""
    is_authenticated = await sync_to_async(lambda: request.user.is_authenticated)()
    if not is_authenticated:
        return False
    is_active = await sync_to_async(lambda: request.user.is_active)()
    is_staff = await sync_to_async(lambda: request.user.is_staff)()
    return is_active and is_staff


def _determine_execution_type(run):
    """
    Determine if a benchmark run represents agent evaluation or workflow evaluation.

    Based on Goali architecture:
    - Agent evaluation: Tests individual agent capabilities
    - Workflow evaluation: Tests complete workflow orchestration
    """
    # Check scenario metadata for workflow type
    if run.scenario and run.scenario.metadata:
        workflow_type = run.scenario.metadata.get('workflow_type')
        if workflow_type:
            # If workflow type is specified, this is workflow evaluation
            return f"Workflow ({workflow_type})"

    # Check parameters for workflow indicators
    if run.parameters:
        # Look for workflow-specific parameters
        if 'workflow_id' in run.parameters or 'workflow_type' in run.parameters:
            return "Workflow Evaluation"

        # Look for multi-agent orchestration indicators
        if 'agent_sequence' in run.parameters or 'orchestrator' in str(run.parameters).lower():
            return "Workflow Evaluation"

    # Default to agent evaluation for individual agent testing
    return "Agent Evaluation"


def _aggregate_execution_stats(execution_sessions):
    """
    Calculate aggregated statistics from execution sessions.
    Each execution session may contain multiple runs for statistical significance.
    """
    if not execution_sessions:
        return {
            'success_rate': 0,
            'avg_semantic_score': 0,
            'token_usage': 0,
            'cost': 0,
            'chart_data': {
                'labels': [],
                'success_rates': [],
                'semantic_scores': [],
                'mean_durations': [],
                'median_durations': [],
                'llm_models': [],
                'agent_versions': []
            }
        }

    # Aggregate statistics across all execution sessions
    total_success_rate = 0
    semantic_scores = []
    total_tokens = 0
    total_cost = 0

    # Chart data
    chart_labels = []
    chart_success_rates = []
    chart_semantic_scores = []
    chart_mean_durations = []
    chart_median_durations = []
    chart_llm_models = []
    chart_agent_versions = []

    for session in execution_sessions:
        runs = session['runs']
        representative_run = session['representative_run']

        # Calculate session-level aggregates
        session_success_rate = sum(run.success_rate for run in runs) / len(runs) if runs else 0
        session_semantic_scores = [run.semantic_score for run in runs if run.semantic_score is not None]
        session_semantic_score = sum(session_semantic_scores) / len(session_semantic_scores) if session_semantic_scores else 0
        mean_duration_runs = [r for r in runs if r.mean_duration]
        session_mean_duration = sum(run.mean_duration for run in mean_duration_runs) / len(mean_duration_runs) if mean_duration_runs else 0
        median_duration_runs = [r for r in runs if r.median_duration]
        session_median_duration = sum(run.median_duration for run in median_duration_runs) / len(median_duration_runs) if median_duration_runs else 0

        # Aggregate for overall stats
        total_success_rate += session_success_rate
        if session_semantic_score > 0:
            semantic_scores.append(session_semantic_score)

        # Sum tokens and costs across all runs in session
        for run in runs:
            total_tokens += (run.total_input_tokens or 0) + (run.total_output_tokens or 0)
            total_cost += run.estimated_cost or 0

        # Add to chart data (one point per execution session)
        chart_labels.append(representative_run.execution_date.isoformat())
        chart_success_rates.append(session_success_rate)
        chart_semantic_scores.append(session_semantic_score)
        chart_mean_durations.append(session_mean_duration if session_mean_duration else 0)
        chart_median_durations.append(session_median_duration if session_median_duration else 0)
        chart_llm_models.append(representative_run.evaluator_llm_model or 'N/A')
        chart_agent_versions.append(representative_run.agent_version or 'N/A')

    # Calculate overall averages
    avg_success_rate = (total_success_rate / len(execution_sessions)) * 100 if execution_sessions else 0
    avg_semantic_score = sum(semantic_scores) / len(semantic_scores) if semantic_scores else 0

    return {
        'success_rate': avg_success_rate,
        'avg_semantic_score': avg_semantic_score,
        'token_usage': total_tokens,
        'cost': total_cost,
        'chart_data': {
            'labels': chart_labels,
            'success_rates': chart_success_rates,
            'semantic_scores': chart_semantic_scores,
            'mean_durations': chart_mean_durations,
            'median_durations': chart_median_durations,
            'llm_models': chart_llm_models,
            'agent_versions': chart_agent_versions
        }
    }


@staff_member_required
def benchmark_history(request, agent_role=None):
    """
    Admin view for benchmark execution history.

    Displays a list of benchmark executions (grouped runs) with filtering options and charts.
    Each row represents a benchmark execution session, which may contain multiple individual runs.
    Distinguishes between agent evaluation and workflow evaluation based on Goali architecture.
    """
    context = {
        'title': 'Benchmark Execution History',
        'has_permission': True,
        'site_header': 'Game of Life Admin',
        'opts': None,
        'app_label': 'admin_tools',
    }

    # Get filter parameters
    agent_role_filter = agent_role or request.GET.get('agent_role')
    scenario_filter = request.GET.get('scenario_id')
    tag_filter = request.GET.get('tag')
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')

    # Base queryset - we'll group by execution session later
    runs_query = BenchmarkRun.objects.select_related('scenario', 'agent_definition')

    # Apply filters
    if agent_role_filter:
        runs_query = runs_query.filter(scenario__agent_role=agent_role_filter)
        context['selected_agent_role'] = agent_role_filter
        # Add backward compatibility for tests expecting 'current_agent_role'
        context['current_agent_role'] = agent_role_filter

    if scenario_filter:
        runs_query = runs_query.filter(scenario_id=scenario_filter)
        context['selected_scenario_id'] = scenario_filter

    if tag_filter:
        runs_query = runs_query.filter(scenario__tags__name=tag_filter)
        context['selected_tag'] = tag_filter

    if start_date:
        start_date_obj = parse_date(start_date)
        if start_date_obj:
            runs_query = runs_query.filter(execution_date__gte=start_date_obj)
            context['start_date'] = start_date

    if end_date:
        end_date_obj = parse_date(end_date)
        if end_date_obj:
            runs_query = runs_query.filter(execution_date__lte=end_date_obj)
            context['end_date'] = end_date

    # Get all runs and group them by execution session
    # We group by scenario, agent_definition, execution_date (rounded to minute), and parameters
    # to identify runs that belong to the same benchmark execution session
    all_runs = runs_query.order_by('-execution_date')

    # Group runs by execution session
    execution_sessions = {}
    for run in all_runs:
        # Create a session key based on scenario, agent, date (rounded to minute), and key parameters
        session_key = (
            run.scenario_id,
            run.agent_definition_id,
            run.execution_date.replace(second=0, microsecond=0),  # Round to minute
            str(sorted(run.parameters.items()) if run.parameters else "")  # Consistent parameter representation
        )

        if session_key not in execution_sessions:
            execution_sessions[session_key] = {
                'runs': [],
                'representative_run': run,  # Use first run as representative
                'execution_type': _determine_execution_type(run)
            }
        execution_sessions[session_key]['runs'].append(run)

    # Convert to list and limit to 50 most recent execution sessions
    execution_list = list(execution_sessions.values())
    execution_list.sort(key=lambda x: x['representative_run'].execution_date, reverse=True)
    executions = execution_list[:50]  # Limit to 50 most recent execution sessions

    # Get available agent roles for filter dropdown
    agent_roles = GenericAgent.objects.values_list('role', flat=True).distinct().order_by('role')
    context['agent_roles'] = agent_roles

    # Get available scenarios for filter dropdown
    available_scenarios = BenchmarkScenario.objects.annotate(
        run_count=Count('runs')
    ).order_by('name')
    context['available_scenarios'] = available_scenarios

    # Get available tags for filter dropdown
    tags = BenchmarkTag.objects.all().order_by('name')
    context['tags'] = tags

    # Calculate aggregated statistics from execution sessions
    stats = _aggregate_execution_stats(execution_list)
    context['success_rate'] = stats['success_rate']
    context['avg_semantic_score'] = stats['avg_semantic_score']
    context['token_usage'] = stats['token_usage']
    context['cost'] = stats['cost']

    # Chart data from aggregated stats
    chart_data = stats['chart_data']
    context['chart_labels_json'] = json.dumps(chart_data['labels'])
    context['success_rates_json'] = json.dumps(chart_data['success_rates'])
    context['semantic_scores_json'] = json.dumps(chart_data['semantic_scores'])
    context['mean_durations_json'] = json.dumps(chart_data['mean_durations'])
    context['median_durations_json'] = json.dumps(chart_data['median_durations'])
    context['llm_models_json'] = json.dumps(chart_data['llm_models'])
    context['agent_versions_json'] = json.dumps(chart_data['agent_versions'])

    # Prepare execution sessions data for the table (one row per execution session)
    executions_data_for_json = []
    for session in execution_list:
        runs = session['runs']
        representative_run = session['representative_run']
        execution_type = session['execution_type']

        # Calculate session-level aggregates
        session_success_rate = sum(run.success_rate for run in runs) / len(runs)
        session_semantic_scores = [run.semantic_score for run in runs if run.semantic_score is not None]
        session_semantic_score = sum(session_semantic_scores) / len(session_semantic_scores) if session_semantic_scores else None
        session_mean_duration = sum(run.mean_duration for run in runs if run.mean_duration) / len([r for r in runs if r.mean_duration]) if any(r.mean_duration for r in runs) else None
        session_total_tokens_in = sum(run.total_input_tokens or 0 for run in runs)
        session_total_tokens_out = sum(run.total_output_tokens or 0 for run in runs)
        session_total_cost = sum(run.estimated_cost or 0 for run in runs)

        executions_data_for_json.append({
            'id': str(representative_run.id),  # Use representative run ID
            'session_id': f"session_{len(executions_data_for_json)}",  # Unique session identifier
            'scenario_name': representative_run.scenario.name,
            'agent_role': representative_run.scenario.agent_role,
            'execution_type': execution_type,  # Agent vs Workflow evaluation
            'runs_count': len(runs),  # Number of runs in this execution session
            'agent_llm_model_name': representative_run.agent_llm_model_name,
            'llm_temperature': representative_run.llm_temperature,
            'execution_date': representative_run.execution_date.isoformat(),
            'mean_duration': session_mean_duration,
            'success_rate': session_success_rate,
            'semantic_score': session_semantic_score,
            'total_input_tokens': session_total_tokens_in,
            'total_output_tokens': session_total_tokens_out,
            'estimated_cost': str(session_total_cost) if session_total_cost > 0 else None,
        })

    context['benchmark_runs_json'] = json.dumps(executions_data_for_json)

    # Add 'runs' context for backward compatibility with tests
    # Convert execution sessions to a queryset-like structure
    runs_for_context = []
    for session in execution_list:
        # Use the representative run from each session
        runs_for_context.append(session['representative_run'])
    context['runs'] = runs_for_context

    # Empty dimension data for initial load (will be populated by API for filtered views)
    context['dimension_data_json'] = json.dumps({})

    # Determine if this is a scenario-specific view
    context['is_scenario_view'] = bool(scenario_filter)

    return render(request, 'admin_tools/benchmark_history.html', context)


@staff_member_required
def benchmark_management(request):
    """Admin view for benchmark scenario and evaluation criteria management."""
    context = {
        'title': 'Benchmark Management',
        'has_permission': True,
        'site_header': 'Game of Life Admin',
        'opts': None,
        'app_label': 'admin_tools',
    }

    try:
        # Get available scenarios with prefetched tags
        scenarios = async_to_sync(sync_to_async(list))(
            BenchmarkScenario.objects.filter(is_latest=True)
                                     .prefetch_related('tags')
                                     .order_by('name')
        )
        context['scenarios'] = scenarios

        # Get available tags for filtering
        tags = async_to_sync(sync_to_async(list))(
            BenchmarkTag.objects.all().order_by('name')
        )
        context['tags'] = tags

        # Get available evaluation criteria templates
        templates = async_to_sync(sync_to_async(list))(
            EvaluationCriteriaTemplate.objects.all().order_by('name')
        )
        context['templates'] = templates

        # Get unique workflow types from scenario metadata
        workflow_types = set()
        workflow_stats = []

        for scenario in scenarios:
            workflow_type = scenario.metadata.get('workflow_type')
            if workflow_type:
                workflow_types.add(workflow_type)

        # Calculate stats for each workflow type
        for workflow_type in workflow_types:
            # Count scenarios of this type
            type_scenarios = [s for s in scenarios if s.metadata.get('workflow_type') == workflow_type]
            scenario_count = len(type_scenarios)
            active_count = len([s for s in type_scenarios if s.is_active])

            # Add to stats
            workflow_stats.append({
                'name': workflow_type,
                'scenario_count': scenario_count,
                'active_count': active_count,
            })

        context['workflow_types'] = sorted(list(workflow_types))
        context['workflow_stats'] = workflow_stats

        # Get available agent roles
        agent_roles = async_to_sync(sync_to_async(list))(
            GenericAgent.objects.values_list('role', flat=True).distinct().order_by('role')
        )
        context['agent_roles'] = agent_roles

    except Exception as e:
        # Handle potential errors during data fetching
        context['error'] = f"Error loading management data: {e}"
        context['scenarios'] = []
        context['tags'] = []
        context['templates'] = []
        context['workflow_types'] = []
        context['workflow_stats'] = []
        context['agent_roles'] = []
        logger.error("Error fetching benchmark management data", exc_info=True)

    return render(request, 'admin_tools/benchmark_management.html', context)


class WorkflowTypeView(View):
    """API View for operations on workflow types."""

    def _check_permissions(self, request):
        """Permission check."""
        return (request.user.is_authenticated and
                request.user.is_active and
                request.user.is_staff)

    def dispatch(self, request, *args, **kwargs):
        # Perform permission check before calling get or post
        if not self._check_permissions(request):
            return HttpResponseForbidden("You do not have permission to access this resource.")
        return super().dispatch(request, *args, **kwargs)

    def get(self, request):
        """Get workflow types and their statistics."""
        try:
            # Get all scenarios with prefetched tags
            scenarios = list(BenchmarkScenario.objects.filter(is_latest=True).prefetch_related('tags'))

            # Extract unique workflow types
            workflow_types = set()
            for scenario in scenarios:
                workflow_type = scenario.metadata.get('workflow_type')
                if workflow_type:
                    workflow_types.add(workflow_type)

            # Calculate stats for each workflow type
            workflow_stats = []
            for workflow_type in sorted(workflow_types):
                # Count scenarios of this type
                type_scenarios = [s for s in scenarios if s.metadata.get('workflow_type') == workflow_type]
                scenario_count = len(type_scenarios)
                active_count = len([s for s in type_scenarios if s.is_active])

                # Get scenario IDs for this workflow type
                scenario_ids = [s.id for s in type_scenarios]

                # Get recent runs stats
                runs = BenchmarkRun.objects.filter(scenario_id__in=scenario_ids).order_by('-execution_date')
                recent_runs = runs[:20]  # Limit to 20 most recent runs

                # Calculate success rate and semantic score
                success_rate = runs.aggregate(Avg('success_rate'))['success_rate__avg'] or 0
                semantic_score = runs.aggregate(Avg('semantic_score'))['semantic_score__avg'] or 0

                run_stats = {
                    'recent_run_count': recent_runs.count(),
                    'success_rate': success_rate,
                    'semantic_score': semantic_score
                }

                # Add to stats
                workflow_stats.append({
                    'name': workflow_type,
                    'scenario_count': scenario_count,
                    'active_count': active_count,
                    'recent_run_count': run_stats['recent_run_count'],
                    'success_rate': run_stats['success_rate'],
                    'semantic_score': run_stats['semantic_score']
                })

            return JsonResponse({'workflow_types': sorted(list(workflow_types)), 'stats': workflow_stats})

        except Exception as e:
            logger.error("Error in WorkflowTypeView.get", exc_info=True)
            return JsonResponse({'error': str(e)}, status=500)


class BenchmarkScenarioView(View):
    """API View for CRUD operations on benchmark scenarios."""

    def _check_permissions(self, request):
        """Permission check."""
        return (request.user.is_authenticated and
                request.user.is_active and
                request.user.is_staff)

    def dispatch(self, request, *args, **kwargs):
        # Perform permission check before calling get or post
        if not self._check_permissions(request):
            return HttpResponseForbidden("You do not have permission to access this resource.")
        return super().dispatch(request, *args, **kwargs)

    def get(self, request, scenario_id=None):
        """
        Get benchmark scenario details (if scenario_id provided) or list scenarios with filtering.
        Accepts query parameters: agent_role, tag, workflow_type, is_active.
        """
        try:
            if scenario_id:
                # Get a specific scenario
                scenario = get_object_or_404(
                    BenchmarkScenario.objects.prefetch_related('tags'),
                    id=scenario_id
                )

                # Convert to dict for JSON response
                scenario_data = {
                    'id': scenario.id,
                    'name': scenario.name,
                    'description': scenario.description,
                    'agent_role': scenario.agent_role,
                    'input_data': scenario.input_data,
                    'metadata': scenario.metadata,
                    'is_active': scenario.is_active,
                    'version': scenario.version,
                    'tags': [{'id': tag.id, 'name': tag.name} for tag in scenario.tags.all()],
                    'created_at': scenario.created_at.isoformat(),
                    'updated_at': scenario.updated_at.isoformat() if scenario.updated_at else None,
                }

                return JsonResponse({'scenario': scenario_data})
            else:
                # Get filtered scenarios
                agent_role = request.GET.get('agent_role')
                tag_param = request.GET.get('tag')
                workflow_type = request.GET.get('workflow_type')
                is_active = request.GET.get('is_active')

                # Start with all latest scenarios
                query = BenchmarkScenario.objects.filter(is_latest=True).prefetch_related('tags')

                # Apply filters if provided
                if agent_role:
                    query = query.filter(agent_role=agent_role)
                if tag_param:
                    # Handle both tag ID and tag name
                    try:
                        # Try to parse as integer (tag ID)
                        tag_id = int(tag_param)
                        query = query.filter(tags__id=tag_id)
                    except ValueError:
                        # If not an integer, treat as tag name
                        query = query.filter(tags__name=tag_param)
                if is_active is not None:
                    is_active_bool = is_active.lower() == 'true'
                    query = query.filter(is_active=is_active_bool)

                # Filter by workflow_type in metadata
                if workflow_type:
                    # This is a bit tricky with JSONField
                    # We need to filter scenarios where metadata->>'workflow_type' = workflow_type
                    query = query.filter(metadata__workflow_type=workflow_type)

                scenarios = list(query.order_by('name'))

                # Convert to list of dicts for JSON response
                scenarios_data = []
                for scenario in scenarios:
                    scenarios_data.append({
                        'id': scenario.id,
                        'name': scenario.name,
                        'description': scenario.description,
                        'agent_role': scenario.agent_role,
                        'workflow_type': scenario.metadata.get('workflow_type', 'N/A'),
                        'is_active': scenario.is_active,
                        'version': scenario.version,
                        'tags': [{'id': tag.id, 'name': tag.name} for tag in scenario.tags.all()],
                        'created_at': scenario.created_at.isoformat(),
                    })

                return JsonResponse({'scenarios': scenarios_data})

        except Exception as e:
            logger.error("Error in BenchmarkScenarioView.get", exc_info=True)
            return JsonResponse({'error': str(e)}, status=500)

    def post(self, request):
        """Create a new benchmark scenario."""
        try:
            data = json.loads(request.body)

            # Validate required fields
            required_fields = ['name', 'description', 'agent_role', 'input_data', 'metadata']
            for field in required_fields:
                if field not in data:
                    return JsonResponse({'error': f"Missing required field: {field}"}, status=400)

            # Create the scenario
            with transaction.atomic():
                # Create the scenario
                scenario = BenchmarkScenario.objects.create(
                    name=data['name'],
                    description=data['description'],
                    agent_role=data['agent_role'],
                    input_data=data['input_data'],
                    metadata=data['metadata'],
                    is_active=data.get('is_active', True),
                    version=1,  # Initial version
                    is_latest=True,
                )

                # Add tags if provided
                if 'tags' in data and isinstance(data['tags'], list):
                    for tag_name in data['tags']:
                        if isinstance(tag_name, str) and tag_name.strip():
                            tag, _ = BenchmarkTag.objects.get_or_create(name=tag_name.strip())
                            scenario.tags.add(tag)

            return JsonResponse({
                'success': True,
                'id': scenario.id,
                'message': f"Scenario '{scenario.name}' created successfully."
            })

        except json.JSONDecodeError:
            return JsonResponse({'error': "Invalid JSON body"}, status=400)
        except Exception as e:
            logger.error("Error in BenchmarkScenarioView.post", exc_info=True)
            return JsonResponse({'error': str(e)}, status=500)

    def put(self, request, scenario_id):
        """Update an existing benchmark scenario."""
        if not scenario_id:
            return JsonResponse({'error': "Scenario ID is required"}, status=400)

        try:
            data = json.loads(request.body)

            with transaction.atomic():
                # Get the scenario
                scenario = get_object_or_404(BenchmarkScenario, id=scenario_id)

                # Update fields
                if 'name' in data:
                    scenario.name = data['name']
                if 'description' in data:
                    scenario.description = data['description']
                if 'agent_role' in data:
                    scenario.agent_role = data['agent_role']
                if 'input_data' in data:
                    scenario.input_data = data['input_data']
                if 'metadata' in data:
                    scenario.metadata = data['metadata']
                if 'is_active' in data:
                    scenario.is_active = data['is_active']

                # Increment version if major changes
                if any(field in data for field in ['input_data', 'metadata']):
                    # Simple version increment for now
                    scenario.version += 1

                scenario.save()

                # Update tags if provided
                if 'tags' in data and isinstance(data['tags'], list):
                    # Clear existing tags
                    scenario.tags.clear()

                    # Add new tags
                    for tag_name in data['tags']:
                        if isinstance(tag_name, str) and tag_name.strip():
                            tag, _ = BenchmarkTag.objects.get_or_create(name=tag_name.strip())
                            scenario.tags.add(tag)

            return JsonResponse({
                'success': True,
                'id': scenario.id,
                'message': f"Scenario '{scenario.name}' updated successfully."
            })

        except json.JSONDecodeError:
            return JsonResponse({'error': "Invalid JSON body"}, status=400)
        except Exception as e:
            logger.error("Error in BenchmarkScenarioView.put", exc_info=True)
            return JsonResponse({'error': str(e)}, status=500)

    def delete(self, request, scenario_id):
        """Delete a benchmark scenario."""
        if not scenario_id:
            return JsonResponse({'error': "Scenario ID is required"}, status=400)

        try:
            scenario = get_object_or_404(BenchmarkScenario, id=scenario_id)
            scenario_name = scenario.name
            scenario.delete()

            return JsonResponse({
                'success': True,
                'message': f"Scenario '{scenario_name}' deleted successfully."
            })

        except Exception as e:
            logger.error("Error in BenchmarkScenarioView.delete", exc_info=True)
            return JsonResponse({'error': str(e)}, status=500)

    def patch(self, request):
        """Batch update scenarios (e.g., add/remove tags, activate/deactivate)."""
        try:
            data = json.loads(request.body)

            # Validate required fields
            if 'scenario_ids' not in data or not isinstance(data['scenario_ids'], list):
                return JsonResponse({'error': "Missing or invalid scenario_ids"}, status=400)

            scenario_ids = data['scenario_ids']

            # Check what operation to perform
            if 'add_tag' in data:
                # Add tag to scenarios
                tag_name = data['add_tag']

                with transaction.atomic():
                    # Get or create the tag
                    tag, _ = BenchmarkTag.objects.get_or_create(name=tag_name.strip())

                    # Add tag to each scenario
                    scenarios = BenchmarkScenario.objects.filter(id__in=scenario_ids)
                    for scenario in scenarios:
                        scenario.tags.add(tag)

                    count = scenarios.count()

                return JsonResponse({
                    'success': True,
                    'message': f"Added tag '{tag_name}' to {count} scenarios."
                })

            elif 'remove_tag' in data:
                # Remove tag from scenarios
                tag_name = data['remove_tag']

                with transaction.atomic():
                    # Get the tag
                    try:
                        tag = BenchmarkTag.objects.get(name=tag_name.strip())
                    except BenchmarkTag.DoesNotExist:
                        count = 0
                    else:
                        # Remove tag from each scenario
                        scenarios = BenchmarkScenario.objects.filter(id__in=scenario_ids)
                        for scenario in scenarios:
                            scenario.tags.remove(tag)

                        count = scenarios.count()

                return JsonResponse({
                    'success': True,
                    'message': f"Removed tag '{tag_name}' from {count} scenarios."
                })

            elif 'set_active' in data:
                # Set active status
                is_active = data['set_active']

                with transaction.atomic():
                    count = BenchmarkScenario.objects.filter(id__in=scenario_ids).update(is_active=is_active)

                status = "activated" if is_active else "deactivated"
                return JsonResponse({
                    'success': True,
                    'message': f"{count} scenarios {status}."
                })

            else:
                return JsonResponse({'error': "No valid operation specified"}, status=400)

        except json.JSONDecodeError:
            return JsonResponse({'error': "Invalid JSON body"}, status=400)
        except Exception as e:
            logger.error("Error in BenchmarkScenarioView.patch", exc_info=True)
            return JsonResponse({'error': str(e)}, status=500)


class EvaluationCriteriaTemplateView(View):
    """API View for CRUD operations on evaluation criteria templates."""

    def _check_permissions(self, request):
        """Permission check."""
        return (request.user.is_authenticated and
                request.user.is_active and
                request.user.is_staff)

    def dispatch(self, request, *args, **kwargs):
        # Perform permission check before calling get or post
        if not self._check_permissions(request):
            return HttpResponseForbidden("You do not have permission to access this resource.")
        return super().dispatch(request, *args, **kwargs)

    def get(self, request, template_id=None):
        """
        Get evaluation criteria template details (if template_id provided) or list templates.
        """
        try:
            if template_id:
                # Get a specific template
                template = get_object_or_404(EvaluationCriteriaTemplate, id=template_id)

                # Convert to dict for JSON response
                template_data = {
                    'id': template.id,
                    'name': template.name,
                    'description': template.description,
                    'workflow_type': template.workflow_type,
                    'category': template.category,
                    'criteria': template.criteria,
                    'contextual_criteria': template.contextual_criteria,
                    'variable_ranges': template.variable_ranges,
                    'is_active': template.is_active,
                    'created_at': template.created_at.isoformat(),
                    'updated_at': template.updated_at.isoformat(),
                }

                return JsonResponse(template_data)
            else:
                # Get filter parameters
                workflow_type = request.GET.get('workflow_type', '')
                category = request.GET.get('category', '')
                status = request.GET.get('status', '')

                # Get all templates with filtering
                queryset = EvaluationCriteriaTemplate.objects.all()

                if workflow_type:
                    queryset = queryset.filter(workflow_type=workflow_type)
                if category:
                    queryset = queryset.filter(category=category)
                if status:
                    is_active = status == 'active'
                    queryset = queryset.filter(is_active=is_active)

                templates = list(queryset.order_by('name'))

                # Convert to list of dicts for JSON response
                templates_data = []
                for template in templates:
                    templates_data.append({
                        'id': template.id,
                        'name': template.name,
                        'description': template.description,
                        'workflow_type': template.workflow_type,
                        'category': template.category,
                        'is_active': template.is_active,
                        'created_at': template.created_at.isoformat(),
                        'updated_at': template.updated_at.isoformat(),
                    })

                return JsonResponse({'templates': templates_data})

        except Exception as e:
            logger.error("Error in EvaluationCriteriaTemplateView.get", exc_info=True)
            return JsonResponse({'error': str(e)}, status=500)

    def post(self, request):
        """Create a new evaluation criteria template."""
        try:
            data = json.loads(request.body)

            # Validate required fields
            required_fields = ['name', 'criteria']
            for field in required_fields:
                if field not in data:
                    return JsonResponse({'error': f"Missing required field: {field}"}, status=400)

            # Create the template
            template = EvaluationCriteriaTemplate.objects.create(
                name=data['name'],
                description=data.get('description', ''),
                workflow_type=data.get('workflow_type', ''),
                category=data.get('category', 'quality'),
                criteria=data['criteria'],
                contextual_criteria=data.get('contextual_criteria', {}),
                variable_ranges=data.get('variable_ranges', {}),
                is_active=data.get('is_active', True)
            )

            return JsonResponse({
                'success': True,
                'id': template.id,
                'message': f"Template '{template.name}' created successfully."
            })

        except json.JSONDecodeError:
            return JsonResponse({'error': "Invalid JSON body"}, status=400)
        except Exception as e:
            logger.error("Error in EvaluationCriteriaTemplateView.post", exc_info=True)
            return JsonResponse({'error': str(e)}, status=500)

    def put(self, request, template_id):
        """Update an existing evaluation criteria template."""
        if not template_id:
            return JsonResponse({'error': "Template ID is required"}, status=400)

        try:
            data = json.loads(request.body)

            template = get_object_or_404(EvaluationCriteriaTemplate, id=template_id)

            # Update fields
            if 'name' in data:
                template.name = data['name']
            if 'description' in data:
                template.description = data['description']
            if 'workflow_type' in data:
                template.workflow_type = data['workflow_type']
            if 'category' in data:
                template.category = data['category']
            if 'criteria' in data:
                template.criteria = data['criteria']
            if 'contextual_criteria' in data:
                template.contextual_criteria = data['contextual_criteria']
            if 'variable_ranges' in data:
                template.variable_ranges = data['variable_ranges']
            if 'is_active' in data:
                template.is_active = data['is_active']

            template.save()

            return JsonResponse({
                'success': True,
                'id': template.id,
                'message': f"Template '{template.name}' updated successfully."
            })

        except json.JSONDecodeError:
            return JsonResponse({'error': "Invalid JSON body"}, status=400)
        except Exception as e:
            logger.error("Error in EvaluationCriteriaTemplateView.put", exc_info=True)
            return JsonResponse({'error': str(e)}, status=500)

    def delete(self, request, template_id):
        """Delete an evaluation criteria template."""
        if not template_id:
            return JsonResponse({'error': "Template ID is required"}, status=400)

        try:
            template = get_object_or_404(EvaluationCriteriaTemplate, id=template_id)
            template_name = template.name
            template.delete()

            return JsonResponse({
                'success': True,
                'message': f"Template '{template_name}' deleted successfully."
            })

        except Exception as e:
            logger.error("Error in EvaluationCriteriaTemplateView.delete", exc_info=True)
            return JsonResponse({'error': str(e)}, status=500)


class BenchmarkValidationView(View):
    """API View for validating benchmark scenarios."""

    async def _check_permissions(self, request):
        """Async-safe permission check."""
        is_authenticated = await sync_to_async(lambda: request.user.is_authenticated)()
        if not is_authenticated:
            return False
        is_active = await sync_to_async(lambda: request.user.is_active)()
        is_staff = await sync_to_async(lambda: request.user.is_staff)()
        return is_active and is_staff

    async def dispatch(self, request, *args, **kwargs):
        # Perform permission check before calling get or post
        if not await self._check_permissions(request):
            return HttpResponseForbidden("You do not have permission to access this resource.")
        return await super().dispatch(request, *args, **kwargs)

    async def post(self, request, scenario_id=None):
        """
        Validate benchmark scenarios.
        If scenario_id is provided, validate that specific scenario.
        Otherwise, validate scenarios specified in the request body.
        """
        try:
            # Parse request body if present
            data = {}
            if request.body:
                try:
                    data = json.loads(request.body)
                except json.JSONDecodeError:
                    return JsonResponse({'error': "Invalid JSON body"}, status=400)

            if scenario_id:
                # Validate a single scenario
                @database_sync_to_async
                def _validate_scenario():
                    # Get the scenario
                    scenario = get_object_or_404(BenchmarkScenario, id=scenario_id)

                    # Run validation
                    output = io.StringIO()
                    call_command('validate_benchmarks_v2',
                                '--scenario-id', str(scenario_id),
                                '--validate-structure',
                                '--validate-templates',
                                stdout=output)

                    return {
                        'scenario_id': scenario_id,
                        'scenario_name': scenario.name,
                        'validation_output': output.getvalue()
                    }

                result = await _validate_scenario()

                return JsonResponse({
                    'success': True,
                    'result': result
                })
            else:
                # Validate multiple scenarios
                scenario_ids = data.get('scenario_ids', [])

                if not scenario_ids:
                    return JsonResponse({'error': "No scenario IDs provided"}, status=400)

                @database_sync_to_async
                def _validate_scenarios():
                    results = []
                    for sid in scenario_ids:
                        try:
                            # Get the scenario
                            scenario = BenchmarkScenario.objects.get(id=sid)

                            # Run validation
                            output = io.StringIO()
                            call_command('validate_benchmarks_v2',
                                        '--scenario-id', str(sid),
                                        '--validate-structure',
                                        '--validate-templates',
                                        stdout=output)

                            results.append({
                                'scenario_id': sid,
                                'scenario_name': scenario.name,
                                'validation_output': output.getvalue()
                            })
                        except BenchmarkScenario.DoesNotExist:
                            results.append({
                                'scenario_id': sid,
                                'scenario_name': 'Unknown',
                                'validation_output': 'Error: Scenario not found'
                            })
                        except Exception as e:
                            results.append({
                                'scenario_id': sid,
                                'scenario_name': 'Error',
                                'validation_output': f'Error validating scenario: {str(e)}'
                            })
                    return results

                results = await _validate_scenarios()

                return JsonResponse({
                    'success': True,
                    'results': results
                })

        except Exception as e:
            logger.error("Error in BenchmarkValidationView.post", exc_info=True)
            return JsonResponse({'error': str(e)}, status=500)


async def run_all_benchmarks_view(request):
    """
    API endpoint to trigger a Celery task that runs all active benchmark scenarios.
    Accepts POST requests with JSON body containing 'runs' and 'semantic_evaluation'.
    """
    # 1. Permission Check
    if not await _check_staff_permissions(request):
        return JsonResponse({'error': 'Permission denied.'}, status=403)

    # 2. Handle POST request
    if request.method != 'POST':
        return JsonResponse({'error': 'Only POST method is allowed.'}, status=405)

    # 3. Parse and validate parameters
    try:
        data = json.loads(request.body)
        runs = int(data.get('runs', 3))  # Default to 3 runs
        semantic_evaluation = bool(data.get('semantic_evaluation', True))  # Default to True
        agent_version = data.get('agent_version', 'latest')  # Default to 'latest'
        llm_config_id = data.get('llm_config_id')  # Optional
        evaluation_llm_config_id = data.get('evaluation_llm_config_id')  # Optional

        # Validate runs
        if runs < 1 or runs > 10:
            return JsonResponse({'error': 'Number of runs must be between 1 and 10.'}, status=400)

        # Prepare parameters
        params = {
            'runs': runs,
            'semantic_evaluation': semantic_evaluation,
            'agent_version': agent_version,
        }

        # Add optional parameters if provided
        if llm_config_id:
            params['llm_config_id'] = llm_config_id
        if evaluation_llm_config_id:
            params['evaluation_llm_config_id'] = evaluation_llm_config_id

    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON in request body.'}, status=400)
    except ValueError:
        return JsonResponse({'error': 'Invalid parameter value.'}, status=400)

    # 4. Trigger Celery Task
    try:
        # Assume the task is defined in 'apps.main.tasks.benchmark_tasks'
        # The task name follows the pattern: app_label.tasks.module_name.task_function_name
        # Adjust if the task location/name is different.
        task_signature = 'apps.main.tasks.benchmark_tasks.run_all_benchmarks_task'
        task = celery_app.send_task(task_signature, args=[params])
        logger.info(f"Dispatched run_all_benchmarks_task with params: {params}. Task ID: {task.id}")

        # 5. Return Success Response
        return JsonResponse({
            'success': True,
            'task_id': task.id,
            'message': f'Task to run all benchmarks initiated successfully (Task ID: {task.id}).'
        })

    except Exception as e:
        logger.error(f"Error dispatching run_all_benchmarks_task: {str(e)}", exc_info=True)
        return JsonResponse({'error': f'Error dispatching task: {str(e)}'}, status=500)


@staff_member_required
def export_scenarios(request):
    """Export BenchmarkScenario objects to a JSON file."""
    try:
        # Get filtered scenarios
        agent_role = request.GET.get('agent_role')
        tag_name = request.GET.get('tag')
        workflow_type = request.GET.get('workflow_type')
        is_active = request.GET.get('is_active')

        # Start with all latest scenarios
        query = BenchmarkScenario.objects.filter(is_latest=True).prefetch_related('tags')

        # Apply filters if provided
        if agent_role:
            query = query.filter(agent_role=agent_role)
        if tag_name:
            query = query.filter(tags__name=tag_name)
        if is_active is not None:
            is_active_bool = is_active.lower() == 'true'
            query = query.filter(is_active=is_active_bool)

        # Filter by workflow_type in metadata
        if workflow_type:
            query = query.filter(metadata__workflow_type=workflow_type)

        scenarios = list(query.order_by('name'))

        # Convert scenarios to JSON-serializable format
        scenarios_data = []
        for scenario in scenarios:
            # Get tags as a list of names
            tags = [tag.name for tag in scenario.tags.all()]

            scenarios_data.append({
                'name': scenario.name,
                'description': scenario.description,
                'agent_role': scenario.agent_role,
                'input_data': scenario.input_data,
                'metadata': scenario.metadata,
                'is_active': scenario.is_active,
                'version': scenario.version,
                'tags': tags,
            })

        # Create response with JSON file
        response = HttpResponse(
            json.dumps(scenarios_data, indent=2),
            content_type='application/json'
        )
        response['Content-Disposition'] = 'attachment; filename="benchmark_scenarios.json"'
        return response

    except Exception as e:
        messages.error(request, f"Error exporting scenarios: {e}")
        logger.error("Error exporting benchmark scenarios", exc_info=True)
        return redirect('game_of_life_admin:benchmark_dashboard')


@staff_member_required
def import_scenarios(request):
    """Imports BenchmarkScenario objects from an uploaded JSON file."""
    if request.method != 'POST':
        messages.error(request, "Invalid request method.")
        return redirect('game_of_life_admin:benchmark_dashboard')

    if 'scenario_file' not in request.FILES:
        messages.error(request, "No scenario file uploaded.")
        return redirect('game_of_life_admin:benchmark_dashboard')

    scenario_file = request.FILES['scenario_file']

    if not scenario_file.name.endswith('.json'):
        messages.error(request, "Invalid file type. Please upload a JSON file.")
        return redirect('game_of_life_admin:benchmark_dashboard')

    try:
        # Parse JSON file
        scenarios_data = json.loads(scenario_file.read().decode('utf-8'))

        # Validate data structure
        if not isinstance(scenarios_data, list):
            raise ValueError("Invalid JSON format. Expected a list of scenarios.")

        # Import scenarios within a transaction
        with transaction.atomic():
            imported_count = 0
            updated_count = 0
            skipped_count = 0

            for scenario_data in scenarios_data:
                # Validate required fields
                required_fields = ['name', 'description', 'agent_role', 'input_data', 'metadata']
                for field in required_fields:
                    if field not in scenario_data:
                        raise ValueError(f"Missing required field '{field}' in scenario: {scenario_data.get('name', 'Unknown')}")

                # Check if scenario already exists
                existing = BenchmarkScenario.objects.filter(
                    name=scenario_data['name'],
                    is_latest=True
                ).first()

                if existing:
                    # Update existing scenario
                    existing.description = scenario_data['description']
                    existing.agent_role = scenario_data['agent_role']
                    existing.input_data = scenario_data['input_data']
                    existing.metadata = scenario_data['metadata']
                    existing.is_active = scenario_data.get('is_active', True)

                    # Increment version (version is an integer field)
                    existing.version = existing.version + 1

                    existing.save()

                    # Update tags
                    if 'tags' in scenario_data and isinstance(scenario_data['tags'], list):
                        existing.tags.clear()
                        for tag_name in scenario_data['tags']:
                            if isinstance(tag_name, str) and tag_name.strip():
                                tag, _ = BenchmarkTag.objects.get_or_create(name=tag_name.strip())
                                existing.tags.add(tag)

                    updated_count += 1
                else:
                    # Create new scenario
                    new_scenario = BenchmarkScenario.objects.create(
                        name=scenario_data['name'],
                        description=scenario_data['description'],
                        agent_role=scenario_data['agent_role'],
                        input_data=scenario_data['input_data'],
                        metadata=scenario_data['metadata'],
                        is_active=scenario_data.get('is_active', True),
                        version=int(float(scenario_data.get('version', 1))) if scenario_data.get('version') else 1,
                        is_latest=True,
                    )

                    # Add tags
                    if 'tags' in scenario_data and isinstance(scenario_data['tags'], list):
                        for tag_name in scenario_data['tags']:
                            if isinstance(tag_name, str) and tag_name.strip():
                                tag, _ = BenchmarkTag.objects.get_or_create(name=tag_name.strip())
                                new_scenario.tags.add(tag)

                    imported_count += 1

        # Show success message
        if imported_count > 0 or updated_count > 0:
            messages.success(
                request,
                f"Successfully imported {imported_count} new scenarios and updated {updated_count} existing scenarios."
            )
        else:
            messages.warning(request, "No scenarios were imported or updated.")

        return redirect('game_of_life_admin:benchmark_management')

    except json.JSONDecodeError:
        messages.error(request, "Invalid JSON file format.")
        logger.warning("Failed to import scenarios: Invalid JSON format.")
    except ValueError as e:
        messages.error(request, f"Invalid data in JSON file: {e}")
        logger.warning(f"Failed to import scenarios: Invalid data - {e}")
    except Exception as e:
        # Catch errors from the transaction rollback or other unexpected issues
        messages.error(request, f"An error occurred during import: {e}")
        logger.error("Error importing benchmark scenarios", exc_info=True)

    return redirect('game_of_life_admin:benchmark_dashboard')


class UserProfileView(View):
    """API View for fetching user profiles for benchmarking."""

    def _check_permissions(self, request):
        """Permission check."""
        return (request.user.is_authenticated and
                request.user.is_active and
                request.user.is_staff)

    def dispatch(self, request, *args, **kwargs):
        # Perform permission check before calling get
        if not self._check_permissions(request):
            return HttpResponseForbidden("You do not have permission to access this resource.")
        return super().dispatch(request, *args, **kwargs)

    def get(self, request):
        """Get user profiles with optional filtering."""
        try:
            from apps.user.models import UserProfile

            # Get filter parameters
            is_real_filter = request.GET.get('is_real')

            # Base queryset with related data
            profiles_query = UserProfile.objects.select_related(
                'demographics', 'trust_level'
            ).prefetch_related('trait_inclinations__generic_trait')

            # Apply is_real filter if specified
            if is_real_filter is not None:
                is_real_bool = is_real_filter.lower() in ('true', '1', 'yes')
                profiles_query = profiles_query.filter(is_real=is_real_bool)

            # Get profiles
            profiles = list(profiles_query.order_by('profile_name'))

            # Serialize profiles
            profiles_data = []
            for profile in profiles:
                profile_data = {
                    'id': profile.id,
                    'profile_name': profile.profile_name,
                    'is_real': profile.is_real,
                    'profile_type': profile.profile_type,
                }

                # Add demographics if available
                if hasattr(profile, 'demographics') and profile.demographics:
                    profile_data['demographics'] = {
                        'full_name': profile.demographics.full_name,
                        'age': profile.demographics.age,
                        'gender': profile.demographics.gender,
                        'location': profile.demographics.location,
                        'language': profile.demographics.language,
                        'occupation': profile.demographics.occupation,
                    }

                # Add trust level if available
                if hasattr(profile, 'trust_level') and profile.trust_level:
                    profile_data['trust_level'] = profile.trust_level.value

                # Add personality traits if available
                trait_inclinations = profile.trait_inclinations.all()
                if trait_inclinations:
                    traits_data = {}
                    for trait_inclination in trait_inclinations:
                        if trait_inclination.generic_trait:
                            traits_data[trait_inclination.generic_trait.name] = {
                                'strength': trait_inclination.strength,
                                'awareness': trait_inclination.awareness
                            }
                    if traits_data:
                        profile_data['traits'] = traits_data

                profiles_data.append(profile_data)

            return JsonResponse({
                'success': True,
                'profiles': profiles_data,
                'count': len(profiles_data)
            })

        except Exception as e:
            logger.error(
                "Error in UserProfileView.get: %s",
                str(e),
                exc_info=True,
                extra={
                    'is_real_filter': request.GET.get('is_real'),
                    'user': request.user.username if request.user.is_authenticated else 'anonymous'
                }
            )
            return JsonResponse({
                'error': f'Failed to fetch user profiles: {str(e)}',
                'success': False
            }, status=500)
