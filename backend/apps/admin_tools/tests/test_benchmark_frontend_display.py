"""
Tests for benchmark frontend display functionality.

This module tests the frontend JavaScript functionality for displaying
benchmark run data, including proper formatting and error handling.
"""

import json
import pytest
from django.test import TestCase, Client, TransactionTestCase
from django.urls import reverse
from django.contrib.auth.models import User
from django.template.loader import render_to_string
from django.test.utils import override_settings
from django.db import transaction
from decimal import Decimal
import asyncio
from asgiref.sync import sync_to_async

from apps.main.models import (
    BenchmarkRun, BenchmarkScenario, GenericAgent, LLMConfig
)


class BenchmarkFrontendDisplayTestCase(TransactionTestCase):
    """Test case for benchmark frontend display functionality."""

    def setUp(self):
        """Set up test data."""
        # Create a test user with staff permissions using unique username
        import uuid
        unique_id = uuid.uuid4().hex[:8]
        unique_username = f'testuser_{unique_id}'
        self.user = User.objects.create_user(
            username=unique_username,
            password='testpass',
            is_staff=True,
            is_superuser=True
        )
        self.username = unique_username
        self.unique_id = unique_id

        # Create test scenario with unique name
        self.scenario = BenchmarkScenario.objects.create(
            name=f'Frontend Test Scenario {unique_id}',
            description='A test scenario for frontend',
            agent_role='mentor',
            input_data={'user_message': 'Test message'},
            metadata={
                'workflow_type': 'test_workflow',
                'context_variables': {
                    'trust_level': 75,
                    'mood': {'valence': 0.5, 'arousal': 0.2},
                    'environment': {'stress_level': 25, 'time_pressure': 15}
                }
            }
        )
        
        # Create test agent
        self.agent, _ = GenericAgent.objects.get_or_create(
            role='mentor',
            defaults={
                'description': 'A test agent for frontend',
                'system_instructions': 'Test instructions',
                'input_schema': {'type': 'object'},
                'output_schema': {'type': 'object'},
                'langgraph_node_class': 'test.TestNode'
            }
        )
        
        # Create test LLM config
        self.llm_config = LLMConfig.objects.create(
            name='Frontend Test LLM',
            model_name='test-model-frontend',
            temperature=0.8,
            input_token_price=Decimal('0.0002'),
            output_token_price=Decimal('0.0006')
        )
        
        self.client = Client()

    def _make_api_request(self, url, method='GET', data=None):
        """
        Helper method to make API requests with proper error handling.
        This handles both sync and async views by catching common errors.
        """
        try:
            self.client.login(username=self.username, password='testpass')
            if method == 'GET':
                response = self.client.get(url)
            elif method == 'POST':
                response = self.client.post(url, data=data, content_type='application/json')
            else:
                raise ValueError(f"Unsupported method: {method}")

            # If we get a 500 error, try to extract useful information
            if response.status_code == 500:
                try:
                    error_data = json.loads(response.content)
                    print(f"API Error: {error_data}")
                except:
                    print(f"API returned 500 with content: {response.content}")

            return response
        except Exception as e:
            print(f"Exception during API request: {e}")
            raise

    def test_benchmark_management_page_loads(self):
        """Test that the benchmark management page loads correctly."""
        # Login as staff user
        self.client.login(username=self.username, password='testpass')
        
        # Access the benchmark management page
        url = reverse('game_of_life_admin:benchmark_management')
        response = self.client.get(url)
        
        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Benchmark Management')
        self.assertContains(response, 'benchmark-runs-tab')
        
        # Check that required JavaScript files are included
        self.assertContains(response, 'benchmark_management.js')
        self.assertContains(response, 'context_preview.js')
        
        # Check that the benchmark runs table structure is present
        self.assertContains(response, 'benchmark-runs-table')
        self.assertContains(response, 'Trust Level')
        self.assertContains(response, 'Valence')
        self.assertContains(response, 'Arousal')
        self.assertContains(response, 'Stress Level')
        self.assertContains(response, 'Time Pressure')
        self.assertContains(response, 'Token Usage')
        self.assertContains(response, 'Cost')

    def test_benchmark_runs_api_response_format(self):
        """Test that the API response format matches frontend expectations."""
        # Create test benchmark run
        benchmark_run = BenchmarkRun.objects.create(
            scenario=self.scenario,
            agent_definition=self.agent,
            agent_version='frontend-v1.0',
            llm_config=self.llm_config,
            parameters={
                'runs': 2,
                'context_variables': {
                    'trust_level': 75,
                    'mood': {'valence': 0.5, 'arousal': 0.2},
                    'environment': {'stress_level': 25, 'time_pressure': 15}
                }
            },
            runs_count=2,
            mean_duration=2500.75,
            median_duration=2450.0,
            min_duration=2400.0,
            max_duration=2600.0,
            std_dev=100.5,
            success_rate=0.95,
            semantic_score=0.82,
            total_input_tokens=1500,
            total_output_tokens=1000,
            estimated_cost=Decimal('0.000900')
        )
        
        # Make API request using helper method
        url = reverse('game_of_life_admin:benchmark_runs_api')
        response = self._make_api_request(url)
        
        # Check response
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        
        # Verify the response structure matches what the frontend expects
        self.assertIn('runs', data)
        self.assertEqual(len(data['runs']), 1)
        
        run_data = data['runs'][0]
        
        # Check that all required fields are present
        required_fields = [
            'id', 'scenario_name', 'agent_role', 'execution_date',
            'success_rate', 'semantic_score', 'mean_duration',
            'trust_level', 'valence', 'arousal', 'stress_level', 'time_pressure',
            'token_usage', 'cost', 'total_input_tokens', 'total_output_tokens', 'estimated_cost'
        ]
        
        for field in required_fields:
            self.assertIn(field, run_data, f"Missing required field: {field}")
        
        # Check data types and formats
        self.assertIsInstance(run_data['id'], int)
        self.assertIsInstance(run_data['scenario_name'], str)
        self.assertIsInstance(run_data['agent_role'], str)
        self.assertIsInstance(run_data['execution_date'], str)
        self.assertIsInstance(run_data['success_rate'], float)
        self.assertIsInstance(run_data['semantic_score'], float)
        self.assertIsInstance(run_data['mean_duration'], float)
        
        # Check context variable types
        self.assertIsInstance(run_data['trust_level'], int)
        self.assertIsInstance(run_data['valence'], float)
        self.assertIsInstance(run_data['arousal'], float)
        self.assertIsInstance(run_data['stress_level'], int)
        self.assertIsInstance(run_data['time_pressure'], int)
        
        # Check token and cost formatting
        self.assertIsInstance(run_data['token_usage'], str)
        self.assertIsInstance(run_data['cost'], str)
        self.assertTrue(run_data['cost'].startswith('$'))

    def test_context_variables_extraction_edge_cases(self):
        """Test context variable extraction handles edge cases."""
        # Create unique scenarios for this test to avoid constraint violations
        import uuid
        test_id = uuid.uuid4().hex[:8]

        # Create scenario for malformed test
        scenario_malformed = BenchmarkScenario.objects.create(
            name=f'Malformed Test Scenario {test_id}',
            description='A test scenario for malformed context variables',
            agent_role='mentor',
            input_data={'user_message': 'Test message'},
            metadata={'workflow_type': 'test_workflow'}
        )

        # Create scenario for nested test
        scenario_nested = BenchmarkScenario.objects.create(
            name=f'Nested Test Scenario {test_id}',
            description='A test scenario for nested context variables',
            agent_role='mentor',
            input_data={'user_message': 'Test message'},
            metadata={'workflow_type': 'test_workflow'}
        )

        # Test case 1: Malformed context variables
        run_malformed = BenchmarkRun.objects.create(
            scenario=scenario_malformed,
            agent_definition=self.agent,
            agent_version='edge-v1.0',
            llm_config=self.llm_config,
            parameters={
                'runs': 1,
                'context_variables': {
                    'trust_level': 'invalid',  # Should be int
                    'mood': 'not_a_dict',      # Should be dict
                    'environment': None        # Should be dict
                }
            },
            runs_count=1,
            mean_duration=1000.0,
            median_duration=1000.0,
            min_duration=1000.0,
            max_duration=1000.0,
            std_dev=0.0,
            success_rate=1.0
        )

        # Test case 2: Nested context variables in different location
        run_nested = BenchmarkRun.objects.create(
            scenario=scenario_nested,
            agent_definition=self.agent,
            agent_version='edge-v2.0',
            llm_config=self.llm_config,
            parameters={
                'runs': 1,
                'evaluation_template_data': {
                    'context_variables': {
                        'trust_level': 85,
                        'mood': {'valence': -0.3, 'arousal': 0.8},
                        'environment': {'stress_level': 60, 'time_pressure': 45}
                    }
                }
            },
            runs_count=1,
            mean_duration=1000.0,
            median_duration=1000.0,
            min_duration=1000.0,
            max_duration=1000.0,
            std_dev=0.0,
            success_rate=1.0
        )
        
        # Make API request using helper method
        url = reverse('game_of_life_admin:benchmark_runs_api')
        response = self._make_api_request(url)
        
        # Check response
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        
        # Find the malformed run
        malformed_data = None
        nested_data = None
        for run in data['runs']:
            if run['id'] == run_malformed.id:
                malformed_data = run
            elif run['id'] == run_nested.id:
                nested_data = run
        
        # Check malformed context variables default to 'N/A'
        self.assertIsNotNone(malformed_data)
        self.assertEqual(malformed_data['trust_level'], 'N/A')
        self.assertEqual(malformed_data['valence'], 'N/A')
        self.assertEqual(malformed_data['arousal'], 'N/A')
        self.assertEqual(malformed_data['stress_level'], 'N/A')
        self.assertEqual(malformed_data['time_pressure'], 'N/A')
        
        # Check nested context variables are extracted correctly
        self.assertIsNotNone(nested_data)
        self.assertEqual(nested_data['trust_level'], 85)
        self.assertEqual(nested_data['valence'], -0.3)
        self.assertEqual(nested_data['arousal'], 0.8)
        self.assertEqual(nested_data['stress_level'], 60)
        self.assertEqual(nested_data['time_pressure'], 45)

    def test_token_usage_formatting(self):
        """Test various token usage formatting scenarios."""
        test_cases = [
            # (input_tokens, output_tokens, expected_display)
            (1200, 800, "1200+800=2000"),
            (5000, 3000, "5000+3000=8000"),
            (0, 500, "0+500=500"),
            (1000, 0, "1000+0=1000"),
            (None, None, "N/A"),
        ]
        
        for i, (input_tokens, output_tokens, expected) in enumerate(test_cases):
            with self.subTest(case=i):
                run = BenchmarkRun.objects.create(
                    scenario=self.scenario,
                    agent_definition=self.agent,
                    agent_version=f'token-test-v{i}',
                    llm_config=self.llm_config,
                    parameters={'runs': 1},
                    runs_count=1,
                    mean_duration=1000.0,
                    median_duration=1000.0,
                    min_duration=1000.0,
                    max_duration=1000.0,
                    std_dev=0.0,
                    success_rate=1.0,
                    total_input_tokens=input_tokens,
                    total_output_tokens=output_tokens
                )
                
                # Make API request using helper method
                url = reverse('game_of_life_admin:benchmark_runs_api')
                response = self._make_api_request(url)
                
                data = json.loads(response.content)
                
                # Find our run
                run_data = None
                for r in data['runs']:
                    if r['id'] == run.id:
                        run_data = r
                        break
                
                self.assertIsNotNone(run_data)
                self.assertEqual(run_data['token_usage'], expected)

    def test_cost_formatting(self):
        """Test cost formatting scenarios."""
        test_cases = [
            # (cost, expected_display)
            (Decimal('0.000360'), "$0.000360"),
            (Decimal('0.001500'), "$0.001500"),
            (Decimal('0.000000'), "$0.000000"),
            (None, "$0.000000"),
        ]
        
        for i, (cost, expected) in enumerate(test_cases):
            with self.subTest(case=i):
                run = BenchmarkRun.objects.create(
                    scenario=self.scenario,
                    agent_definition=self.agent,
                    agent_version=f'cost-test-v{i}',
                    llm_config=self.llm_config,
                    parameters={'runs': 1},
                    runs_count=1,
                    mean_duration=1000.0,
                    median_duration=1000.0,
                    min_duration=1000.0,
                    max_duration=1000.0,
                    std_dev=0.0,
                    success_rate=1.0,
                    estimated_cost=cost
                )
                
                # Make API request using helper method
                url = reverse('game_of_life_admin:benchmark_runs_api')
                response = self._make_api_request(url)
                
                data = json.loads(response.content)
                
                # Find our run
                run_data = None
                for r in data['runs']:
                    if r['id'] == run.id:
                        run_data = r
                        break
                
                self.assertIsNotNone(run_data)
                self.assertEqual(run_data['cost'], expected)
