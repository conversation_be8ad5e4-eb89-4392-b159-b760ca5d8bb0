"""
Tests for benchmark runs display functionality.

This module tests that benchmark run data is correctly stored and displayed,
including context variables, token usage, and cost information.
"""

import json
import pytest
from decimal import Decimal
from django.test import TestCase, Client, TransactionTestCase
from django.urls import reverse
from django.contrib.auth.models import User
from django.utils import timezone
from unittest.mock import MagicMock

from apps.main.models import (
    BenchmarkRun, BenchmarkScenario, GenericAgent, LLMConfig
)


class BenchmarkRunsDisplayTestCase(TransactionTestCase):
    """Test case for benchmark runs display functionality."""

    def setUp(self):
        """Set up test data."""
        # Create a test user with staff permissions using unique username
        import uuid
        unique_username = f'testuser_{uuid.uuid4().hex[:8]}'
        self.user = User.objects.create_user(
            username=unique_username,
            password='testpass',
            is_staff=True,
            is_superuser=True
        )
        self.username = unique_username
        
        # Create test scenario
        self.scenario = BenchmarkScenario.objects.create(
            name='Test Scenario',
            description='A test scenario',
            agent_role='mentor',
            input_data={'user_message': 'Test message'},
            metadata={
                'workflow_type': 'test_workflow',
                'context_variables': {
                    'trust_level': 65,
                    'mood': {'valence': 0.3, 'arousal': -0.1},
                    'environment': {'stress_level': 40, 'time_pressure': 30}
                }
            }
        )
        
        # Create test agent
        self.agent, _ = GenericAgent.objects.get_or_create(
            role='mentor',
            defaults={
                'description': 'A test mentor agent',
                'system_instructions': 'Test instructions',
                'input_schema': {'type': 'object'},
                'output_schema': {'type': 'object'},
                'langgraph_node_class': 'test.TestNode'
            }
        )
        
        # Create test LLM config
        self.llm_config = LLMConfig.objects.create(
            name='Test LLM Config',
            model_name='test-model',
            temperature=0.7,
            input_token_price=Decimal('0.0001'),
            output_token_price=Decimal('0.0003')
        )
        
        # Create test benchmark run with comprehensive data
        self.benchmark_run = BenchmarkRun.objects.create(
            scenario=self.scenario,
            agent_definition=self.agent,
            agent_version='test-v1.0',
            llm_config=self.llm_config,
            parameters={
                'runs': 3,
                'context_variables': {
                    'trust_level': 65,
                    'mood': {'valence': 0.3, 'arousal': -0.1},
                    'environment': {'stress_level': 40, 'time_pressure': 30}
                }
            },
            runs_count=3,
            mean_duration=1500.5,
            median_duration=1450.0,
            min_duration=1400.0,
            max_duration=1600.0,
            std_dev=75.2,
            success_rate=0.85,
            semantic_score=0.78,
            total_input_tokens=1200,
            total_output_tokens=800,
            estimated_cost=Decimal('0.000360'),
            stage_performance_details={
                'init': {
                    'mean_ms': 150.5,
                    'median_ms': 145.0,
                    'min_ms': 140.0,
                    'max_ms': 160.0,
                    'std_dev_ms': 8.2,
                    'count': 3
                },
                'process': {
                    'mean_ms': 1200.0,
                    'median_ms': 1180.0,
                    'min_ms': 1150.0,
                    'max_ms': 1250.0,
                    'std_dev_ms': 45.8,
                    'count': 3
                },
                'finalize': {
                    'mean_ms': 150.0,
                    'median_ms': 125.0,
                    'min_ms': 110.0,
                    'max_ms': 190.0,
                    'std_dev_ms': 21.2,
                    'count': 3
                }
            }
        )
        
        self.client = Client()

    def _make_api_request(self, url, method='GET', data=None):
        """
        Helper method to make API requests with proper error handling.
        This handles both sync and async views by catching common errors.
        """
        try:
            self.client.login(username='testuser', password='testpass')
            if method == 'GET':
                response = self.client.get(url)
            elif method == 'POST':
                response = self.client.post(url, data=data, content_type='application/json')
            else:
                raise ValueError(f"Unsupported method: {method}")

            # If we get a 500 error, try to extract useful information
            if response.status_code == 500:
                try:
                    error_data = json.loads(response.content)
                    print(f"API Error: {error_data}")
                except:
                    print(f"API returned 500 with content: {response.content}")

            return response
        except Exception as e:
            print(f"Exception during API request: {e}")
            raise

    def test_benchmark_runs_api_returns_complete_data(self):
        """Test that the benchmark runs API returns all required data."""
        # Make API request using helper method
        url = reverse('game_of_life_admin:benchmark_runs_api')
        response = self._make_api_request(url)
        
        # Check response
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        
        # Verify structure
        self.assertIn('runs', data)
        self.assertEqual(len(data['runs']), 1)
        
        run_data = data['runs'][0]
        
        # Check basic fields
        self.assertEqual(run_data['scenario_name'], 'Test Scenario')
        self.assertEqual(run_data['agent_role'], 'mentor')
        self.assertEqual(run_data['success_rate'], 0.85)
        self.assertEqual(run_data['semantic_score'], 0.78)
        self.assertEqual(run_data['mean_duration'], 1500.5)
        
        # Check context variables
        self.assertEqual(run_data['trust_level'], 65)
        self.assertEqual(run_data['valence'], 0.3)
        self.assertEqual(run_data['arousal'], -0.1)
        self.assertEqual(run_data['stress_level'], 40)
        self.assertEqual(run_data['time_pressure'], 30)
        
        # Check token usage and cost
        self.assertIn('token_usage', run_data)
        self.assertIn('cost', run_data)
        self.assertEqual(run_data['total_input_tokens'], 1200)
        self.assertEqual(run_data['total_output_tokens'], 800)
        self.assertEqual(run_data['estimated_cost'], 0.000360)
        
        # Verify token usage display format
        expected_token_display = "1200+800=2000"
        self.assertEqual(run_data['token_usage'], expected_token_display)
        
        # Verify cost display format
        expected_cost_display = "$0.000360"
        self.assertEqual(run_data['cost'], expected_cost_display)

    def test_benchmark_runs_api_handles_missing_context_variables(self):
        """Test that the API handles missing context variables gracefully."""
        # Create a run without context variables
        run_without_context = BenchmarkRun.objects.create(
            scenario=self.scenario,
            agent_definition=self.agent,
            agent_version='test-v2.0',
            llm_config=self.llm_config,
            parameters={'runs': 1},  # No context_variables
            runs_count=1,
            mean_duration=1000.0,
            median_duration=1000.0,
            min_duration=1000.0,
            max_duration=1000.0,
            std_dev=0.0,
            success_rate=1.0
        )
        
        # Make API request using helper method
        url = reverse('game_of_life_admin:benchmark_runs_api')
        response = self._make_api_request(url)
        
        # Check response
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        
        # Find the run without context
        run_data = None
        for run in data['runs']:
            if run['id'] == run_without_context.id:
                run_data = run
                break
        
        self.assertIsNotNone(run_data)
        
        # Check that missing context variables default to 'N/A'
        self.assertEqual(run_data['trust_level'], 'N/A')
        self.assertEqual(run_data['valence'], 'N/A')
        self.assertEqual(run_data['arousal'], 'N/A')
        self.assertEqual(run_data['stress_level'], 'N/A')
        self.assertEqual(run_data['time_pressure'], 'N/A')

    def test_benchmark_runs_api_handles_missing_token_data(self):
        """Test that the API handles missing token and cost data gracefully."""
        # Create a run without token data
        run_without_tokens = BenchmarkRun.objects.create(
            scenario=self.scenario,
            agent_definition=self.agent,
            agent_version='test-v3.0',
            llm_config=self.llm_config,
            parameters={'runs': 1},
            runs_count=1,
            mean_duration=1000.0,
            median_duration=1000.0,
            min_duration=1000.0,
            max_duration=1000.0,
            std_dev=0.0,
            success_rate=1.0
            # No token or cost data
        )
        
        # Make API request using helper method
        url = reverse('game_of_life_admin:benchmark_runs_api')
        response = self._make_api_request(url)
        
        # Check response
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        
        # Find the run without tokens
        run_data = None
        for run in data['runs']:
            if run['id'] == run_without_tokens.id:
                run_data = run
                break
        
        self.assertIsNotNone(run_data)
        
        # Check that missing token/cost data defaults appropriately
        self.assertEqual(run_data['token_usage'], 'N/A')
        self.assertEqual(run_data['cost'], '$0.000000')
        self.assertEqual(run_data['total_input_tokens'], None)
        self.assertEqual(run_data['total_output_tokens'], None)
        self.assertEqual(run_data['estimated_cost'], 0.0)

    def test_token_usage_display_property(self):
        """Test the token_usage_display property of BenchmarkRun model."""
        # Test with both input and output tokens
        self.assertEqual(self.benchmark_run.token_usage_display, "1.2k+800=2.0k")

        # Test with only input tokens
        run_input_only = BenchmarkRun.objects.create(
            scenario=self.scenario,
            agent_definition=self.agent,
            agent_version='test-v4.0',
            llm_config=self.llm_config,
            parameters={'runs': 1},
            runs_count=1,
            mean_duration=1000.0,
            median_duration=1000.0,
            min_duration=1000.0,
            max_duration=1000.0,
            std_dev=0.0,
            success_rate=1.0,
            total_input_tokens=500,
            total_output_tokens=None
        )

        self.assertEqual(run_input_only.token_usage_display, "500")

        # Test with only output tokens
        run_output_only = BenchmarkRun.objects.create(
            scenario=self.scenario,
            agent_definition=self.agent,
            agent_version='test-v5.0',
            llm_config=self.llm_config,
            parameters={'runs': 1},
            runs_count=1,
            mean_duration=1000.0,
            median_duration=1000.0,
            min_duration=1000.0,
            max_duration=1000.0,
            std_dev=0.0,
            success_rate=1.0,
            total_input_tokens=None,
            total_output_tokens=300
        )

        self.assertEqual(run_output_only.token_usage_display, "300")

        # Test with no tokens
        run_no_tokens = BenchmarkRun.objects.create(
            scenario=self.scenario,
            agent_definition=self.agent,
            agent_version='test-v6.0',
            llm_config=self.llm_config,
            parameters={'runs': 1},
            runs_count=1,
            mean_duration=1000.0,
            median_duration=1000.0,
            min_duration=1000.0,
            max_duration=1000.0,
            std_dev=0.0,
            success_rate=1.0
        )

        self.assertEqual(run_no_tokens.token_usage_display, "0")
