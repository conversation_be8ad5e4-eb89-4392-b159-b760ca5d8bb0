"""
Admin tools views for the Game of Life project.

This module contains views for the admin tools, including:
- Dashboard views
- Benchmark run views
- History views
"""

import json
import logging
from datetime import datetime
from django.http import JsonResponse, HttpResponse, Http404, HttpResponseForbidden
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib import messages
from django.db import transaction
from django.views import View
from django.db.models import Count
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync, sync_to_async
from channels.db import database_sync_to_async
import traceback

# Import models and services needed for benchmarking
from apps.main.models import BenchmarkScenario, BenchmarkRun, GenericAgent, BenchmarkTag
from apps.main.services.benchmark_manager import AgentBenchmarker
from django.utils.dateparse import parse_date
from celery import current_app as celery_app # Import Celery app instance
import asyncio # For potential async operations if needed directly in view
import uuid # For handling UUIDs in API responses

# Import benchmark management views - these are now in the benchmark package
from apps.admin_tools.benchmark import views as benchmark_views

logger = logging.getLogger(__name__)

# Ensure only staff members can access this view
@staff_member_required
def websocket_tester_view(request):
    """
    Admin view to send messages to the WebSocket consumer and view history.
    """
    context = {
        'title': 'WebSocket Tester',
        'has_permission': True, # Required for admin templates
        'site_header': 'Game of Life Admin', # Optional: Customize header
        'opts': '', # Placeholder for model options if extending ModelAdmin
        'app_label': 'admin_tools', # App label for breadcrumbs/context
    }

    if request.method == 'POST':
        is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest' or \
                  request.content_type == 'application/json'

        send_status = None
        send_error = None
        user_id = None
        message_json = None

        try:
            if is_ajax:
                # Handle Fetch request (JSON body)
                data = json.loads(request.body)
                user_id = data.get('user_id')
                message_json = data.get('message_json')
            else:
                # Handle standard form submission (fallback)
                user_id = request.POST.get('user_id')
                message_json = request.POST.get('message_json')
                context['last_user_id'] = user_id
                context['last_sent_message'] = message_json

            if not user_id or not message_json:
                raise ValueError("Missing user_id or message_json")

            # Validate JSON and send message (common logic)
            message_data = json.loads(message_json) # Validate JSON structure
            channel_layer = get_channel_layer()
            # Target the predictable group name that the UserSessionConsumer joins
            target_group_name = f"user_session_group_{user_id}"

            async_to_sync(channel_layer.group_send)(
                target_group_name, # Send to the user-specific group
                {
                    # Use the custom type that maps to the 'admin_message' handler
                    "type": "admin.message",
                    # Pass the raw JSON string as 'text' which the handler expects
                    "text": message_json
                }
            )
            send_status = f"Message successfully sent to group '{target_group_name}' via admin.message handler." # Update status message

        except json.JSONDecodeError:
            send_error = "Invalid JSON format. Please check your input."
        except ValueError as e:
             send_error = str(e)
        except Exception as e:
            send_error = f"An error occurred while sending: {e}"

        if is_ajax:
            # Return JSON response for Fetch request
            return JsonResponse({'send_status': send_status, 'send_error': send_error})
        else:
            # Update context for standard form submission render
            context['send_status'] = send_status
            context['send_error'] = send_error

    # Render the full page for GET requests or standard POST fallback
    return render(request, 'admin_tools/websocket_tester.html', context)


###############################################################################
# Dashboard Views
###############################################################################

@staff_member_required
def dashboard(request):
    """Admin dashboard view."""
    context = {
        'title': 'Admin Dashboard',
        'has_permission': True,
        'site_header': 'Game of Life Admin',
        'opts': None,
        'app_label': 'admin_tools',
    }

    # Get recent benchmark runs with related data
    recent_runs = BenchmarkRun.objects.select_related('scenario', 'agent_definition').order_by('-execution_date')[:10]
    context['recent_runs'] = recent_runs

    # Get active benchmark scenarios
    active_scenarios = BenchmarkScenario.objects.filter(is_active=True).count()
    context['active_scenarios'] = active_scenarios

    # Get total benchmark runs
    total_runs = BenchmarkRun.objects.count()
    context['total_runs'] = total_runs

    # Get success rate (using success_rate field, considering >= 0.5 as success)
    if total_runs > 0:
        success_runs = BenchmarkRun.objects.filter(success_rate__gte=0.5).count()
        context['success_rate'] = (success_runs / total_runs * 100)
    else:
        context['success_rate'] = 0

    # Prepare recent runs data for JavaScript (for error status updates)
    recent_runs_data = []
    for run in recent_runs:
        # Check if run has errors in raw_results
        has_errors = False
        if run.raw_results and isinstance(run.raw_results, dict):
            errors = run.raw_results.get('errors', [])
            has_errors = isinstance(errors, list) and len(errors) > 0

        recent_runs_data.append({
            'id': run.id,
            'has_errors': has_errors
        })

    import json
    context['recent_runs_json'] = json.dumps(recent_runs_data)

    return render(request, 'admin_tools/benchmark_dashboard.html', context)

###############################################################################
# Benchmark Run Views
###############################################################################

@staff_member_required
def benchmark_run(request, run_id=None):
    """Admin view for benchmark run details."""
    context = {
        'title': 'Benchmark Run',
        'has_permission': True,
        'site_header': 'Game of Life Admin',
        'opts': None,
        'app_label': 'admin_tools',
    }

    if run_id:
        # Get the benchmark run
        run = get_object_or_404(BenchmarkRun, id=run_id)
        context['run'] = run

        # Get the scenario
        scenario = run.scenario
        context['scenario'] = scenario

        # Get the agent
        agent = run.agent
        context['agent'] = agent

        # Get the run details
        context['run_details'] = {
            'id': run.id,
            'execution_date': run.execution_date,
            'success': run.success,
            'success_rate': run.success_rate,
            'semantic_score': run.semantic_score,
            'execution_time': run.execution_time,
            'token_usage': run.token_usage,
            'cost': run.cost,
            'evaluator_llm_model': run.evaluator_llm_model,
        }

        # Get the run output
        context['run_output'] = run.output

        # Get the run evaluation
        context['run_evaluation'] = run.evaluation

        return render(request, 'admin_tools/benchmark_run.html', context)
    else:
        # Redirect to the dashboard
        return redirect('game_of_life_admin:dashboard')

###############################################################################
# History Views
###############################################################################

@staff_member_required
def history(request):
    """Admin view for benchmark run history."""
    context = {
        'title': 'Benchmark History',
        'has_permission': True,
        'site_header': 'Game of Life Admin',
        'opts': None,
        'app_label': 'admin_tools',
    }

    # Get all benchmark runs
    runs = BenchmarkRun.objects.all().order_by('-execution_date')
    context['runs'] = runs

    # Get success rate
    success_runs = runs.filter(success=True).count()
    context['success_rate'] = (success_runs / runs.count() * 100) if runs.count() > 0 else 0

    # Get average semantic score
    semantic_scores = [run.semantic_score for run in runs if run.semantic_score is not None]
    context['avg_semantic_score'] = sum(semantic_scores) / len(semantic_scores) if semantic_scores else 0

    # Get total token usage
    token_usage = sum([run.token_usage for run in runs if run.token_usage is not None])
    context['token_usage'] = token_usage

    # Get total cost
    cost = sum([run.cost for run in runs if run.cost is not None])
    context['cost'] = cost

    return render(request, 'admin_tools/history.html', context)

###############################################################################
# API Views
###############################################################################

class BenchmarkRunView(View):
    """API View for benchmark run operations."""

    async def _check_permissions(self, request):
        """Async-safe permission check."""
        is_authenticated = await sync_to_async(lambda: request.user.is_authenticated)()
        if not is_authenticated:
            return False
        is_active = await sync_to_async(lambda: request.user.is_active)()
        is_staff = await sync_to_async(lambda: request.user.is_staff)()
        return is_active and is_staff

    def _check_permissions_sync(self, request):
        """Sync version of permission check."""
        return (request.user.is_authenticated and
                request.user.is_active and
                request.user.is_staff)

    def dispatch(self, request, *args, **kwargs):
        # Perform permission check before calling get or post
        if not self._check_permissions_sync(request):
            return HttpResponseForbidden("You do not have permission to access this resource.")
        return super().dispatch(request, *args, **kwargs)

    def get(self, request, run_id=None):
        """Get benchmark run details."""
        try:
            if run_id:
                # Get a specific run
                # Get a specific run (now sync)
                run = get_object_or_404(BenchmarkRun.objects.select_related('scenario', 'agent_definition'), id=run_id)
                # Extract context package information
                context_package = self._extract_context_package(run)

                # Import the _determine_execution_type function from benchmark views
                from apps.admin_tools.benchmark.views import _determine_execution_type

                # Determine execution type (Agent vs Workflow evaluation)
                execution_type = _determine_execution_type(run)

                # Extract workflow-specific data if this is a workflow evaluation
                workflow_type = None
                agent_communications = None

                if 'Workflow' in execution_type:
                    # Extract workflow type from scenario metadata or parameters
                    if run.scenario and run.scenario.metadata:
                        workflow_type = run.scenario.metadata.get('workflow_type')

                    if not workflow_type and run.parameters:
                        workflow_type = run.parameters.get('workflow_type')

                    # Extract agent communications from raw results
                    if run.raw_results:
                        agent_communications = run.raw_results.get('agent_communications', {})
                        # If not found in top level, check in last_output
                        if not agent_communications:
                            last_output = run.raw_results.get('last_output', {})
                            if isinstance(last_output, dict):
                                agent_communications = last_output.get('agent_communications', {})

                run_data = {
                    'id': run.id,
                    'scenario': run.scenario.name if run.scenario else 'N/A',
                    'agent_role': run.agent_definition.role if run.agent_definition else 'N/A',
                    'execution_date': run.execution_date.isoformat(),
                    'execution_type': execution_type,  # Add execution type
                    'workflow_type': workflow_type,    # Add workflow type for workflow evaluations
                    'success_rate': run.success_rate,
                    'semantic_score': run.semantic_score,
                    'semantic_evaluation_details': run.semantic_evaluation_details,
                    'semantic_evaluations': run.semantic_evaluations,
                    'mean_duration': run.mean_duration,
                    'median_duration': run.median_duration,
                    'min_duration': run.min_duration,
                    'max_duration': run.max_duration,
                    'std_dev': run.std_dev,
                    'llm_calls': run.llm_calls,
                    'tool_calls': run.tool_calls,
                    'tool_breakdown': run.tool_breakdown,
                    'tool_call_details': self._extract_tool_call_details(run),
                    'token_usage': run.total_tokens,
                    'total_input_tokens': run.total_input_tokens,
                    'total_output_tokens': run.total_output_tokens,
                    'estimated_cost': float(run.estimated_cost) if run.estimated_cost else 0.0,
                    'llm_model': run.agent_llm_model_name,
                    'llm_temperature': run.llm_temperature,
                    'agent_version': run.agent_version,
                    'evaluator_llm_model': run.evaluator_llm_model,
                    'parameters': run.parameters,
                    'raw_results': run.raw_results,
                    'context_package': context_package,
                    'agent_communications': agent_communications,  # Add agent communications for workflow evaluations
                    'comparison_results': self._extract_comparison_results(run),
                }

                return JsonResponse(run_data)
            else:
                # List runs with filtering
                scenario_id = request.GET.get('scenario_id')
                agent_role = request.GET.get('agent_role')  # Fixed: use agent_role instead of agent_id
                tag = request.GET.get('tag')  # Added tag filtering
                success = request.GET.get('success')
                start_date = request.GET.get('start_date')
                end_date = request.GET.get('end_date')

                # Debug logging for filter parameters
                logger.debug(f"Filter parameters: scenario_id={scenario_id}, agent_role={agent_role}, tag={tag}, success={success}, start_date={start_date}, end_date={end_date}")

                # List runs with filtering (now sync)
                queryset = BenchmarkRun.objects.select_related('scenario', 'agent_definition')

                if scenario_id:
                    queryset = queryset.filter(scenario_id=scenario_id)

                if agent_role:
                    # Filter by agent role through agent_definition
                    queryset = queryset.filter(agent_definition__role=agent_role)

                if tag:
                    # Filter by scenario tags
                    try:
                        tag_id = int(tag)
                        queryset = queryset.filter(scenario__tags__id=tag_id)
                    except ValueError:
                        # If not an integer, treat as tag name
                        queryset = queryset.filter(scenario__tags__name=tag)

                if success is not None:
                    success_bool = success.lower() == 'true'
                    # Use success_rate >= 0.5 to determine success
                    if success_bool:
                        queryset = queryset.filter(success_rate__gte=0.5)
                    else:
                        queryset = queryset.filter(success_rate__lt=0.5)

                if start_date:
                    start_date_obj = parse_date(start_date)
                    if start_date_obj:
                        queryset = queryset.filter(execution_date__gte=start_date_obj)

                if end_date:
                    end_date_obj = parse_date(end_date)
                    if end_date_obj:
                        queryset = queryset.filter(execution_date__lte=end_date_obj)

                # Convert to list of dicts
                runs = queryset.order_by('-execution_date')
                runs_data = []
                for run in runs:
                    try:
                        # Extract evaluation variables from parameters
                        params = run.parameters or {}
                        context_vars = params.get('context_variables', {})

                        # Also check in evaluation_template_data if not found in context_variables
                        if not context_vars:
                            template_data = params.get('evaluation_template_data', {})
                            context_vars = template_data.get('context_variables', {})

                        # Extract individual evaluation variables with proper handling of different formats
                        try:
                            trust_level_raw = context_vars.get('trust_level', 'N/A')
                            if isinstance(trust_level_raw, dict) and 'value' in trust_level_raw:
                                trust_level = trust_level_raw['value']
                            elif isinstance(trust_level_raw, (int, float)):
                                trust_level = trust_level_raw
                            else:
                                trust_level = 'N/A'
                        except Exception:
                            trust_level = 'N/A'

                        # Extract valence - check both nested and direct formats
                        try:
                            valence_raw = context_vars.get('valence', 'N/A')
                            if valence_raw == 'N/A':
                                # Try nested format
                                mood = context_vars.get('mood', {})
                                valence_raw = mood.get('valence', 'N/A') if isinstance(mood, dict) else 'N/A'

                            if isinstance(valence_raw, dict) and 'value' in valence_raw:
                                valence = valence_raw['value']
                            elif isinstance(valence_raw, (int, float)):
                                valence = valence_raw
                            else:
                                valence = 'N/A'
                        except Exception:
                            valence = 'N/A'

                        # Extract arousal - check both nested and direct formats
                        try:
                            arousal_raw = context_vars.get('arousal', 'N/A')
                            if arousal_raw == 'N/A':
                                # Try nested format
                                mood = context_vars.get('mood', {})
                                arousal_raw = mood.get('arousal', 'N/A') if isinstance(mood, dict) else 'N/A'

                            if isinstance(arousal_raw, dict) and 'value' in arousal_raw:
                                arousal = arousal_raw['value']
                            elif isinstance(arousal_raw, (int, float)):
                                arousal = arousal_raw
                            else:
                                arousal = 'N/A'
                        except Exception:
                            arousal = 'N/A'

                        # Extract stress level - check both nested and direct formats
                        try:
                            stress_level_raw = context_vars.get('stress_level', 'N/A')
                            if stress_level_raw == 'N/A':
                                # Try nested format
                                environment = context_vars.get('environment', {})
                                stress_level_raw = environment.get('stress_level', 'N/A') if isinstance(environment, dict) else 'N/A'

                            if isinstance(stress_level_raw, dict) and 'value' in stress_level_raw:
                                stress_level = stress_level_raw['value']
                            elif isinstance(stress_level_raw, (int, float)):
                                stress_level = stress_level_raw
                            else:
                                stress_level = 'N/A'
                        except Exception:
                            stress_level = 'N/A'

                        # Extract time pressure - check both nested and direct formats
                        try:
                            time_pressure_raw = context_vars.get('time_pressure', 'N/A')
                            if time_pressure_raw == 'N/A':
                                # Try nested format
                                environment = context_vars.get('environment', {})
                                time_pressure_raw = environment.get('time_pressure', 'N/A') if isinstance(environment, dict) else 'N/A'

                            if isinstance(time_pressure_raw, dict) and 'value' in time_pressure_raw:
                                time_pressure = time_pressure_raw['value']
                            elif isinstance(time_pressure_raw, (int, float)):
                                time_pressure = time_pressure_raw
                            else:
                                time_pressure = 'N/A'
                        except Exception:
                            time_pressure = 'N/A'

                        # Format token usage for display with safe property access
                        try:
                            # Use raw numbers for frontend display (not the k-formatted version)
                            if run.total_input_tokens is not None and run.total_output_tokens is not None:
                                total_tokens = (run.total_input_tokens or 0) + (run.total_output_tokens or 0)
                                token_usage_display = f"{run.total_input_tokens}+{run.total_output_tokens}={total_tokens}"
                            elif run.total_input_tokens is not None or run.total_output_tokens is not None:
                                # Handle case where only one is set
                                input_tokens = run.total_input_tokens or 0
                                output_tokens = run.total_output_tokens or 0
                                total_tokens = input_tokens + output_tokens
                                token_usage_display = f"{input_tokens}+{output_tokens}={total_tokens}"
                            else:
                                # Both total_input_tokens and total_output_tokens are None
                                token_usage_display = "N/A"
                        except Exception as e:
                            logger.warning(f"Error formatting token usage for run {run.id}: {e}")
                            token_usage_display = "N/A"

                        # Format cost for display with safe conversion
                        try:
                            if run.estimated_cost is not None:
                                cost_display = f"${float(run.estimated_cost):.6f}"
                            else:
                                cost_display = '$0.000000'
                        except (ValueError, TypeError) as e:
                            logger.warning(f"Error formatting cost for run {run.id}: {e}")
                            cost_display = '$0.000000'

                        # Import the _determine_execution_type function from benchmark views
                        from apps.admin_tools.benchmark.views import _determine_execution_type

                        # Determine execution type (Agent vs Workflow evaluation)
                        execution_type = _determine_execution_type(run)

                        runs_data.append({
                            'id': run.id,
                            'scenario_id': run.scenario_id,
                            'scenario_name': run.scenario.name if run.scenario else 'N/A',
                            'agent_role': run.agent_definition.role if run.agent_definition else 'N/A',
                            'execution_date': run.execution_date.isoformat(),
                            'execution_type': execution_type,  # Add execution type for proper display
                            'success_rate': run.success_rate,
                            'semantic_score': run.semantic_score,
                            'mean_duration': run.mean_duration,
                            'trust_level': trust_level,
                            'valence': valence,
                            'arousal': arousal,
                            'stress_level': stress_level,
                            'time_pressure': time_pressure,
                            'token_usage': token_usage_display,
                            'cost': cost_display,
                            'total_input_tokens': run.total_input_tokens,
                            'total_output_tokens': run.total_output_tokens,
                            'estimated_cost': float(run.estimated_cost) if run.estimated_cost else 0.0,
                            'context_variables': context_vars,  # Include full context variables for frontend processing
                            'parameters': run.parameters,  # Add parameters for error checking
                        })
                    except Exception as e:
                        logger.error(f"Error processing run {run.id}: {e}", exc_info=True)

                return JsonResponse({'runs': runs_data})

        except Exception as e:
            logger.error("Error in BenchmarkRunView.get", exc_info=True)
            return JsonResponse({'error': str(e)}, status=500)

    def post(self, request):
        """Run a benchmark scenario."""
        try:
            data = json.loads(request.body)

            # Check if this is an evaluation template test request
            if 'evaluation_template_id' in data or 'evaluation_template_data' in data:
                return self._handle_template_test_sync(data)

            # Original benchmark run logic
            # Validate required fields
            required_fields = ['scenario_id']
            for field in required_fields:
                if field not in data:
                    return JsonResponse({'error': f"Missing required field: {field}"}, status=400)

            # Get parameters
            params = data.get('params', {})
            runs = params.get('runs', 1)
            semantic_evaluation = params.get('semantic_evaluation', True)

            # Get the scenario (now sync)
            scenario = get_object_or_404(BenchmarkScenario, id=data['scenario_id'])

            # Launch benchmark task (now sync)
            from celery import current_app as celery_app

            task_params = {
                'runs': runs,
                'semantic_evaluation': semantic_evaluation,
            }

            # Add optional parameters
            if 'llm_config_id' in params:
                task_params['llm_config_id'] = params['llm_config_id']
            if 'evaluation_llm_config_id' in params:
                task_params['evaluation_llm_config_id'] = params['evaluation_llm_config_id']
            if 'context_variables' in params:
                task_params['context_variables'] = params['context_variables']

            # Add user_profile_id if present
            if 'user_profile_id' in params:
                task_params['user_profile_id'] = params['user_profile_id']

            # Add execution mode parameters (use_real_llm, use_real_tools, use_real_db)
            if 'use_real_llm' in params:
                task_params['use_real_llm'] = params['use_real_llm']
            if 'use_real_tools' in params:
                task_params['use_real_tools'] = params['use_real_tools']
            if 'use_real_db' in params:
                task_params['use_real_db'] = params['use_real_db']

            # Launch the task
            task = celery_app.send_task(
                'apps.main.tasks.benchmark_tasks.run_workflow_benchmark',
                args=[str(scenario.id)],
                kwargs={'params': task_params}
            )

            task_id = task.id

            return JsonResponse({
                'success': True,
                'task_id': task_id,
                'message': f"Benchmark task launched successfully."
            })

        except json.JSONDecodeError:
            return JsonResponse({'error': "Invalid JSON body"}, status=400)
        except Exception as e:
            logger.error("Error in BenchmarkRunView.post", exc_info=True)

            # Emit error event via WebSocket for real-time feedback
            try:
                from apps.main.services.event_service import EventService
                import traceback
                import asyncio

                # Create task to emit error event (since this is a sync context)
                asyncio.create_task(
                    EventService.emit_debug_info(
                        level='error',
                        message=f"Benchmark API error: {str(e)}",
                        source='BenchmarkRunView',
                        details={
                            'error_type': type(e).__name__,
                            'error_message': str(e),
                            'traceback': traceback.format_exc(),
                            'endpoint': 'POST /admin/benchmarks/api/run/'
                        }
                    )
                )
            except Exception as emit_error:
                logger.warning(f"Failed to emit error event: {emit_error}")

            return JsonResponse({'error': str(e)}, status=500)

    def _extract_tool_call_details(self, run):
        """Extract tool call details for the modal display."""
        try:
            tool_call_details = {
                'total_calls': run.tool_calls or 0,
                'mocked_calls': 0,
                'real_calls': 0,
            }

            # Try to extract mocked vs real calls from raw results
            if run.raw_results:
                # Look for tool call information in various places
                errors = run.raw_results.get('errors', [])
                for error in errors:
                    if 'mock' in str(error).lower():
                        tool_call_details['mocked_calls'] += 1

                # Calculate real calls
                tool_call_details['real_calls'] = tool_call_details['total_calls'] - tool_call_details['mocked_calls']

            return tool_call_details
        except Exception as e:
            logger.warning(f"Error extracting tool call details for run {run.id}: {e}")
            return {'total_calls': 0, 'mocked_calls': 0, 'real_calls': 0}

    def _extract_comparison_results(self, run):
        """Extract comparison results if available."""
        try:
            if run.compared_to_run and run.performance_p_value is not None:
                return {
                    'compared_to_run_id': str(run.compared_to_run.id),
                    'performance_p_value': run.performance_p_value,
                    'is_performance_significant': run.is_performance_significant,
                }
            return None
        except Exception as e:
            logger.warning(f"Error extracting comparison results for run {run.id}: {e}")
            return None

    def _extract_context_package(self, run):
        """Extract and format context package information from a benchmark run."""
        try:
            context_package = {
                'summary': {},  # High-level summary for quick analysis
                'input_context': {},
                'agent_output': {},
                'context_flow': [],
                'evaluation_context': {},
                'analysis': {}  # Meaningful analysis data
            }

            # Extract input context from parameters
            params = run.parameters or {}

            # Get context variables used for evaluation
            context_vars = params.get('context_variables', {})
            if context_vars:
                context_package['input_context']['context_variables'] = context_vars

            # Get evaluation template data if present
            template_data = params.get('evaluation_template_data', {})
            if template_data:
                context_package['evaluation_context']['template_data'] = template_data

            # Extract scenario input data
            scenario_input = {}
            if run.scenario and run.scenario.input_data:
                scenario_input = run.scenario.input_data
                context_package['input_context']['scenario_input'] = scenario_input

            # Extract agent output from raw results
            raw_results = run.raw_results or {}

            # Get the last output data which contains agent responses
            # Handle both agent benchmarks (last_output_data) and workflow benchmarks (last_output)
            last_output = raw_results.get('last_output_data', {}) or raw_results.get('last_output', {})
            if last_output:
                context_package['agent_output']['final_output'] = last_output

                # For workflow benchmarks, extract wheel data if available
                if 'output_data' in last_output and 'wheel' in last_output['output_data']:
                    wheel_data = last_output['output_data']['wheel']
                    context_package['agent_output']['wheel_data'] = wheel_data
                    context_package['agent_output']['workflow_type'] = last_output.get('workflow_type', 'unknown')

            # Extract individual run outputs if available
            individual_runs = raw_results.get('individual_runs', [])
            if individual_runs:
                context_package['agent_output']['individual_runs'] = individual_runs

            # Extract context flow information
            context_flow = []

            # Add initial context
            if context_vars or scenario_input:
                context_flow.append({
                    'stage': 'input',
                    'description': 'Initial context provided to agent',
                    'data': {
                        'context_variables': context_vars,
                        'scenario_input': scenario_input
                    }
                })

            # Add agent processing stage
            if last_output:
                context_flow.append({
                    'stage': 'processing',
                    'description': 'Agent processing and output generation',
                    'data': last_output
                })

            # Add evaluation stage if semantic evaluation was performed
            if run.semantic_score is not None:
                context_flow.append({
                    'stage': 'evaluation',
                    'description': 'Semantic evaluation of agent output',
                    'data': {
                        'semantic_score': run.semantic_score,
                        'evaluation_details': run.semantic_evaluation_details
                    }
                })

            context_package['context_flow'] = context_flow

            # Generate high-level summary
            context_package['summary'] = self._generate_context_summary(run, context_vars, last_output, scenario_input)

            # Generate analysis data
            context_package['analysis'] = self._generate_context_analysis(run, context_vars, last_output, individual_runs)

            return context_package

        except Exception as e:
            logger.error(f"Error extracting context package for run {run.id}: {e}", exc_info=True)
            return {
                'error': f"Failed to extract context package: {str(e)}",
                'summary': {},
                'input_context': {},
                'agent_output': {},
                'context_flow': [],
                'evaluation_context': {},
                'analysis': {}
            }

    def _generate_context_summary(self, run, context_vars, last_output, scenario_input):
        """Generate high-level summary for quick analysis."""
        try:
            summary = {
                'context_profile': 'Unknown',
                'agent_confidence': 'N/A',
                'response_quality': 'N/A',
                'key_factors': [],
                'performance_indicators': {}
            }

            # Determine context profile based on context variables
            if context_vars:
                trust_level = self._extract_numeric_value(context_vars.get('trust_level', 0))
                stress_level = self._extract_numeric_value(context_vars.get('stress_level', 0))

                if trust_level >= 70:
                    if stress_level <= 30:
                        summary['context_profile'] = 'High Trust, Low Stress'
                    else:
                        summary['context_profile'] = 'High Trust, High Stress'
                elif trust_level >= 40:
                    if stress_level <= 30:
                        summary['context_profile'] = 'Moderate Trust, Low Stress'
                    else:
                        summary['context_profile'] = 'Moderate Trust, High Stress'
                else:
                    if stress_level <= 30:
                        summary['context_profile'] = 'Low Trust, Low Stress'
                    else:
                        summary['context_profile'] = 'Low Trust, High Stress'

                # Extract key factors
                key_factors = []
                if trust_level < 40:
                    key_factors.append('Low Trust Environment')
                if stress_level > 70:
                    key_factors.append('High Stress Situation')

                valence = self._extract_numeric_value(context_vars.get('valence', 0))
                if valence < -0.5:
                    key_factors.append('Negative Mood')
                elif valence > 0.5:
                    key_factors.append('Positive Mood')

                summary['key_factors'] = key_factors

            # Extract agent confidence if available
            if last_output and isinstance(last_output, dict):
                confidence = last_output.get('confidence_score') or last_output.get('confidence')
                if confidence is not None:
                    try:
                        conf_val = float(confidence)
                        if conf_val >= 0.8:
                            summary['agent_confidence'] = 'High'
                        elif conf_val >= 0.6:
                            summary['agent_confidence'] = 'Moderate'
                        else:
                            summary['agent_confidence'] = 'Low'
                    except (ValueError, TypeError):
                        pass

            # Determine response quality based on semantic score
            if run.semantic_score is not None:
                try:
                    score = float(run.semantic_score)
                    if score >= 0.8:
                        summary['response_quality'] = 'Excellent'
                    elif score >= 0.6:
                        summary['response_quality'] = 'Good'
                    elif score >= 0.4:
                        summary['response_quality'] = 'Fair'
                    else:
                        summary['response_quality'] = 'Poor'
                except (ValueError, TypeError):
                    pass

            # Performance indicators
            summary['performance_indicators'] = {
                'semantic_score': run.semantic_score,
                'success_rate': run.success_rate,
                'mean_duration': run.mean_duration,
                'token_efficiency': self._calculate_token_efficiency(run)
            }

            return summary

        except Exception as e:
            logger.warning(f"Error generating context summary for run {run.id}: {e}")
            return {'error': str(e)}

    def _generate_context_analysis(self, run, context_vars, last_output, individual_runs):
        """Generate meaningful analysis data for human analysis."""
        try:
            analysis = {
                'context_impact': {},
                'agent_behavior': {},
                'performance_patterns': {},
                'recommendations': []
            }

            # Context impact analysis
            if context_vars:
                trust_level = self._extract_numeric_value(context_vars.get('trust_level', 0))
                stress_level = self._extract_numeric_value(context_vars.get('stress_level', 0))

                analysis['context_impact'] = {
                    'trust_influence': self._analyze_trust_impact(trust_level, run.semantic_score),
                    'stress_influence': self._analyze_stress_impact(stress_level, run.mean_duration),
                    'mood_influence': self._analyze_mood_impact(context_vars, last_output)
                }

            # Agent behavior analysis
            if last_output:
                analysis['agent_behavior'] = {
                    'response_length': len(str(last_output.get('agent_response', ''))) if last_output.get('agent_response') else 0,
                    'reasoning_depth': len(str(last_output.get('reasoning', ''))) if last_output.get('reasoning') else 0,
                    'confidence_level': last_output.get('confidence_score', 'N/A'),
                    'response_type': self._classify_response_type(last_output)
                }

            # Performance patterns
            if individual_runs and len(individual_runs) > 1:
                durations = [run.get('duration', 0) for run in individual_runs if run.get('duration')]
                if durations:
                    analysis['performance_patterns'] = {
                        'consistency': self._calculate_consistency(durations),
                        'improvement_trend': self._analyze_improvement_trend(individual_runs),
                        'variability': max(durations) - min(durations) if durations else 0
                    }

            # Generate recommendations
            analysis['recommendations'] = self._generate_recommendations(run, context_vars, analysis)

            return analysis

        except Exception as e:
            logger.warning(f"Error generating context analysis for run {run.id}: {e}")
            return {'error': str(e)}

    def _extract_numeric_value(self, value):
        """Extract numeric value from various formats."""
        if isinstance(value, dict) and 'value' in value:
            return float(value['value'])
        elif isinstance(value, (int, float)):
            return float(value)
        return 0.0

    def _analyze_trust_impact(self, trust_level, semantic_score):
        """Analyze how trust level impacts performance."""
        if semantic_score is None:
            return "Insufficient data"

        if trust_level >= 70 and semantic_score >= 0.7:
            return "High trust correlates with good performance"
        elif trust_level < 40 and semantic_score < 0.5:
            return "Low trust may be limiting performance"
        else:
            return "Trust level shows mixed correlation with performance"

    def _analyze_stress_impact(self, stress_level, mean_duration):
        """Analyze how stress level impacts response time."""
        if mean_duration is None:
            return "Insufficient data"

        if stress_level > 70 and mean_duration > 2000:
            return "High stress may be increasing response time"
        elif stress_level < 30 and mean_duration < 1500:
            return "Low stress environment enables faster responses"
        else:
            return "Stress level shows mixed correlation with response time"

    def _analyze_mood_impact(self, context_vars, last_output):
        """Analyze how mood impacts agent output."""
        if not last_output:
            return "Insufficient data"

        valence = self._extract_numeric_value(context_vars.get('valence', 0))
        response = str(last_output.get('agent_response', ''))

        if valence > 0.5 and ('positive' in response.lower() or 'good' in response.lower()):
            return "Positive mood reflected in optimistic response"
        elif valence < -0.5 and ('concern' in response.lower() or 'careful' in response.lower()):
            return "Negative mood reflected in cautious response"
        else:
            return "Mood impact on response tone unclear"

    def _classify_response_type(self, last_output):
        """Classify the type of agent response."""
        if not last_output or not last_output.get('agent_response'):
            return "No response"

        response = str(last_output.get('agent_response', '')).lower()

        if 'recommend' in response or 'suggest' in response:
            return "Recommendation"
        elif 'question' in response or '?' in response:
            return "Inquiry"
        elif 'explain' in response or 'because' in response:
            return "Explanation"
        else:
            return "General response"

    def _calculate_consistency(self, durations):
        """Calculate consistency score based on duration variance."""
        if len(durations) < 2:
            return "N/A"

        avg = sum(durations) / len(durations)
        variance = sum((d - avg) ** 2 for d in durations) / len(durations)
        std_dev = variance ** 0.5

        if std_dev / avg < 0.1:
            return "Very consistent"
        elif std_dev / avg < 0.2:
            return "Consistent"
        else:
            return "Variable"

    def _analyze_improvement_trend(self, individual_runs):
        """Analyze if performance improves across runs."""
        if len(individual_runs) < 2:
            return "Insufficient data"

        # Simple trend analysis based on duration
        durations = [run.get('duration', 0) for run in individual_runs if run.get('duration')]
        if len(durations) < 2:
            return "Insufficient data"

        if durations[-1] < durations[0]:
            return "Improving (faster responses)"
        elif durations[-1] > durations[0]:
            return "Declining (slower responses)"
        else:
            return "Stable performance"

    def _calculate_token_efficiency(self, run):
        """Calculate token efficiency score."""
        try:
            if run.total_input_tokens and run.total_output_tokens and run.semantic_score:
                total_tokens = run.total_input_tokens + run.total_output_tokens
                efficiency = float(run.semantic_score) / (total_tokens / 1000)  # Score per 1k tokens
                return round(efficiency, 3)
        except (TypeError, ZeroDivisionError):
            pass
        return "N/A"

    def _generate_recommendations(self, run, context_vars, analysis):
        """Generate actionable recommendations based on analysis."""
        recommendations = []

        # Performance-based recommendations
        if run.semantic_score is not None and run.semantic_score < 0.5:
            recommendations.append("Consider adjusting evaluation criteria or agent prompts")

        if run.mean_duration is not None and run.mean_duration > 3000:
            recommendations.append("Investigate potential performance bottlenecks")

        # Context-based recommendations
        if context_vars:
            trust_level = self._extract_numeric_value(context_vars.get('trust_level', 0))
            stress_level = self._extract_numeric_value(context_vars.get('stress_level', 0))

            if trust_level < 40:
                recommendations.append("Test with higher trust scenarios to validate agent capabilities")

            if stress_level > 70:
                recommendations.append("Consider stress-reduction strategies in agent design")

        # Behavior-based recommendations
        behavior = analysis.get('agent_behavior', {})
        if behavior.get('confidence_level') and isinstance(behavior['confidence_level'], (int, float)):
            if behavior['confidence_level'] < 0.5:
                recommendations.append("Review agent confidence calibration")

        return recommendations[:5]  # Limit to top 5 recommendations

    def _handle_template_test_sync(self, data):
        """Handle evaluation template testing requests (sync version)."""
        try:
            # Validate required fields for template testing
            if 'scenario_id' not in data:
                return JsonResponse({'error': "Missing required field: scenario_id"}, status=400)

            # Get parameters
            params = data.get('params', {})
            context_variables = params.get('context_variables', {})

            # Extract execution mode parameters
            use_real_llm = params.get('use_real_llm', False)
            use_real_tools = params.get('use_real_tools', False)
            use_real_db = params.get('use_real_db', False)

            # Get the scenario and template (now sync)
            from apps.main.models import EvaluationCriteriaTemplate
            scenario = get_object_or_404(BenchmarkScenario, id=data['scenario_id'])

            # Handle both saved templates and template data
            if 'evaluation_template_id' in data:
                template = get_object_or_404(EvaluationCriteriaTemplate, id=data['evaluation_template_id'])
                template_data = {
                    'id': template.id,
                    'name': template.name,
                    'description': template.description,
                    'criteria': template.criteria,
                    'contextual_criteria': template.contextual_criteria,
                    'context_variables': context_variables
                }
            elif 'evaluation_template_data' in data:
                template_data = data['evaluation_template_data']
                template_data['context_variables'] = context_variables
            else:
                raise ValueError("Either evaluation_template_id or evaluation_template_data must be provided")

            # Start the Celery task for template testing
            from apps.main.tasks.benchmark_tasks import run_template_test

            task = run_template_test.delay(
                scenario_id=str(scenario.id),
                template_data=template_data,
                params=params
            )

            logger.info(f"Started template test task {task.id} for scenario {scenario.id}")

            return JsonResponse({
                'success': True,
                'task_id': task.id,
                'message': f'Template test started for scenario "{scenario.name}". Task ID: {task.id}'
            })

        except Exception as e:
            logger.error("Error in template test", exc_info=True)
            return JsonResponse({'error': str(e)}, status=500)

    async def _handle_template_test(self, data):
        """Handle evaluation template testing requests."""
        try:
            # Validate required fields for template testing
            if 'scenario_id' not in data:
                return JsonResponse({'error': "Missing required field: scenario_id"}, status=400)

            # Get parameters
            params = data.get('params', {})
            runs = min(params.get('runs', 1), 5)  # Limit to 5 runs for template testing
            semantic_evaluation = params.get('semantic_evaluation', True)
            context_variables = params.get('context_variables', {})
            multi_range_evaluation = params.get('multi_range_contextual_evaluation', False)
            selected_combinations = params.get('selected_combinations', [])
            selected_combination_indices = params.get('selected_combination_indices', [])

            # Get the scenario and template
            @database_sync_to_async
            def _get_scenario_and_template():
                from apps.main.models import EvaluationCriteriaTemplate
                scenario = get_object_or_404(BenchmarkScenario, id=data['scenario_id'])

                # Handle both saved templates and template data
                if 'evaluation_template_id' in data:
                    template = get_object_or_404(EvaluationCriteriaTemplate, id=data['evaluation_template_id'])
                    template_data = {
                        'id': template.id,
                        'name': template.name,
                        'description': template.description,
                        'criteria': template.criteria,
                        'contextual_criteria': template.contextual_criteria,
                        'context_variables': context_variables
                    }
                elif 'evaluation_template_data' in data:
                    template_data = data['evaluation_template_data']
                    template_data['context_variables'] = context_variables
                    template = None  # No saved template
                else:
                    raise ValueError("Either evaluation_template_id or evaluation_template_data must be provided")

                return scenario, template, template_data

            scenario, template, template_data = await _get_scenario_and_template()

            # Start the Celery task for template testing instead of running synchronously
            from apps.main.tasks.benchmark_tasks import run_template_test

            task = run_template_test.delay(
                scenario_id=str(scenario.id),
                template_data=template_data,
                params=params
            )

            logger.info(f"Started template test task {task.id} for scenario {scenario.id}")

            return JsonResponse({
                'success': True,
                'task_id': task.id,
                'message': f'Template test started for scenario "{scenario.name}". Task ID: {task.id}'
            })

        except Exception as e:
            logger.error("Error in template test", exc_info=True)

            # Emit error event via WebSocket for real-time feedback
            try:
                from apps.main.services.event_service import EventService
                import traceback
                import asyncio

                # Create task to emit error event
                asyncio.create_task(
                    EventService.emit_debug_info(
                        level='error',
                        message=f"Template test error: {str(e)}",
                        source='BenchmarkRunView',
                        details={
                            'error_type': type(e).__name__,
                            'error_message': str(e),
                            'traceback': traceback.format_exc(),
                            'endpoint': 'POST /admin/benchmarks/api/run/ (template test)'
                        }
                    )
                )
            except Exception as emit_error:
                logger.warning(f"Failed to emit error event: {emit_error}")

            return JsonResponse({'error': str(e)}, status=500)


class BenchmarkRunStopView(View):
    """Stop a running benchmark task."""

    async def post(self, request, task_id):
        """Stop a benchmark task by task ID."""
        try:
            # Import Celery app
            from celery import current_app as celery_app

            # Revoke the task
            celery_app.control.revoke(task_id, terminate=True)

            logger.info(f"Stopped benchmark task: {task_id}")

            return JsonResponse({
                'success': True,
                'message': f'Task {task_id} has been stopped.'
            })

        except Exception as e:
            logger.error(f"Error stopping task {task_id}: {e}", exc_info=True)
            return JsonResponse({'error': str(e)}, status=500)


class BenchmarkTaskStatusView(View):
    """
    Check the status of a benchmark task with enhanced error reporting.

    This view provides detailed information about task execution including:
    - Task completion status
    - Detailed error information with classification
    - Progress metadata
    - Error summaries for failed tasks
    """

    async def get(self, request, task_id):
        """
        Get the status of a benchmark task by task ID with enhanced error reporting.

        Args:
            request: HTTP request object
            task_id: Celery task ID to check status for

        Returns:
            JsonResponse with detailed task status including error information
        """
        try:
            from celery.result import AsyncResult

            # Get the task result
            result = AsyncResult(task_id)

            # Check task status
            if result.ready():
                if result.failed():
                    # Enhanced error handling for failed tasks
                    error_info = self._extract_error_information(result)

                    return JsonResponse({
                        'status': 'failed',
                        'error': error_info.get('primary_error', str(result.result)),
                        'task_id': task_id,
                        'error_details': error_info,
                        'has_errors': True,
                        'has_critical_errors': error_info.get('has_critical_errors', True)
                    })
                else:
                    # Task completed successfully - check for non-critical errors
                    task_result = result.result
                    error_info = self._extract_success_errors(task_result)

                    # Also check the BenchmarkRun object for errors
                    benchmark_run_error_info = await self._extract_benchmark_run_errors(task_result)

                    # Merge error information from both sources
                    merged_error_info = self._merge_error_info(error_info, benchmark_run_error_info)

                    response_data = {
                        'status': 'completed',
                        'result': task_result,
                        'task_id': task_id,
                        'runs': task_result.get('runs', []) if isinstance(task_result, dict) else [],
                        'has_errors': merged_error_info.get('has_errors', False),
                        'has_critical_errors': merged_error_info.get('has_critical_errors', False)
                    }

                    # Add error information if present
                    if merged_error_info.get('has_errors'):
                        response_data['error_details'] = merged_error_info
                        response_data['error_summary'] = merged_error_info.get('error_summary', {})

                    return JsonResponse(response_data)
            else:
                # Task is still running or pending
                status = result.status.lower() if result.status else 'pending'

                # Get task metadata if available
                meta = result.info if hasattr(result, 'info') and result.info else {}

                # Extract error information from metadata if present
                meta_errors = self._extract_meta_errors(meta)

                response_data = {
                    'status': status,
                    'task_id': task_id,
                    'meta': meta,
                    'has_errors': meta_errors.get('has_errors', False)
                }

                if meta_errors.get('has_errors'):
                    response_data['error_details'] = meta_errors

                return JsonResponse(response_data)

        except Exception as e:
            logger.error(f"Error checking task status {task_id}: {e}", exc_info=True)
            return JsonResponse({'error': str(e)}, status=500)

    def _extract_error_information(self, result):
        """
        Extract detailed error information from a failed Celery task result.

        Args:
            result: AsyncResult object for a failed task

        Returns:
            dict: Detailed error information
        """
        error_info = {
            'primary_error': str(result.result),
            'has_errors': True,
            'has_critical_errors': True,
            'error_summary': {
                'total_errors': 1,
                'critical_errors': 1,
                'warnings': 0,
                'info_messages': 0
            },
            'errors': []
        }

        try:
            # Check if the result has detailed error information
            if hasattr(result, 'info') and isinstance(result.info, dict):
                meta = result.info

                # Extract errors from meta if available
                if 'errors' in meta and isinstance(meta['errors'], list):
                    error_info['errors'] = meta['errors']
                    error_info['error_summary'] = self._calculate_error_summary(meta['errors'])
                    error_info['has_critical_errors'] = error_info['error_summary']['critical_errors'] > 0

                # Add primary error information
                if 'exc_type' in meta and 'exc_message' in meta:
                    primary_error = {
                        'type': 'critical',
                        'level': 'error',
                        'message': meta['exc_message'],
                        'source': 'celery_task',
                        'details': {
                            'error_type': meta['exc_type'],
                            'task_status': meta.get('status', 'Task failed')
                        }
                    }

                    # Add to errors list if not already present
                    if not any(e.get('message') == primary_error['message'] for e in error_info['errors']):
                        error_info['errors'].append(primary_error)
                        error_info['error_summary'] = self._calculate_error_summary(error_info['errors'])

        except Exception as e:
            logger.warning(f"Failed to extract detailed error information: {e}")

        return error_info

    def _extract_success_errors(self, task_result):
        """
        Extract error information from a successful task that may have non-critical errors.

        Args:
            task_result: The result dictionary from a successful task

        Returns:
            dict: Error information if present
        """
        if not isinstance(task_result, dict):
            return {'has_errors': False, 'has_critical_errors': False}

        # Check for errors in the task result
        errors = task_result.get('errors', [])
        if not errors:
            return {'has_errors': False, 'has_critical_errors': False}

        error_summary = self._calculate_error_summary(errors)

        return {
            'has_errors': len(errors) > 0,
            'has_critical_errors': error_summary['critical_errors'] > 0,
            'errors': errors,
            'error_summary': error_summary
        }

    def _extract_meta_errors(self, meta):
        """
        Extract error information from task metadata (for running tasks).

        Args:
            meta: Task metadata dictionary

        Returns:
            dict: Error information if present
        """
        if not isinstance(meta, dict):
            return {'has_errors': False}

        errors = meta.get('errors', [])
        if not errors:
            return {'has_errors': False}

        error_summary = self._calculate_error_summary(errors)

        return {
            'has_errors': len(errors) > 0,
            'has_critical_errors': error_summary['critical_errors'] > 0,
            'errors': errors,
            'error_summary': error_summary
        }

    def _calculate_error_summary(self, errors):
        """
        Calculate error summary statistics from a list of errors.

        Args:
            errors: List of error dictionaries

        Returns:
            dict: Error summary with counts by type
        """
        if not isinstance(errors, list):
            return {
                'total_errors': 0,
                'critical_errors': 0,
                'warnings': 0,
                'info_messages': 0
            }

        critical_count = 0
        warning_count = 0
        info_count = 0

        for error in errors:
            if not isinstance(error, dict):
                continue

            error_type = error.get('type', '').lower()
            error_level = error.get('level', '').lower()

            if error_type == 'critical' or error_level == 'error':
                critical_count += 1
            elif error_type == 'warning' or error_level == 'warning':
                warning_count += 1
            else:
                info_count += 1

        return {
            'total_errors': len(errors),
            'critical_errors': critical_count,
            'warnings': warning_count,
            'info_messages': info_count
        }

    async def _extract_benchmark_run_errors(self, task_result):
        """
        Extract error information from the BenchmarkRun object.

        Args:
            task_result: The result dictionary from a successful task

        Returns:
            dict: Error information from BenchmarkRun if present
        """
        if not isinstance(task_result, dict):
            return {'has_errors': False, 'has_critical_errors': False}

        # Get the benchmark run ID from the task result
        benchmark_run_id = task_result.get('benchmark_run_id')
        if not benchmark_run_id:
            return {'has_errors': False, 'has_critical_errors': False}

        try:
            # Get the BenchmarkRun object (use sync_to_async for async context)
            from asgiref.sync import sync_to_async
            benchmark_run = await sync_to_async(BenchmarkRun.objects.get)(id=benchmark_run_id)

            # Check if it has errors using the model methods
            if not benchmark_run.has_errors():
                return {'has_errors': False, 'has_critical_errors': False}

            # Extract errors from raw_results
            raw_results = benchmark_run.raw_results or {}
            errors = raw_results.get('errors', [])
            error_summary = raw_results.get('error_summary', {})

            # If no error_summary, calculate it
            if not error_summary and errors:
                error_summary = self._calculate_error_summary(errors)

            return {
                'has_errors': len(errors) > 0,
                'has_critical_errors': benchmark_run.has_critical_errors(),
                'errors': errors,
                'error_summary': error_summary,
                'source': 'benchmark_run'
            }

        except BenchmarkRun.DoesNotExist:
            logger.warning(f"BenchmarkRun with ID {benchmark_run_id} not found")
            return {'has_errors': False, 'has_critical_errors': False}
        except Exception as e:
            logger.error(f"Error extracting BenchmarkRun errors: {e}")
            return {'has_errors': False, 'has_critical_errors': False}

    def _merge_error_info(self, task_error_info, benchmark_run_error_info):
        """
        Merge error information from task result and BenchmarkRun object.

        Args:
            task_error_info: Error info from task result
            benchmark_run_error_info: Error info from BenchmarkRun object

        Returns:
            dict: Merged error information
        """
        # If neither has errors, return no errors
        if not task_error_info.get('has_errors') and not benchmark_run_error_info.get('has_errors'):
            return {'has_errors': False, 'has_critical_errors': False}

        # Start with the more comprehensive source (usually BenchmarkRun)
        if benchmark_run_error_info.get('has_errors'):
            merged = benchmark_run_error_info.copy()

            # Add task errors if they exist and are different
            task_errors = task_error_info.get('errors', [])
            benchmark_errors = merged.get('errors', [])

            # Merge errors, avoiding duplicates
            all_errors = benchmark_errors.copy()
            for task_error in task_errors:
                # Check if this error is already in benchmark errors
                if not any(be.get('message') == task_error.get('message') for be in benchmark_errors):
                    all_errors.append(task_error)

            # Update merged info
            merged['errors'] = all_errors
            merged['error_summary'] = self._calculate_error_summary(all_errors)
            merged['has_errors'] = len(all_errors) > 0
            merged['has_critical_errors'] = merged['error_summary']['critical_errors'] > 0

        else:
            # Use task error info as fallback
            merged = task_error_info.copy()

        return merged