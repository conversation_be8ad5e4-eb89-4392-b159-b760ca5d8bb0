import os
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from apps.main.models import GenericAgent, AgentRole, BenchmarkMetric, AppliedSeedingCommand # Import the tracking model

class Command(BaseCommand):
    help = 'Creates the core agent team for the Game of Life system. Skips if already applied by run_seeders.'
    # change the command name (incremental number) to ensure the db gets updated
    COMMAND_NAME = "2"+os.path.splitext(os.path.basename(__file__))[0] # Keep this custom name

    def handle(self, *args, **kwargs):
        # Re-add the initial check for the command itself
        if AppliedSeedingCommand.objects.filter(command_name=self.COMMAND_NAME).exists():
            self.stdout.write(self.style.WARNING(f"Command '{self.COMMAND_NAME}' has already been applied by run_seeders. Skipping."))
            return

        self.stdout.write(self.style.SUCCESS(f"Running command '{self.COMMAND_NAME}' logic (will update/create individual items)..."))

        try:
            # 2. Execute the seeding logic within a transaction
            # Note: Individual methods use get_or_create, ensuring internal idempotency.
            with transaction.atomic():
                # Create benchmark metrics first
                self.create_benchmark_metrics()

                # Create tools that agents will use
                #self.create_agent_tools()

                # Create the core agents
                self.create_mentor_agent()
                self.create_orchestrator_agent()
                self.create_resource_agent()
                self.create_engagement_agent()
                self.create_psychological_agent()
                self.create_strategy_agent()
                self.create_wheel_activity_agent()
                self.create_ethical_agent()
                self.create_dispatcher_agent()
                self.create_error_handler_agent()

                # Link benchmark metrics to agents
                #self.link_metrics_to_agents()

                # Ensure AppliedSeedingCommand.objects.create(...) remains removed from here.
                # run_seeders.py handles the recording upon successful completion of this command.
                self.stdout.write(self.style.SUCCESS(f"Finished command '{self.COMMAND_NAME}' logic! Agent team seeded/updated."))

        except Exception as e:
            # Catch any exception during the transaction
            self.stderr.write(self.style.ERROR(f"Command '{self.COMMAND_NAME}' failed internally: {str(e)}"))
            # Re-raise the exception so run_seeders knows it failed and won't record success
            raise CommandError(f"Command '{self.COMMAND_NAME}' failed: {e}") from e
    

    def create_benchmark_metrics(self):
        """Create the benchmark metrics for agent evaluation"""
        self.stdout.write('Creating benchmark metrics...')
        
        # Shared metrics for all agents
        BenchmarkMetric.objects.get_or_create(
            code='processing_time',
            defaults={
                'name': 'Processing Time',
                'description': 'Time taken for agent to complete processing',
                'data_type': 'duration',
                'unit': 'seconds',
                'target_value': 2.0,
                'applicable_roles': list(dict(AgentRole.choices).keys())
            }
        )
        
        BenchmarkMetric.objects.get_or_create(
            code='error_rate',
            defaults={
                'name': 'Error Rate',
                'description': 'Percentage of agent runs that result in errors',
                'data_type': 'numeric',
                'unit': 'percent',
                'target_value': 0.0,
                'applicable_roles': list(dict(AgentRole.choices).keys())
            }
        )
        
        BenchmarkMetric.objects.get_or_create(
            code='schema_compliance',
            defaults={
                'name': 'Schema Compliance',
                'description': 'Percentage of outputs that fully comply with schema',
                'data_type': 'numeric',
                'unit': 'percent',
                'target_value': 100.0,
                'applicable_roles': list(dict(AgentRole.choices).keys())
            }
        )
        
        # Mentor Agent specific metrics
        BenchmarkMetric.objects.get_or_create(
            code='communication_quality',
            defaults={
                'name': 'Communication Quality',
                'description': 'Quality of natural language interaction',
                'data_type': 'numeric',
                'unit': 'score',
                'target_value': 9.0,
                'applicable_roles': ['mentor']
            }
        )
        
        BenchmarkMetric.objects.get_or_create(
            code='personalization_level',
            defaults={
                'name': 'Personalization Level',
                'description': 'How well communication is tailored to user preferences',
                'data_type': 'numeric',
                'unit': 'score',
                'target_value': 8.0,
                'applicable_roles': ['mentor']
            }
        )
        
        # Resource Agent specific metrics
        BenchmarkMetric.objects.get_or_create(
            code='resource_validation_accuracy',
            defaults={
                'name': 'Resource Validation Accuracy',
                'description': 'Accuracy of resource availability assessments',
                'data_type': 'numeric',
                'unit': 'percent',
                'target_value': 95.0,
                'applicable_roles': ['resource']
            }
        )
        
        # Psychological Agent specific metrics
        BenchmarkMetric.objects.get_or_create(
            code='trait_assessment_consistency',
            defaults={
                'name': 'Trait Assessment Consistency',
                'description': 'Consistency of trait assessments over time',
                'data_type': 'numeric',
                'unit': 'score',
                'target_value': 8.5,
                'applicable_roles': ['psychological']
            }
        )
        
        # Ethical Agent specific metrics
        BenchmarkMetric.objects.get_or_create(
            code='ethical_coverage',
            defaults={
                'name': 'Ethical Coverage',
                'description': 'Percentage of ethical principles assessed in validation',
                'data_type': 'numeric',
                'unit': 'percent',
                'target_value': 100.0,
                'applicable_roles': ['ethical']
            }
        )
        

    def create_mentor_agent(self):
        """Create the Mentor Agent"""
        self.stdout.write('Creating Mentor Agent...')
        
        agent, created = GenericAgent.objects.get_or_create(
            role=AgentRole.MENTOR,
            defaults={
                'version': '1.0.0',
                'is_active': True,
                'description': 'Serves as the primary user-facing conversational interface within workflow graphs, builds trust and relationship with the user through consistent, supportive interactions, processes conversational context passed from the ConversationDispatcher, presents activities, explanations, and feedback in user-friendly language, maintains the philosophical framing of the game throughout all interactions, and adjusts tone and framing based on user-specific communication guidelines.',
                'system_instructions': """You are the Mentor Agent, the primary user-facing conversational interface in the Game of Life system. You work in conjunction with the ConversationDispatcher, which handles initial message classification and routing. Your focus is on providing personalized, conversational responses based on workflow results.

    Core responsibilities:
    1. Process the pre-classified context packet from the dispatcher
    2. Enhance context with user-specific communication preferences
    3. Retrieve relevant historical context for continuity
    4. Generate natural language responses with clear sections
    5. Structure natural language responses with appropriate philosophical framing
    6. Format outputs for downstream agents in the workflow
    7. When appropriate or requested, provide user-friendly presentation of activities, explanations, and feedback

    Communication guidelines:
    - Be **as concise as possible** (one short sentence if possible), unless asked otherwise by the user
    - Select appropriate tone based on trust phase and preferences
    - Determine detail level based on user history
    - Choose metaphors and examples that resonate with user
    - Balance philosophical framing with practical instructions
    - Include appropriate emotion and enthusiasm calibration
    - Format responses for optimal clarity and engagement

    Never:
    - Directly query or modify the data model beyond access permissions
    - Make substantive decisions about challenges or psychological assessments
    - Implement complex reasoning about user's state (defer to specialized agents)
    - Overwhelm the user with technical explanations or system mechanics
    - Use judgmental language or pressure when users refuse activities""",
                'input_schema': {
                    'type': 'object',
                    'properties': {
                        'context_packet': {
                            'type': 'object',
                            'properties': {
                                'user_id': {'type': 'string'},
                                'session_timestamp': {'type': 'string'},
                                'reported_mood': {'type': 'string'},
                                'reported_environment': {'type': 'string'},
                                'reported_time_availability': {'type': 'string'},
                                'reported_focus': {'type': 'string'},
                                'workflow_type': {'type': 'string'},
                                'user_ws_session_name': {'type': 'string'}
                            }
                        },
                        'workflow_id': {'type': 'string'},
                        'wheel': {'type': ['object', 'null']},
                        'activity_details': {'type': ['object', 'null']}
                    },
                    'required': ['context_packet', 'workflow_id']
                },
                'output_schema': {
                    'type': 'object',
                    'properties': {
                        'user_response': {'type': 'string'},
                        'context_packet': {'type': 'object'},
                        'next_agent': {'type': 'string'},
                        'forwardTo': {'type': 'string'}
                    },
                    'required': ['user_response', 'context_packet']
                },
                'state_schema': {
                    'type': 'object',
                    'properties': {
                        'current_stage': {'type': 'string'},
                        'last_agent': {'type': 'string'},
                        'output_data': {'type': 'object'}
                    }
                },
                'memory_schema': {
                    'type': 'object',
                    'properties': {
                        'communication_preferences': {'type': 'object'},
                        'conversation_context': {'type': 'object'},
                        'effective_approaches': {'type': 'object'}
                    }
                },
                'langgraph_node_class': 'apps.main.agents.mentor_agent.MentorAgent',
                'processing_timeout': 45,
                'read_models': [
                    'user.UserProfile', 
                    'main.HistoryEvent', 
                    'main.UserFeedback', 
                    'activity.ActivityTailored', 
                    'main.Wheel', 
                    'main.WheelItem',
                    'user.TrustLevel'
                ],
                'write_models': [
                    'main.HistoryEvent',
                    'main.UserFeedback',
                    'user.CurrentMood'
                ],
                'recommend_models': [
                    'user.TrustLevel'
                ]
            }
        )
        
        self.stdout.write(f'{"Created" if created else "Updated"} Mentor Agent')
        return agent
        
    def create_orchestrator_agent(self):
        """Create the Orchestrator Agent"""
        self.stdout.write('Creating Orchestrator Agent...')
        
        agent, created = GenericAgent.objects.get_or_create(
            role=AgentRole.ORCHESTRATOR,
            defaults={
                'version': '1.0.0',
                'is_active': True,
                'description': 'Coordinates the flow of information between all specialized agents, determines which agents to invoke and in what sequence based on current task, evaluates and makes decisions between specialized agent recommendations, implements a priority framework for decision-making, and manages error handling and recovery for the entire agent system.',
                'system_instructions': """You are the Orchestrator Agent, responsible for coordinating the multi-agent workflow in the Game of Life system. You direct tasks to specialized agents, integrate their outputs, and manage the overall process flow.

Core responsibilities:
1. Determine appropriate agent sequences based on task type
2. Distribute relevant context to specialized agents
3. Manage workflow state transitions and agent dependencies
4. Integrate outputs from multiple agents into coherent decisions
5. Resolve conflicts using established priority framework
6. Handle errors and implement recovery mechanisms

Decision-making guidelines:
- Apply priority hierarchy for conflict resolution:
  1. Safety and psychological well-being (highest)
  2. Ethical alignment and transparency 
  3. Psychological appropriateness and growth potential
  4. Resource feasibility and practical implementation
  5. Engagement optimization and enjoyment (lowest)
- Ensure full traceability of decisions to source recommendations
- Maintain workflow state integrity across agent transitions
- Document all workflow transitions in HistoryLog

Never:
- Implement domain-specific logic that belongs to specialized agents
- Bypass ethical validation for efficiency
- Make subjective assessments of user psychology
- Process natural language directly (rely on Mentor Agent)
- Overwhelm specialized agents with unnecessary context""",
                'input_schema': {
                    'type': 'object',
                    'properties': {
                        'task_type': {'type': 'string'},
                        'context_data': {'type': 'object'},
                        'workflow_id': {'type': 'string'},
                        'user_profile_id': {'type': 'string'}
                    },
                    'required': ['task_type', 'workflow_id', 'user_profile_id']
                },
                'output_schema': {
                    'type': 'object',
                    'properties': {
                        'agent_sequence': {'type': 'array'},
                        'agent_data_packages': {'type': 'object'},
                        'workflow_state': {'type': 'object'},
                        'decision_log': {'type': 'array'}
                    },
                    'required': ['agent_sequence', 'agent_data_packages', 'workflow_state']
                },
                'state_schema': {
                    'type': 'object',
                    'properties': {
                        'current_workflow_stage': {'type': 'string'},
                        'agent_activation_status': {'type': 'object'},
                        'collected_outputs': {'type': 'object'}
                    }
                },
                'memory_schema': {
                    'type': 'object'  # Stateless for MVP
                },
                'langgraph_node_class': 'apps.agents.nodes.OrchestratorAgentNode',
                'processing_timeout': 60,
                'read_models': [
                    'user.UserProfile',
                    'main.HistoryEvent',
                    'main.AgentRun',
                    'main.AgentRecommendation',
                    'user.TrustLevel',
                    'activity.ActivityTailored',
                    'main.Wheel',
                    'main.WheelItem'
                ],
                'write_models': [
                    'main.HistoryEvent',
                    'main.AgentRun'
                ],
                'recommend_models': []
            }
        )
        
        self.stdout.write(f'{"Created" if created else "Updated"} Orchestrator Agent')
        return agent
        
    def create_resource_agent(self):
        """Create the Resource & Capacity Management Agent"""
        self.stdout.write('Creating Resource & Capacity Management Agent...')
        
        agent, created = GenericAgent.objects.get_or_create(
            role=AgentRole.RESOURCE,
            defaults={
                'version': '1.0.0',
                'is_active': True,
                'description': "Analyzes user's available physical resources, time, and environmental context, identifies constraints and limitations that affect activity feasibility, maps resource requirements to user's current inventory and capabilities, validates environmental compatibility with activities, and provides feasibility assessments for potential activities.",
                'system_instructions': """You are the Resource & Capacity Management Agent, responsible for analyzing the user's physical resources, environment, time availability, and capabilities to determine activity feasibility.

Core responsibilities:
1. Analyze the user's current environmental context
2. Evaluate available resources against activity requirements
3. Assess time availability and constraints
4. Determine physical and cognitive capability limits
5. Identify resource-based limitations
6. Provide feasibility assessments for activities

Analysis guidelines:
- Compare environmental context with UserEnvironment records
- Match available resources against Inventory records
- Calculate time availability parameters from user reports
- Assess capability constraints based on documented abilities
- Apply conservative default assumptions when data is uncertain
- Provide clear constraint boundaries with specific parameters
- Include confidence ratings for all assessments

Never:
- Make psychological assessments outside resource domain
- Modify core user psychological profile data
- Adjust activity challenge levels directly
- Override ethical constraints for resource optimization
- Assume resource availability without validation""",
                'input_schema': {
                    'type': 'object',
                    'properties': {
                        'user_profile_id': {'type': 'string'},
                        'environmental_context': {'type': 'object'},
                        'time_availability': {'type': 'object'},
                        'analysis_type': {'type': 'string'},
                        'activity_ids': {'type': ['array', 'null']}
                    },
                    'required': ['user_profile_id', 'analysis_type']
                },
                'output_schema': {
                    'type': 'object',
                    'properties': {
                        'resource_context': {'type': 'object'},
                        'feasibility_ratings': {'type': ['object', 'null']},
                        'constraint_specifications': {'type': 'object'},
                        'confidence_metrics': {'type': 'object'}
                    },
                    'required': ['resource_context', 'constraint_specifications']
                },
                'state_schema': {
                    'type': 'object',
                    'properties': {
                        'analysis_stage': {'type': 'string'},
                        'collected_resource_data': {'type': 'object'}
                    }
                },
                'memory_schema': {
                    'type': 'object'  # Stateless for MVP
                },
                'langgraph_node_class': 'apps.agents.nodes.ResourceAgentNode',
                'processing_timeout': 30,
                'read_models': [
                    'user.UserEnvironment',
                    'user.UserEnvironmentPhysicalProperties',
                    'user.UserEnvironmentSocialContext',
                    'user.UserEnvironmentActivitySupport',
                    'user.Inventory',
                    'user.UserResource',
                    'user.Skill',
                    'user.UserLimitation',
                    'activity.ActivityTailored',
                    'activity.ActivityEnvRequirement',
                    'activity.ActivityUserRequirement'
                ],
                'write_models': [
                    'user.Inventory',
                    'user.UserResource'
                ],
                'recommend_models': [
                    'user.Skill',
                    'user.UserLimitation'
                ]
            }
        )
        
        self.stdout.write(f'{"Created" if created else "Updated"} Resource & Capacity Management Agent')
        return agent
        
    def create_engagement_agent(self):
        """Create the Engagement & Pattern Analytics Agent"""
        self.stdout.write('Creating Engagement & Pattern Analytics Agent...')
        
        agent, created = GenericAgent.objects.get_or_create(
            role=AgentRole.ENGAGEMENT,
            defaults={
                'version': '1.0.0',
                'is_active': True,
                'description': "Integrates quantitative engagement metrics with qualitative user feedback, analyzes historical interaction patterns alongside real-time sentiment, identifies recurring behavioral and emotional themes to guide system improvements, provides data-driven view of user engagement and satisfaction, and supports targeted adjustments to activities based on pattern insights.",
                'system_instructions': """You are the Engagement & Pattern Analytics Agent, responsible for analyzing user interaction patterns, activity preferences, and engagement trends to optimize future recommendations.

Core responsibilities:
1. Analyze historical activity completion and refusal patterns
2. Identify domain preferences and activity format preferences
3. Detect time-of-day and contextual patterns
4. Evaluate sentiment patterns across different domains and activities
5. Compare stated preferences with behavioral evidence
6. Update pattern memory with new insights

Analysis guidelines:
- Calculate domain-specific completion rates with statistical significance
- Identify temporal patterns (time-of-day, day-of-week effects)
- Map engagement trends over time to detect shifts
- Correlate quantitative metrics with qualitative feedback
- Document preference contradictions or evolutions
- Provide confidence ratings for all pattern identifications
- Consider both recent feedback and historical trends

Never:
- Rely exclusively on recent feedback without historical context
- Make direct psychological assessments beyond engagement data
- Filter or bias negative feedback
- Overemphasize either quantitative or qualitative data
- Treat coincidental patterns as statistically significant""",
                'input_schema': {
                    'type': 'object',
                    'properties': {
                        'user_profile_id': {'type': 'string'},
                        'time_period': {'type': ['object', 'null']},
                        'analysis_type': {'type': 'string'},
                        'specific_queries': {'type': ['object', 'null']}
                    },
                    'required': ['user_profile_id', 'analysis_type']
                },
                'output_schema': {
                    'type': 'object',
                    'properties': {
                        'domain_preferences': {'type': 'object'},
                        'temporal_patterns': {'type': 'object'},
                        'format_preferences': {'type': 'object'},
                        'pattern_confidence': {'type': 'object'},
                        'trend_analysis': {'type': 'object'}
                    },
                    'required': ['domain_preferences', 'pattern_confidence']
                },
                'state_schema': {
                    'type': 'object',
                    'properties': {
                        'analysis_stage': {'type': 'string'},
                        'historical_data': {'type': 'object'}
                    }
                },
                'memory_schema': {
                    'type': 'object',
                    'properties': {
                        'domain_engagement_metrics': {'type': 'object'},
                        'pattern_confidence_scores': {'type': 'object'},
                        'activity_response_patterns': {'type': 'object'}
                    }
                },
                'langgraph_node_class': 'apps.agents.nodes.EngagementAndPatternAgentNode',
                'processing_timeout': 45,
                'read_models': [
                    'main.HistoryEvent',
                    'main.UserFeedback',
                    'user.Preference',
                    'activity.ActivityTailored',
                    'main.Wheel',
                    'main.WheelItem'
                ],
                'write_models': [],
                'recommend_models': [
                    'user.Preference',
                    'user.UserTraitInclination',
                    'user.TrustLevel'
                ]
            }
        )
        
        self.stdout.write(f'{"Created" if created else "Updated"} Engagement & Pattern Analytics Agent')
        return agent
    
    def create_psychological_agent(self):
        """Create the Psychological Monitoring Agent"""
        self.stdout.write('Creating Psychological Monitoring Agent...')
        
        agent, created = GenericAgent.objects.get_or_create(
            role=AgentRole.PSYCHOLOGICAL,
            defaults={
                'version': '1.0.0',
                'is_active': True,
                'description': "Assesses user's current psychological state and trust level, analyzes relationship between activities and user's belief system, evaluates appropriate challenge levels based on user traits, identifies growth opportunities and psychological readiness, and monitors emotional vulnerabilities and safety boundaries.",
                'system_instructions': """You are the Psychological Monitoring Agent, responsible for assessing the user's psychological state, determining appropriate challenge levels, and identifying growth opportunities that support their goals.

Core responsibilities:
1. Evaluate current mood and emotional state
2. Determine trust phase (Foundation or Expansion)
3. Analyze trait expressions and growth opportunities
4. Identify belief-based limitations or supports
5. Assess goal alignment with activities
6. Recommend appropriate challenge calibration
7. Identify safety boundaries and vulnerabilities

Assessment guidelines:
- Use HEXACO traits framework for personality assessment
- Apply phase-based approach to trust development
- Identify traits that support or hinder goal achievement
- Connect belief systems to trait expressions
- Calculate appropriate challenge levels based on trust phase
- Document growth opportunities with supporting evidence
- Maintain psychological safety throughout recommendations

Never:
- Make resource feasibility assessments (defer to Resource Agent)
- Implement complex activity selection logic (defer to Activity Agent)
- Bypass ethical oversight for growth potential
- Push beyond appropriate challenge levels for trust phase
- Treat correlation as causation in psychological patterns
- Suggest activities that could trigger identified vulnerabilities""",
                'input_schema': {
                    'type': 'object',
                    'properties': {
                        'user_profile_id': {'type': 'string'},
                        'current_mood_data': {'type': ['object', 'null']},
                        'resource_context': {'type': ['object', 'null']},
                        'engagement_patterns': {'type': ['object', 'null']},
                        'assessment_type': {'type': 'string'}
                    },
                    'required': ['user_profile_id', 'assessment_type']
                },
                'output_schema': {
                    'type': 'object',
                    'properties': {
                        'psychological_state': {'type': 'object'},
                        'trust_phase': {'type': 'string'},
                        'trait_analysis': {'type': 'object'},
                        'growth_opportunities': {'type': 'array'},
                        'challenge_calibration': {'type': 'object'},
                        'belief_implications': {'type': 'object'},
                        'safety_boundaries': {'type': 'object'}
                    },
                    'required': ['psychological_state', 'trust_phase', 'challenge_calibration', 'safety_boundaries']
                },
                'state_schema': {
                    'type': 'object',
                    'properties': {
                        'assessment_stage': {'type': 'string'},
                        'trait_data': {'type': 'object'},
                        'trust_metrics': {'type': 'object'}
                    }
                },
                'memory_schema': {
                    'type': 'object',
                    'properties': {
                        'trait_expression_patterns': {'type': 'object'},
                        'trust_development_history': {'type': 'object'},
                        'growth_area_indicators': {'type': 'object'},
                        'vulnerability_triggers': {'type': 'object'}
                    }
                },
                'langgraph_node_class': 'apps.agents.nodes.PsychologicalAgentNode',
                'processing_timeout': 60,
                'read_models': [
                    'user.UserTraitInclination',
                    'user.Belief',
                    'user.BeliefEvidence',
                    'user.TrustLevel',
                    'user.UserGoal',
                    'user.Inspiration',
                    'user.GoalInspiration',
                    'user.CurrentMood',
                    'user.UserLimitation',
                    'activity.ActivityTailored'
                ],
                'write_models': [
                    'user.CurrentMood'
                ],
                'recommend_models': [
                    'user.TrustLevel',
                    'user.Belief',
                    'user.UserTraitInclination'
                ]
            }
        )
        
        self.stdout.write(f'{"Created" if created else "Updated"} Psychological Monitoring Agent')
        return agent
        
    def create_strategy_agent(self):
        """Create the Strategy Agent"""
        self.stdout.write('Creating Strategy Agent...')
        
        agent, created = GenericAgent.objects.get_or_create(
            role=AgentRole.STRATEGY,
            defaults={
                'version': '1.0.0',
                'is_active': True,
                'description': "Formulates comprehensive activity selection strategy based on multi-agent inputs, performs gap analysis between user traits and activity requirements, balances challenge calibration with domain distribution, aligns activity strategy with user's growth trajectory, and establishes boundary conditions and constraints for activity selection.",
                'system_instructions': """You are the Strategy Agent, responsible for formulating comprehensive strategies for activity selection that balance engagement, challenge, and growth based on multi-agent inputs.

Core responsibilities:
1. Synthesize inputs from all specialized agents into coherent strategy
2. Perform gap analysis between user traits and activity requirements
3. Calculate optimal domain distribution for activities
4. Determine appropriate challenge levels based on trust phase
5. Create boundary conditions for activity selection
6. Align activity strategy with user's goals and growth trajectory

Strategy development guidelines:
- Integrate resource, engagement, and psychological inputs
- Apply trust phase modifiers to base challenge levels
- Balance user preferences with growth needs in domain distribution
- Create domain-specific challenge adjustments
- Establish progression pathways for growth
- Implement priority framework for resolving conflicts

Never:
- Select specific activities (defer to Wheel/Activity Agent)
- Make direct psychological assessments (rely on Psychological Agent)
- Evaluate specific resource feasibility (rely on Resource Agent)
- Implement ethical judgments (defer to Ethical Oversight Agent)
- Optimize for engagement at the expense of well-being or growth""",
                'input_schema': {
                    'type': 'object',
                    'properties': {
                        'user_profile_id': {'type': 'string'},
                        'resource_context': {'type': 'object'},
                        'engagement_patterns': {'type': 'object'},
                        'psychological_assessment': {'type': 'object'},
                        'strategy_type': {'type': 'string'}
                    },
                    'required': ['user_profile_id', 'resource_context', 'engagement_patterns', 'psychological_assessment', 'strategy_type']
                },
                'output_schema': {
                    'type': 'object',
                    'properties': {
                        'challenge_calibration': {'type': 'object'},
                        'domain_distribution': {'type': 'object'},
                        'activity_selection_criteria': {'type': 'object'},
                        'constraint_boundaries': {'type': 'object'},
                        'growth_alignment': {'type': 'object'},
                        'strategic_rationale': {'type': 'object'}
                    },
                    'required': ['challenge_calibration', 'domain_distribution', 'activity_selection_criteria', 'constraint_boundaries']
                },
                'state_schema': {
                    'type': 'object',
                    'properties': {
                        'strategy_development_stage': {'type': 'string'},
                        'integration_data': {'type': 'object'},
                        'gap_analysis_results': {'type': 'object'}
                    }
                },
                'memory_schema': {
                    'type': 'object',
                    'properties': {
                        'baseline_strategy_parameters': {'type': 'object'},
                        'trust_adaptation_patterns': {'type': 'object'},
                        'challenge_calibration_rationales': {'type': 'object'}
                    }
                },
                'langgraph_node_class': 'apps.agents.nodes.StrategyAgentNode',
                'processing_timeout': 60,
                'read_models': [
                    'user.UserGoal',
                    'user.UserTraitInclination',
                    'user.TrustLevel',
                    'user.Preference',
                    'user.Belief'
                ],
                'write_models': [],
                'recommend_models': []
            }
        )
        
        self.stdout.write(f'{"Created" if created else "Updated"} Strategy Agent')
        return agent
        
    def create_wheel_activity_agent(self):
        """Create the Wheel/Activity Agent"""
        self.stdout.write('Creating Wheel/Activity Agent...')
        
        agent, created = GenericAgent.objects.get_or_create(
            role=AgentRole.ACTIVITY,
            defaults={
                'version': '1.0.0',
                'is_active': True,
                'description': "Selects concrete activities based on strategy framework, tailors generic activities to user's specific context, constructs the wheel with appropriate probability distributions, customizes activity instructions and resource requirements, and provides clear value propositions for selected activities.",
                'system_instructions': """You are the Wheel/Activity Agent, responsible for selecting and customizing activities based on the strategy framework and constructing the wheel with appropriate probability distributions.

Core responsibilities:
1. Select appropriate activities from the GenericActivity catalog
2. Tailor activities to user's specific context
3. Adjust challenge levels to match calibration parameters
4. Customize instructions and resource requirements
5. Construct wheel with appropriate probability distributions
6. Provide clear value propositions for selected activities

Activity selection guidelines:
- Query GenericActivity catalog with strategy parameters
- Apply domain distribution requirements
- Filter by resource availability and constraints
- Match challenge levels to calibration parameters
- Select appropriate variety and balance
- Create personalized value propositions

Never:
- Override strategy framework parameters
- Select activities that violate resource constraints
- Include activities with inappropriate challenge levels
- Bypass ethical validation
- Generate generic activities without personalization
- Create wheel with insufficient variety or domain balance""",
                'input_schema': {
                    'type': 'object',
                    'properties': {
                        'user_profile_id': {'type': 'string'},
                        'strategy_framework': {'type': 'object'},
                        'resource_context': {'type': 'object'},
                        'wheel_request_type': {'type': 'string'}
                    },
                    'required': ['user_profile_id', 'strategy_framework', 'wheel_request_type']
                },
                'output_schema': {
                    'type': 'object',
                    'properties': {
                        'wheel': {'type': 'object'},
                        'wheel_items': {'type': 'array'},
                        'tailored_activities': {'type': 'array'},
                        'value_propositions': {'type': 'object'},
                        'selection_rationales': {'type': 'object'}
                    },
                    'required': ['wheel', 'wheel_items', 'tailored_activities']
                },
                'state_schema': {
                    'type': 'object',
                    'properties': {
                        'activity_selection_stage': {'type': 'string'},
                        'selected_activities': {'type': 'array'},
                        'wheel_construction_data': {'type': 'object'}
                    }
                },
                'memory_schema': {
                    'type': 'object'  # Stateless for MVP
                },
                'langgraph_node_class': 'apps.agents.nodes.ActivityAgentNode',
                'processing_timeout': 90,
                'read_models': [
                    'activity.GenericActivity',
                    'user.UserProfile',
                    'user.UserEnvironment',
                    'user.Inventory',
                    'user.UserTraitInclination',
                    'user.UserGoal',
                    'user.UserResource'
                ],
                'write_models': [
                    'main.Wheel',
                    'main.WheelItem',
                    'activity.ActivityTailored'
                ],
                'recommend_models': []
            }
        )
        
        self.stdout.write(f'{"Created" if created else "Updated"} Wheel/Activity Agent')
        return agent
        
    def create_ethical_agent(self):
        """Create the Ethical Oversight Agent"""
        self.stdout.write('Creating Ethical Oversight Agent...')
        
        agent, created = GenericAgent.objects.get_or_create(
            role=AgentRole.ETHICAL,
            defaults={
                'version': '1.0.0',
                'is_active': True,
                'description': "Reviews all proposed activities for ethical alignment, ensures respect for user autonomy and well-being, validates appropriate challenge calibration, verifies transparency in activity descriptions, and implements the ethical framework of benevolence, fairness, and transparency.",
                'system_instructions': """You are the Ethical Oversight Agent, responsible for ensuring all activities and recommendations align with the system's ethical principles of benevolence, fairness, transparency, and respect for user autonomy.

Core responsibilities:
1. Review activities for ethical alignment
2. Ensure psychological safety based on trust phase
3. Validate appropriate challenge calibration
4. Verify transparency in activity descriptions
5. Assess overall wheel balance and composition
6. Apply strict safety boundaries for vulnerable areas

Ethical review guidelines:
- Apply core ethical principles:
  - Benevolence: Activities must support user well-being
  - Fairness: Challenge calibration must respect current capabilities
  - Transparency: Instructions and expectations must be clear
  - Autonomy: User's boundaries must be respected
- Verify psychological safety based on trust phase
- Validate resource requirement appropriateness
- Review for potential manipulation or bias
- Document validation rationales with explicit principles

Never:
- Implement domain-specific logic outside ethical scope
- Replace rejected activities (defer to Activity Agent)
- Make subjective judgments about activity value
- Prioritize engagement or growth over well-being
- Apply overly rigid interpretations of ethical guidelines""",
                'input_schema': {
                    'type': 'object',
                    'properties': {
                        'user_profile_id': {'type': 'string'},
                        'wheel': {'type': ['object', 'null']},
                        'activities': {'type': 'array'},
                        'psychological_assessment': {'type': 'object'},
                        'validation_type': {'type': 'string'}
                    },
                    'required': ['user_profile_id', 'activities', 'validation_type']
                },
                'output_schema': {
                    'type': 'object',
                    'properties': {
                        'activity_validations': {'type': 'array'},
                        'wheel_validation': {'type': ['object', 'null']},
                        'safety_considerations': {'type': 'object'},
                        'modification_recommendations': {'type': 'array'},
                        'ethical_rationales': {'type': 'object'}
                    },
                    'required': ['activity_validations', 'safety_considerations']
                },
                'state_schema': {
                    'type': 'object',
                    'properties': {
                        'validation_stage': {'type': 'string'},
                        'activity_assessments': {'type': 'object'},
                        'safety_concerns': {'type': 'array'}
                    }
                },
                'memory_schema': {
                    'type': 'object'  # Stateless for MVP
                },
                'langgraph_node_class': 'apps.agents.nodes.EthicalAgentNode',
                'processing_timeout': 60,
                'read_models': [
                    'main.Wheel',
                    'main.WheelItem',
                    'activity.ActivityTailored',
                    'user.UserTraitInclination',
                    'user.Belief',
                    'user.TrustLevel',
                    'user.UserLimitation'
                ],
                'write_models': [],
                'recommend_models': []
            }
        )
        
        self.stdout.write(f'{"Created" if created else "Updated"} Ethical Oversight Agent')
        return agent
    
    def create_dispatcher_agent(self):
        """Create the Dispatcher Agent"""
        self.stdout.write('Creating Dispatcher Agent...')

        agent_role = AgentRole.DISPATCHER

        
        agent, created = GenericAgent.objects.get_or_create(
            role=agent_role,
            defaults={
                'version': '1.0.0',
                'is_active': True,
                'description': "Analyzes user input to determine appropriate workflows, extracts contextual information, and handles routing decisions at the entry point of conversations.",
                'system_instructions': """You are the Dispatcher Agent, responsible for analyzing user input to determine the appropriate workflow and extract contextual information for downstream processing.

Core responsibilities:
1. Extract contextual information from user messages (mood, environment, time availability, etc.)
2. Classify message intent and determine appropriate workflow routing
3. Detect when additional information is needed before proceeding
4. Format context packets for downstream agents
5. Provide accurate workflow classification with confidence metrics

Analysis guidelines:
- Extract context elements like mood, environment, and time availability from natural language
- Classify messages into appropriate workflow types:
  - wheel_generation: User wants activity suggestions
  - activity_feedback: User is providing feedback on a completed activity
  - pre_spin_feedback: User is providing context before spinning the wheel
  - post_spin: User has selected an activity from the wheel
  - progress_review: User wants information about their progress
  - user_onboarding: User is in the onboarding process
- Identify when additional information is needed to proceed with workflow
- Supplement extracted context with explicit metadata when available
- Apply classification rules to improve accuracy and confidence

Never:
- Engage in lengthy dialogue with the user (defer to Mentor Agent)
- Make activity recommendations (defer to specialized agents)
- Implement complex psychological assessments (defer to Psychological Agent)
- Assume missing information when confidence is low
- Bypass proper workflow routing for efficiency""",
                'input_schema': {
                    'type': 'object',
                    'properties': {
                        'text': {'type': 'string'},
                        'metadata': {'type': 'object'},
                        'timestamp': {'type': 'string'},
                        'user_profile_id': {'type': 'string'}
                    },
                    'required': ['text', 'user_profile_id']
                },
                'output_schema': {
                    'type': 'object',
                    'properties': {
                        'workflow_type': {'type': 'string'},
                        'confidence': {'type': 'number'},
                        'context_packet': {'type': 'object'},
                        'estimated_completion_time': {'type': 'number'},
                        'action_required': {'type': ['object', 'null']}
                    },
                    'required': ['workflow_type', 'confidence', 'context_packet']
                },
                'state_schema': {
                    'type': 'object',
                    'properties': {
                        'context_packet': {'type': 'object'},
                        'workflow_classification': {'type': 'object'}
                    }
                },
                'memory_schema': {
                    'type': 'object',
                    'properties': {
                        'context_extraction_patterns': {'type': 'object'},
                        'last_workflow_selection': {'type': 'object'}
                    }
                },
                'langgraph_node_class': 'apps.main.agents.nodes.dispatcher_agent_node.DispatcherAgentNode',
                'processing_timeout': 20,
                'read_models': [
                    'user.UserProfile',
                    'user.CurrentMood',
                    'user.UserEnvironment',
                    'main.HistoryEvent',
                    'main.UserFeedback'
                ],
                'write_models': [
                    'main.HistoryEvent'
                ],
                'recommend_models': []
            }
        )
        
        self.stdout.write(f'{"Created" if created else "Updated"} Dispatcher Agent')
        return agent

    def create_error_handler_agent(self):
        """Create the Error Handler Agent"""
        self.stdout.write('Creating Error Handler Agent...')

        agent_role = AgentRole.ERROR_HANDLER

        agent, created = GenericAgent.objects.get_or_create(
            role=agent_role,
            defaults={
                'version': '1.0.0',
                'is_active': True,
                'description': "Handles errors and exceptions that occur during workflow execution, providing recovery strategies and user-friendly error communication.",
                'system_instructions': """You are the Error Handler Agent, responsible for managing errors and exceptions that occur during workflow execution.

Core responsibilities:
1. Analyze errors to determine if they are recoverable or persistent
2. Implement recovery strategies for transient errors
3. Provide user-friendly error communication without technical details
4. Track error patterns to improve system reliability
5. Coordinate with other agents for error recovery

Error handling guidelines:
- Classify errors as recoverable (temporary issues) or persistent (fundamental problems)
- For recoverable errors, implement appropriate retry strategies
- For persistent errors, gracefully terminate with helpful user communication
- Never expose technical error details to users
- Log detailed error information for system debugging
- Maintain workflow state consistency during error recovery

Recovery strategies:
- Retry with exponential backoff for transient failures
- Route to alternative agents when primary agents fail
- Provide fallback responses when full recovery isn't possible
- Clear error state after successful recovery

Never:
- Expose raw error messages or stack traces to users
- Retry indefinitely without considering retry limits
- Ignore errors or fail silently
- Compromise user data integrity during recovery""",
                'input_schema': {
                    'type': 'object',
                    'properties': {
                        'error': {'type': 'string'},
                        'last_agent': {'type': 'string'},
                        'current_stage': {'type': 'string'},
                        'workflow_state': {'type': 'object'},
                        'retry_count': {'type': 'number'}
                    },
                    'required': ['error', 'last_agent', 'current_stage']
                },
                'output_schema': {
                    'type': 'object',
                    'properties': {
                        'error_handled': {'type': 'boolean'},
                        'recovery_plan': {'type': 'object'},
                        'next_agent': {'type': ['string', 'null']},
                        'user_message': {'type': 'string'},
                        'should_terminate': {'type': 'boolean'}
                    },
                    'required': ['error_handled', 'user_message']
                },
                'state_schema': {
                    'type': 'object',
                    'properties': {
                        'error': {'type': ['string', 'null']},
                        'retry_count': {'type': 'number'},
                        'recovery_attempts': {'type': 'array'}
                    }
                },
                'memory_schema': {
                    'type': 'object',
                    'properties': {
                        'error_patterns': {'type': 'object'},
                        'recovery_success_rates': {'type': 'object'}
                    }
                },
                'langgraph_node_class': 'apps.main.agents.error_handler.ErrorHandlerAgent',
                'processing_timeout': 30,
                'read_models': [
                    'main.HistoryEvent',
                    'main.AgentRun',
                    'user.UserProfile'
                ],
                'write_models': [
                    'main.HistoryEvent',
                    'main.AgentRun'
                ],
                'recommend_models': []
            }
        )

        self.stdout.write(f'{"Created" if created else "Updated"} Error Handler Agent')
        return agent
