"""
Tests for the workflow benchmarking system.

This module contains tests for the AsyncWorkflowManager and WheelWorkflowBenchmarkManager classes.
"""

import uuid
import pytest
from unittest.mock import AsyncMock, MagicMock, patch

from django.utils import timezone
from asgiref.sync import sync_to_async

from apps.main.models import BenchmarkScenario, BenchmarkRun
from apps.main.services.async_workflow_manager import TokenTracker, StageTimer, BenchmarkResult
from apps.main.services.wheel_workflow_benchmark_manager import WheelWorkflowBenchmarkManager
from apps.main.testing.mock_tool_registry import MockToolRegistry

@pytest.mark.asyncio
@pytest.mark.django_db(transaction=True)
async def test_token_tracker():
    """Test the TokenTracker class."""
    # Create a benchmark run for testing
    scenario = await create_test_scenario_async()
    benchmark_run = await create_test_benchmark_run_async(scenario)

    # Create a token tracker
    tracker = TokenTracker(run_id=benchmark_run.id)

    # Record token usage
    await tracker.record_usage(input_tokens=10, output_tokens=20)

    # Verify token counts were updated
    assert tracker.input_tokens == 10
    assert tracker.output_tokens == 20

    # Refresh the benchmark run from the database
    updated_run = await get_benchmark_run_async(benchmark_run.id)
    assert updated_run.total_input_tokens == 10
    assert updated_run.total_output_tokens == 20
    assert updated_run.llm_calls == 1

    # Record more token usage
    await tracker.record_usage(input_tokens=15, output_tokens=25)

    # Verify token counts were updated
    assert tracker.input_tokens == 25
    assert tracker.output_tokens == 45

    # Calculate cost
    cost = await tracker.calculate_cost(input_price=0.001, output_price=0.002)
    # Due to floating point precision, we need to use approx
    assert cost == pytest.approx(0.115, abs=1e-10)

@pytest.mark.asyncio
async def test_stage_timer():
    """Test the StageTimer class."""
    # Create a stage timer
    timer = StageTimer()

    # Start and end a stage
    await timer.start_stage('test_stage')
    await timer.end_stage('test_stage')

    # Verify stage duration was recorded
    durations = timer.get_stage_durations()
    assert 'test_stage' in durations
    assert durations['test_stage'] > 0

    # Verify millisecond conversion
    durations_ms = timer.get_stage_durations_ms()
    assert 'test_stage' in durations_ms
    assert durations_ms['test_stage'] == durations['test_stage'] * 1000

    # Test multiple stages
    await timer.start_stage('stage1')
    await timer.start_stage('stage2')
    await timer.end_stage('stage2')
    await timer.end_stage('stage1')

    # Verify all stage durations were recorded
    durations = timer.get_stage_durations()
    assert 'stage1' in durations
    assert 'stage2' in durations
    assert durations['stage1'] > 0
    assert durations['stage2'] > 0

    # Verify stage that wasn't ended doesn't have duration
    await timer.start_stage('incomplete_stage')
    durations = timer.get_stage_durations()
    assert 'incomplete_stage' not in durations

@pytest.mark.asyncio
@pytest.mark.django_db(transaction=True)
async def test_wheel_workflow_benchmark_manager():
    """Test the WheelWorkflowBenchmarkManager class."""
    # Create a test scenario
    scenario = await create_test_scenario_async()

    # Create a test agent definition
    from apps.main.models import GenericAgent
    from apps.main.models import AgentRole as ModelAgentRole
    from apps.main.tests.utils import ensure_mentor_agent_exists

    # Get or create the mentor agent definition
    agent_def, created = await sync_to_async(
        ensure_mentor_agent_exists,
        thread_sensitive=True
    )()

    # Log whether we created a new agent or used an existing one
    if created:
        print("Created new mentor agent for testing")
    else:
        print("Using existing mentor agent for testing")

    # Create a mock for run_wheel_generation_workflow
    with patch('apps.main.services.wheel_workflow_benchmark_manager.run_wheel_generation_workflow', new_callable=AsyncMock) as mock_run_workflow:
        # Configure the mock to return a sample output
        mock_run_workflow.return_value = {
            'workflow_id': str(uuid.uuid4()),
            'status': 'completed',
            'stage_timings': {
                'initial_conversation': 0.5,
                'profile_analysis': 0.8,
                'wheel_generation': 1.2
            },
            'token_usage': {
                'input_tokens': 100,
                'output_tokens': 200
            },
            'output': {
                'wheel': {
                    'id': 'test-wheel-123',
                    'activities': [
                        {'name': 'Activity 1', 'category': 'productivity'},
                        {'name': 'Activity 2', 'category': 'wellness'}
                    ]
                }
            }
        }

        # Create a mock tool registry
        mock_tools = MockToolRegistry()
        # Set up call_counts as a property that returns the expected counts
        mock_tools.call_counts = {
            'get_user_profile': 1,
            'get_user_activities': 1,
            'store_wheel': 1
        }
        mock_tools.get_call_counts = MagicMock(return_value={
            'get_user_profile': 1,
            'get_user_activities': 1,
            'store_wheel': 1
        })
        mock_tools.activate = MagicMock()
        mock_tools.deactivate = MagicMock()

        # Create a benchmark manager
        manager = WheelWorkflowBenchmarkManager()

        # Run the benchmark
        result = await manager._run_workflow_benchmark(
            scenario=scenario,
            workflow_type='wheel_generation',
            mock_tools=mock_tools,
            runs=2,
            warmup_runs=1
        )

        # Verify the result
        assert isinstance(result, BenchmarkResult)
        assert result.workflow_type == 'wheel_generation'
        assert result.scenario_name == scenario.name
        assert result.mean_duration > 0
        assert result.success_rate == 1.0  # All runs successful
        assert result.tool_call_counts == {'get_user_profile': 2, 'get_user_activities': 2, 'store_wheel': 2}
        assert result.total_input_tokens == 200  # 100 per run * 2 runs
        assert result.total_output_tokens == 400  # 200 per run * 2 runs

        # Note: The workflow benchmark manager uses _run_mock_wheel_generation_workflow
        # instead of the actual run_wheel_generation_workflow, so the mock isn't called.
        # This is by design to avoid LLM client dependencies in tests.
        # The important thing is that the benchmark runs successfully and produces the expected results.

        # Test execute_benchmark
        with patch.object(manager, '_run_workflow_benchmark', return_value=result) as mock_run_benchmark:
            # Mock the _get_agent_definition method to return our test agent
            with patch.object(manager, '_create_benchmark_run_sync') as mock_create_run:
                # Configure the mock to return a benchmark run
                mock_create_run.return_value = await create_test_benchmark_run_async(scenario)

                # Run execute_benchmark
                benchmark_run = await manager.execute_benchmark(scenario_id=scenario.id)

                # Verify the benchmark run was created
                assert benchmark_run is not None
                assert benchmark_run.scenario_id == scenario.id

                # Verify mock was called correctly
                mock_run_benchmark.assert_awaited_once()

# Helper functions for creating test data

async def create_test_scenario_async():
    """Create a test benchmark scenario."""
    from django.db.models import Q
    from asgiref.sync import sync_to_async

    # Generate a unique name to avoid conflicts
    unique_name = f"test_wheel_workflow_{uuid.uuid4().hex[:8]}"

    # Check if scenario with this name already exists
    exists = await sync_to_async(
        lambda: BenchmarkScenario.objects.filter(Q(name=unique_name)).exists(),
        thread_sensitive=True
    )()

    if exists:
        # If it exists, return the existing scenario
        return await sync_to_async(
            lambda: BenchmarkScenario.objects.get(name=unique_name),
            thread_sensitive=True
        )()

    # Create a new scenario
    return await sync_to_async(
        BenchmarkScenario.objects.create,
        thread_sensitive=True
    )(
        name=unique_name,
        description="Test wheel workflow benchmark scenario",
        agent_role="workflow",
        input_data={
            "user_profile_id": "test-user-123",
            "context_packet": {
                "task_type": "wheel_generation",
                "user_message": "I want to improve my productivity"
            }
        },
        metadata={
            "workflow_type": "wheel_generation",
            "expected_quality_criteria": {
                "Completeness": ["Does the wheel contain a balanced set of activities?"]
            },
            "mock_tool_responses": {
                "get_user_profile": "{'id': 'test-user-123', 'name': 'Test User'}"
            }
        },
        is_active=True
    )

async def create_test_benchmark_run_async(scenario):
    """Create a test benchmark run."""
    from asgiref.sync import sync_to_async
    from apps.main.models import GenericAgent
    from apps.main.models import AgentRole as ModelAgentRole
    from apps.main.tests.utils import ensure_mentor_agent_exists

    # Get or create the mentor agent definition
    agent_def, created = await sync_to_async(
        ensure_mentor_agent_exists,
        thread_sensitive=True
    )()

    # Log whether we created a new agent or used an existing one
    if created:
        print("Created new mentor agent for testing")
    else:
        print("Using existing mentor agent for testing")

    return await sync_to_async(
        BenchmarkRun.objects.create,
        thread_sensitive=True
    )(
        scenario=scenario,
        agent_definition=agent_def,
        agent_version="1.0",
        parameters={},
        runs_count=1,
        mean_duration=100.0,
        median_duration=100.0,
        min_duration=100.0,
        max_duration=100.0,
        std_dev=0.0,
        success_rate=100.0,
        llm_calls=0,
        tool_calls=0,
        tool_breakdown={},
        tool_call_details={},  # Add missing field for test
        total_input_tokens=0,
        total_output_tokens=0,
        raw_results={},
        agent_communications={}  # Add missing field for test
    )

async def get_benchmark_run_async(run_id):
    """Get a benchmark run by ID."""
    from asgiref.sync import sync_to_async

    return await sync_to_async(
        BenchmarkRun.objects.get,
        thread_sensitive=True
    )(id=run_id)
