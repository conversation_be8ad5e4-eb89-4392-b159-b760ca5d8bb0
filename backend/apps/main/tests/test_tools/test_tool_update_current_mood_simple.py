import pytest
from django.contrib.auth import get_user_model

# Import the tool function we are testing
from apps.main.agents.tools.update_current_mood_tool import update_current_mood

# Import models needed for setup and assertions
from apps.main.models import HistoryEvent
from apps.user.models import UserProfile, CurrentMood

# Import factories
from tests.factories import UserProfileFactory, CurrentMoodFactory, UserFactory

# Get the User model
User = get_user_model()

# --- Test Data ---
# Use a placeholder ID for input data structure, will use real IDs in tests
TEST_USER_PROFILE_ID = 999
INPUT_DATA_CREATE = {
    'user_profile_id': TEST_USER_PROFILE_ID,
    'description': 'Feeling creative',
    'height': 75,
    'user_awareness': 80,
    'inferred_from_text': False
}
INPUT_DATA_UPDATE = {
    'user_profile_id': TEST_USER_PROFILE_ID,
    'description': 'Feeling focused now',
    'height': 60,
    'user_awareness': 70,
    'inferred_from_text': True
}

# Mark all tests in this module to use the database and run asynchronously
pytestmark = [pytest.mark.django_db, pytest.mark.asyncio]

@pytest.mark.test_type("unit")
@pytest.mark.component("main.agents.tools.update_current_mood")
@pytest.mark.tool("update_current_mood")
async def test_mood_creation_simple():
    """
    Simplified test for mood creation
    """

    # Arrange: Create UserProfile using factory
    user_profile = UserProfileFactory.create()
    
    # Clean up any existing moods for this user profile
    await CurrentMood.objects.filter(user_profile_id=user_profile.id).adelete()
    
    input_data = {**INPUT_DATA_CREATE, 'user_profile_id': user_profile.id}
    
    # Act: Call the tool function
    result = await update_current_mood(input_data)
    
    # Assert: Basic checks
    assert 'mood' in result
    assert result['mood']['description'] == input_data['description']
    assert int(result['mood']['height']) == input_data['height']
    
    # Check database state
    created_mood = await CurrentMood.objects.aget(user_profile_id=user_profile.id)
    assert str(created_mood.id) == str(result['mood']['id'])

@pytest.mark.test_type("unit")
@pytest.mark.component("main.agents.tools.update_current_mood")
@pytest.mark.tool("update_current_mood")
async def test_mood_update_simple():
    """
    Simplified test for mood update
    """

    # Arrange: Create UserProfile and an existing CurrentMood
    user_profile = UserProfileFactory.create()

    # Clean up any existing moods for this user profile
    await CurrentMood.objects.filter(user_profile_id=user_profile.id).adelete()

    # Create a new mood
    existing_mood = CurrentMoodFactory.create(
        user_profile=user_profile,
        description="Old description",
        height=50,
        user_awareness=55,
        inferred_from_text=False
    )

    input_data = {**INPUT_DATA_UPDATE, 'user_profile_id': user_profile.id}

    # Act: Call the tool function
    result = await update_current_mood(input_data)

    # Assert: Basic checks
    assert 'mood' in result
    assert result['mood']['description'] == input_data['description']
    assert int(result['mood']['height']) == input_data['height']

    # Check database state
    updated_mood = await CurrentMood.objects.aget(user_profile_id=user_profile.id)
    assert str(updated_mood.id) == str(result['mood']['id'])
    assert updated_mood.description == input_data['description']


@pytest.mark.test_type("unit")
@pytest.mark.component("main.agents.tools.update_current_mood")
@pytest.mark.tool("update_current_mood")
async def test_debug_model_relationships():
    """
    Debug test to isolate the relationship issue
    """
    # Create a UserProfile using factory
    user_profile = UserProfileFactory.create()

    # Log the types and values
    print(f"UserProfile: {user_profile}, type: {type(user_profile)}")
    print(f"UserProfile.id: {user_profile.id}, type: {type(user_profile.id)}")
    print(f"UserProfile.user: {user_profile.user}, type: {type(user_profile.user)}")
    print(f"UserProfile.user.id: {user_profile.user.id}, type: {type(user_profile.user.id)}")

    # Try to create a CurrentMood directly
    try:
        import datetime
        # Use a fixed datetime instead of timezone.now() to avoid F() expression issues
        fixed_time = datetime.datetime(2024, 1, 1, 12, 0, 0, tzinfo=datetime.timezone.utc)
        current_mood = await CurrentMood.objects.acreate(
            user_profile=user_profile,
            description="Test mood",
            height=50,
            user_awareness=50,
            duration_estimate="1 day",
            effective_start=fixed_time  # Use fixed time instead of timezone.now()
        )
        print(f"Successfully created CurrentMood: {current_mood}")
    except Exception as e:
        print(f"Error creating CurrentMood: {e}")
        print(f"Exception type: {type(e)}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        raise
