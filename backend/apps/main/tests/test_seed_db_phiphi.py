"""
Tests for the seed_db_phiphi management command.

This test verifies that the PhiPhi fake user profile is correctly seeded
for benchmarking purposes.
"""
import pytest
from django.test import TestCase
from django.core.management import call_command
from django.contrib.auth.models import User
from io import StringIO

from apps.user.models import (
    UserProfile, Demographics, Aspiration, Intention,
    Inspiration, GoalInspiration, TrustLevel, Preference, Belief,
    BeliefEvidence, BeliefInfluence, CurrentMood, UserTraitInclination,
    GenericTrait, UserEnvironment, GenericEnvironment,
    UserEnvironmentPhysicalProperties, UserEnvironmentSocialContext,
    UserEnvironmentActivitySupport, UserEnvironmentPsychologicalQualities,
    Inventory, UserResource, GenericResource, Skill, GenericSkill,
    UserLimitation, GenericUserLimitation,
    GenericEnvironmentDomainRelationship
)
from apps.activity.models import GenericDomain


class SeedDbPhiPhiTestCase(TestCase):
    """Test case for the seed_db_phiphi management command."""

    def setUp(self):
        """Set up test environment."""
        # Ensure we start with a clean state
        User.objects.filter(username="phiphi").delete()
        
        # Create some basic generic data that the command might need
        self._create_basic_generic_data()

    def _create_basic_generic_data(self):
        """Create basic generic data needed for seeding."""
        # Create some generic domains if they don't exist
        domains = [
            ('phys_active', 'Physical Activity'),
            ('creative_making', 'Creative Making'),
            ('creative_music', 'Creative Music'),
            ('soc_connecting', 'Social Connecting'),
            ('intel_learning', 'Intellectual Learning'),
            ('refl_mindful', 'Reflective Mindfulness'),
            ('prod_skill', 'Productive Skills'),
        ]
        
        for code, name in domains:
            GenericDomain.objects.get_or_create(
                code=code,
                defaults={'name': name, 'description': f'{name} domain'}
            )

        # Create some basic HEXACO traits if they don't exist
        traits = [
            ('honesty_sincerity', 'Honesty-Humility: Sincerity'),
            ('honesty_fairness', 'Honesty-Humility: Fairness'),
            ('honesty_greed_avoidance', 'Honesty-Humility: Greed Avoidance'),
            ('honesty_modesty', 'Honesty-Humility: Modesty'),
            ('emotion_fearfulness', 'Emotionality: Fearfulness'),
            ('emotion_anxiety', 'Emotionality: Anxiety'),
            ('emotion_dependence', 'Emotionality: Dependence'),
            ('emotion_sentimentality', 'Emotionality: Sentimentality'),
            ('extra_self_esteem', 'Extraversion: Self-esteem'),
            ('extra_social_boldness', 'Extraversion: Social Boldness'),
            ('extra_sociability', 'Extraversion: Sociability'),
            ('extra_liveliness', 'Extraversion: Liveliness'),
            ('agree_forgiveness', 'Agreeableness: Forgiveness'),
            ('agree_gentleness', 'Agreeableness: Gentleness'),
            ('agree_flexibility', 'Agreeableness: Flexibility'),
            ('agree_patience', 'Agreeableness: Patience'),
            ('consc_organization', 'Conscientiousness: Organization'),
            ('consc_diligence', 'Conscientiousness: Diligence'),
            ('consc_perfectionism', 'Conscientiousness: Perfectionism'),
            ('consc_prudence', 'Conscientiousness: Prudence'),
            ('open_aesthetic', 'Openness: Aesthetic Appreciation'),
            ('open_inquisitive', 'Openness: Inquisitiveness'),
            ('open_creativity', 'Openness: Creativity'),
            ('open_unconventional', 'Openness: Unconventionality'),
        ]
        
        for code, name in traits:
            GenericTrait.objects.get_or_create(
                code=code,
                defaults={
                    'name': name,
                    'description': f'{name} trait',
                    'trait_type': 'OPEN'  # Default trait type
                }
            )

        # Create some basic limitations if they don't exist
        limitations = [
            ('cog_attention', 'Cognitive: Attention', 'COGNITIVE'),
            ('psych_motivation', 'Psychological: Motivation', 'PSYCHOLOGICAL'),
            ('social_communication', 'Social: Communication', 'SOCIAL'),
            ('psych_social_anxiety', 'Psychological: Social Anxiety', 'PSYCHOLOGICAL'),
            ('time_morning', 'Time: Morning Productivity', 'TEMPORAL'),
            ('phys_energy', 'Physical: Energy Levels', 'PHYSICAL'),
            ('fin_resources', 'Financial: Limited Resources', 'FINANCIAL'),
        ]

        for code, description, limitation_type in limitations:
            GenericUserLimitation.objects.get_or_create(
                code=code,
                defaults={
                    'description': description,
                    'limitation_type': limitation_type
                }
            )

        # Create some basic skills if they don't exist
        skills = [
            ('tech_ai_ml', 'AI/ML Development'),
            ('tech_programming', 'Programming'),
            ('creative_writing', 'Creative Writing'),
            ('creative_music', 'Music'),
            ('social_communication', 'Communication'),
            ('phys_yoga', 'Yoga'),
            ('lang_german', 'German Language'),
            ('lang_english', 'English Language'),
            ('lang_french', 'French Language'),
        ]

        for code, description in skills:
            GenericSkill.objects.get_or_create(
                code=code,
                defaults={'description': description}
            )

    def test_command_creates_phiphi_user(self):
        """Test that the command creates the PhiPhi user correctly."""
        # Ensure user doesn't exist initially
        self.assertFalse(User.objects.filter(username="phiphi").exists())
        
        # Run the command
        out = StringIO()
        call_command('seed_db_phiphi', stdout=out)
        
        # Check that user was created
        self.assertTrue(User.objects.filter(username="phiphi").exists())
        
        # Get the user and verify properties
        user = User.objects.get(username="phiphi")
        self.assertEqual(user.email, "<EMAIL>")
        self.assertEqual(user.first_name, "PhiPhi")
        self.assertEqual(user.last_name, "Schmidt")
        self.assertTrue(user.is_staff)
        self.assertTrue(user.is_superuser)
        
        # Verify output contains success messages
        output = out.getvalue()
        self.assertIn("PhiPhi's user account created", output)
        self.assertIn("successfully seeded", output)

    def test_command_creates_fake_user_profile(self):
        """Test that the UserProfile is created with is_real=False for benchmarking."""
        # Run the command
        call_command('seed_db_phiphi')
        
        # Check that user profile was created correctly
        self.assertTrue(UserProfile.objects.filter(profile_name="PhiPhi").exists())
        
        user_profile = UserProfile.objects.get(profile_name="PhiPhi")
        self.assertFalse(user_profile.is_real)  # This is the key test - should be False for benchmarking
        self.assertEqual(user_profile.profile_type, "Test Profile")

    def test_command_creates_demographics(self):
        """Test that demographics are created correctly."""
        call_command('seed_db_phiphi')
        
        user_profile = UserProfile.objects.get(profile_name="PhiPhi")
        self.assertTrue(hasattr(user_profile, 'demographics'))
        
        demographics = user_profile.demographics
        self.assertEqual(demographics.full_name, "PhiPhi Schmidt")
        self.assertEqual(demographics.age, 22)
        self.assertEqual(demographics.gender, "Male")
        self.assertIn("Lyon, France", demographics.location)
        self.assertIn("German", demographics.language)
        self.assertEqual(demographics.occupation, "Undefined / Traveler")
        
        # Check personal preferences JSON
        prefs = demographics.personal_prefs_json
        self.assertIn("traveling", prefs["likes"])
        self.assertIn("AI", prefs["likes"])
        self.assertIn("sailing", prefs["likes"])

    def test_command_creates_environment(self):
        """Test that the farm environment is created correctly."""
        call_command('seed_db_phiphi')
        
        user_profile = UserProfile.objects.get(profile_name="PhiPhi")
        self.assertIsNotNone(user_profile.current_environment)
        
        environment = user_profile.current_environment
        self.assertEqual(environment.environment_name, "Lyon Rural Farm")
        self.assertTrue(environment.is_current)
        self.assertIn("caravan", environment.environment_description)
        
        # Check that all environment properties were created
        self.assertTrue(hasattr(environment, 'physical_properties'))
        self.assertTrue(hasattr(environment, 'social_context'))
        self.assertTrue(hasattr(environment, 'activity_support'))
        self.assertTrue(hasattr(environment, 'psychological_qualities'))

    def test_command_creates_beliefs(self):
        """Test that beliefs are created correctly."""
        call_command('seed_db_phiphi')
        
        user_profile = UserProfile.objects.get(profile_name="PhiPhi")
        beliefs = Belief.objects.filter(user_profile=user_profile)
        
        # Should have multiple beliefs
        self.assertGreater(beliefs.count(), 10)
        
        # Check for specific key beliefs
        genius_belief = beliefs.filter(content__contains="genius spirit").first()
        self.assertIsNotNone(genius_belief)
        self.assertGreater(genius_belief.user_confidence, 0)
        self.assertGreater(genius_belief.system_confidence, 0)
        
        # Check that some beliefs have evidence
        beliefs_with_evidence = beliefs.filter(evidences__isnull=False).distinct()
        self.assertGreater(beliefs_with_evidence.count(), 0)

    def test_command_creates_goals_and_aspirations(self):
        """Test that goals and aspirations are created correctly."""
        call_command('seed_db_phiphi')
        
        user_profile = UserProfile.objects.get(profile_name="PhiPhi")
        
        # Check aspirations
        aspirations = Aspiration.objects.filter(user_profile=user_profile)
        self.assertGreater(aspirations.count(), 3)
        
        # Check for specific aspirations
        farm_aspiration = aspirations.filter(title__contains="farm in Italy").first()
        self.assertIsNotNone(farm_aspiration)
        
        game_aspiration = aspirations.filter(title__contains="Game of Life").first()
        self.assertIsNotNone(game_aspiration)
        
        # Check intentions
        intentions = Intention.objects.filter(user_profile=user_profile)
        self.assertGreater(intentions.count(), 5)
        
        # Check for specific intentions
        ai_intention = intentions.filter(title__contains="AI").first()
        self.assertIsNotNone(ai_intention)

    def test_command_creates_inspirations(self):
        """Test that inspirations are created and linked to goals."""
        call_command('seed_db_phiphi')
        
        user_profile = UserProfile.objects.get(profile_name="PhiPhi")
        inspirations = Inspiration.objects.filter(user_profile=user_profile)
        
        # Should have multiple inspirations
        self.assertGreater(inspirations.count(), 5)
        
        # Check for specific inspirations
        rick_rubin = inspirations.filter(source="Rick Rubin").first()
        self.assertIsNotNone(rick_rubin)
        
        grandfather = inspirations.filter(source__contains="Grandfather").first()
        self.assertIsNotNone(grandfather)
        
        # Check that inspirations are linked to goals
        goal_inspirations = GoalInspiration.objects.filter(inspiration__user_profile=user_profile)
        self.assertGreater(goal_inspirations.count(), 0)

    def test_command_creates_traits(self):
        """Test that personality traits are created correctly."""
        call_command('seed_db_phiphi')
        
        user_profile = UserProfile.objects.get(profile_name="PhiPhi")
        traits = UserTraitInclination.objects.filter(user_profile=user_profile)
        
        # Should have HEXACO traits (24 total)
        self.assertGreater(traits.count(), 20)
        
        # Check for specific traits
        creativity_trait = traits.filter(generic_trait__code="open_creativity").first()
        self.assertIsNotNone(creativity_trait)
        self.assertGreater(creativity_trait.strength, 70)  # Should be high for PhiPhi

    def test_command_creates_mood_and_trust(self):
        """Test that mood and trust level are created."""
        call_command('seed_db_phiphi')
        
        user_profile = UserProfile.objects.get(profile_name="PhiPhi")
        
        # Check mood
        mood = CurrentMood.objects.filter(user_profile=user_profile).first()
        self.assertIsNotNone(mood)
        self.assertIn("AI", mood.description)
        
        # Check trust level
        trust_level = TrustLevel.objects.filter(user_profile=user_profile).first()
        self.assertIsNotNone(trust_level)
        self.assertEqual(trust_level.value, 65)

    def test_command_idempotent(self):
        """Test that running the command multiple times doesn't create duplicates."""
        # Run command twice
        call_command('seed_db_phiphi')
        call_command('seed_db_phiphi')
        
        # Should still only have one user
        users = User.objects.filter(username="phiphi")
        self.assertEqual(users.count(), 1)
        
        # Should still only have one user profile
        profiles = UserProfile.objects.filter(profile_name="PhiPhi")
        self.assertEqual(profiles.count(), 1)

    def test_command_output_messages(self):
        """Test that the command provides appropriate output messages."""
        out = StringIO()
        call_command('seed_db_phiphi', stdout=out)
        
        output = out.getvalue()
        
        # Should contain key success messages
        self.assertIn("Checking for existing PhiPhi user", output)
        self.assertIn("PhiPhi's user account created", output)
        self.assertIn("PhiPhi's user profile created", output)
        self.assertIn("PhiPhi's demographics created", output)
        self.assertIn("PhiPhi's farm environment created", output)
        self.assertIn("PhiPhi's personality traits created", output)
        self.assertIn("PhiPhi's beliefs created", output)
        self.assertIn("PhiPhi's goals, intentions, and aspirations created", output)
        self.assertIn("successfully seeded", output)

    def test_command_skips_if_user_exists(self):
        """Test that the command skips seeding if user already exists."""
        # Run command once to create the user
        call_command('seed_db_phiphi')

        # Run command again and check that it skips
        out = StringIO()
        call_command('seed_db_phiphi', stdout=out)

        output = out.getvalue()
        self.assertIn("already exists. Skipping seeding", output)

    def test_user_profile_api_endpoint(self):
        """Test that the user profile API endpoint works correctly after seeding."""
        from django.test import Client
        from django.contrib.auth.models import User as AuthUser
        import uuid

        # Create admin user for API access with unique username
        admin_user = AuthUser.objects.create_user(
            username=f'admin_{uuid.uuid4().hex[:8]}',
            password='testpass',
            is_staff=True,
            is_superuser=True
        )

        # Seed the PhiPhi user
        call_command('seed_db_phiphi')

        # Test the API endpoint
        client = Client()
        client.force_login(admin_user)

        # Test fetching fake user profiles
        response = client.get('/admin/benchmarks/api/user-profiles/?is_real=false')
        self.assertEqual(response.status_code, 200)

        data = response.json()
        self.assertTrue(data.get('success', False))
        self.assertIn('profiles', data)
        self.assertGreater(len(data['profiles']), 0)

        # Find PhiPhi profile
        phiphi_profile = None
        for profile in data['profiles']:
            if profile['profile_name'] == 'PhiPhi':
                phiphi_profile = profile
                break

        self.assertIsNotNone(phiphi_profile, "PhiPhi profile should be in the API response")

        # Verify profile structure
        self.assertEqual(phiphi_profile['profile_name'], 'PhiPhi')
        self.assertIn('demographics', phiphi_profile)
        self.assertIn('traits', phiphi_profile)

        # Verify demographics
        demographics = phiphi_profile['demographics']
        self.assertEqual(demographics['full_name'], 'PhiPhi Schmidt')
        self.assertEqual(demographics['age'], 22)

        # Verify traits structure (should have strength and awareness)
        traits = phiphi_profile['traits']
        self.assertGreater(len(traits), 0)

        # Check a specific trait
        for trait_name, trait_data in traits.items():
            self.assertIn('strength', trait_data)
            self.assertIn('awareness', trait_data)
            self.assertIsInstance(trait_data['strength'], (int, float))
            self.assertIsInstance(trait_data['awareness'], (int, float))

    def test_user_profile_api_error_handling(self):
        """Test that the user profile API handles errors gracefully."""
        from django.test import Client
        from django.contrib.auth.models import User as AuthUser
        import uuid

        # Create admin user for API access with unique username
        admin_user = AuthUser.objects.create_user(
            username=f'admin_{uuid.uuid4().hex[:8]}',
            password='testpass',
            is_staff=True,
            is_superuser=True
        )

        client = Client()
        client.force_login(admin_user)

        # Test with valid parameters (should work even with no profiles)
        response = client.get('/admin/benchmarks/api/user-profiles/?is_real=false')
        self.assertEqual(response.status_code, 200)

        data = response.json()
        self.assertTrue(data.get('success', False))
        self.assertIn('profiles', data)
        self.assertEqual(len(data['profiles']), 0)  # No fake profiles yet

    def test_user_profile_api_permissions(self):
        """Test that the user profile API requires proper permissions."""
        from django.test import Client
        from django.contrib.auth.models import User as AuthUser
        import uuid

        # Create regular user (not staff) with unique username
        regular_user = AuthUser.objects.create_user(
            username=f'regular_{uuid.uuid4().hex[:8]}',
            password='testpass',
            is_staff=False
        )

        client = Client()
        client.force_login(regular_user)

        # Should be forbidden for non-staff users
        response = client.get('/admin/benchmarks/api/user-profiles/?is_real=false')
        self.assertEqual(response.status_code, 403)

    def test_user_profile_api_filtering(self):
        """Test that the user profile API filtering works correctly."""
        from django.test import Client
        from django.contrib.auth.models import User as AuthUser
        import uuid

        # Create admin user for API access with unique username
        admin_user = AuthUser.objects.create_user(
            username=f'admin_{uuid.uuid4().hex[:8]}',
            password='testpass',
            is_staff=True,
            is_superuser=True
        )

        # Seed the PhiPhi user (fake profile)
        call_command('seed_db_phiphi')

        # Create a real user profile with unique username
        real_user = User.objects.create_user(
            username=f"realuser_{uuid.uuid4().hex[:8]}",
            email="<EMAIL>"
        )
        UserProfile.objects.create(
            user=real_user,
            profile_name="RealUser",
            is_real=True
        )

        client = Client()
        client.force_login(admin_user)

        # Test filtering for fake profiles only
        response = client.get('/admin/benchmarks/api/user-profiles/?is_real=false')
        self.assertEqual(response.status_code, 200)

        data = response.json()
        fake_profiles = data['profiles']

        # Should only contain fake profiles
        for profile in fake_profiles:
            # PhiPhi should be the only fake profile
            self.assertEqual(profile['profile_name'], 'PhiPhi')

        # Test filtering for real profiles only
        response = client.get('/admin/benchmarks/api/user-profiles/?is_real=true')
        self.assertEqual(response.status_code, 200)

        data = response.json()
        real_profiles = data['profiles']

        # Should only contain real profiles
        for profile in real_profiles:
            self.assertEqual(profile['profile_name'], 'RealUser')

        # Test no filtering (should get both)
        response = client.get('/admin/benchmarks/api/user-profiles/')
        self.assertEqual(response.status_code, 200)

        data = response.json()
        all_profiles = data['profiles']
        self.assertEqual(len(all_profiles), 2)  # Both fake and real profiles

    def test_user_profile_api_traits_structure(self):
        """Test that the API correctly handles trait_inclinations (not 'traits' field)."""
        from django.test import Client
        from django.contrib.auth.models import User as AuthUser
        import uuid

        # Create admin user for API access with unique username
        admin_user = AuthUser.objects.create_user(
            username=f'admin_{uuid.uuid4().hex[:8]}',
            password='testpass',
            is_staff=True,
            is_superuser=True
        )

        # Seed the PhiPhi user (which has trait_inclinations)
        call_command('seed_db_phiphi')

        client = Client()
        client.force_login(admin_user)

        # This would have failed with the original broken code that used .prefetch_related('traits')
        # instead of .prefetch_related('trait_inclinations__generic_trait')
        response = client.get('/admin/benchmarks/api/user-profiles/?is_real=false')
        self.assertEqual(response.status_code, 200)

        data = response.json()
        self.assertTrue(data.get('success', False))

        # Verify PhiPhi profile has traits data
        phiphi_profile = None
        for profile in data['profiles']:
            if profile['profile_name'] == 'PhiPhi':
                phiphi_profile = profile
                break

        self.assertIsNotNone(phiphi_profile)
        self.assertIn('traits', phiphi_profile)

        # Verify traits have the correct structure (strength and awareness)
        traits = phiphi_profile['traits']
        self.assertGreater(len(traits), 0)

        # Check specific HEXACO traits that should be present (now with category prefixes)
        expected_traits = [
            'Honesty-Humility: Sincerity',
            'Openness: Creativity',
            'Extraversion: Social Boldness'
        ]

        for expected_trait in expected_traits:
            self.assertIn(expected_trait, traits, f"Expected trait '{expected_trait}' not found in API response")
            trait_data = traits[expected_trait]
            self.assertIn('strength', trait_data)
            self.assertIn('awareness', trait_data)
            self.assertIsInstance(trait_data['strength'], (int, float))
            self.assertIsInstance(trait_data['awareness'], (int, float))
