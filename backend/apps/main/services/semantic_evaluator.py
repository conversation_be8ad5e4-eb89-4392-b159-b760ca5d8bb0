# ACTIVE_FILE - 29-05-2025
"""
Semantic Evaluator Service

This service provides functionality for evaluating the semantic quality of agent responses
using multiple LLM models and dimension-based scoring.

Key responsibilities:
1. Evaluate agent responses against predefined criteria
2. Support multiple evaluator models
3. Provide dimension-based scoring
4. Normalize scores across different models
"""

import json
import logging
import re
from typing import Dict, Any, List, Optional, Union

from django.conf import settings

from apps.main.services.event_service import EventService
from apps.main.llm.service import RealLLMClient
from apps.main.llm.response import LLMResponse, ResponseType

# Configure logging
logger = logging.getLogger(__name__)

# Default evaluator model if none specified
DEFAULT_EVALUATOR_LLM_MODEL = getattr(settings, 'DEFAULT_EVALUATOR_LLM_MODEL', 'mistral/mistral-small')

class SemanticEvaluator:
    """
    Evaluates the semantic quality of agent responses using multiple LLM models.

    This class provides methods for evaluating agent responses against predefined criteria
    using one or more LLM models. It supports dimension-based scoring and score normalization.
    """

    def __init__(self):
        """Initialize the semantic evaluator."""
        self.llm_services = {}  # Cache of LLM clients by model name

    async def evaluate_response(
        self,
        scenario_context: str,
        agent_response: str,
        criteria: Dict[str, Any],
        evaluator_models: List[str] = None,
        user_profile_id: Optional[str] = None,
        trust_level: Optional[int] = None,
        evaluation_context: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Evaluate an agent response using multiple evaluator models.

        Args:
            scenario_context: Context of the scenario being evaluated
            agent_response: Agent's response to evaluate
            criteria: Dictionary containing criteria (either flat or phase-aware)
            evaluator_models: List of LLM model identifiers to use for evaluation
            user_profile_id: Optional ID of the user initiating the evaluation
            trust_level: Optional trust level (0-100) for phase-aware criteria

        Returns:
            Dict containing evaluation results from each model
        """
        if not evaluator_models:
            evaluator_models = [DEFAULT_EVALUATOR_LLM_MODEL]

        # Process criteria based on structure
        processed_criteria = self._process_criteria(criteria, trust_level)

        # Filter out tone-related criteria for workflow evaluations
        if not self._should_include_tone_analysis(evaluation_context, criteria):
            processed_criteria = self._filter_tone_criteria(processed_criteria)

        results = {}
        errors = []

        # Evaluate with each model
        for model in evaluator_models:
            try:
                model_result = await self._evaluate_with_model(
                    model,
                    scenario_context,
                    agent_response,
                    processed_criteria,
                    evaluation_context
                )
                results[model] = model_result
            except Exception as e:
                logger.error(f"Error evaluating with model {model}: {str(e)}", exc_info=True)
                errors.append(f"Model {model}: {str(e)}")
                results[model] = {
                    "error": True,
                    "error_message": str(e),
                    "overall_score": 0.0,
                    "overall_reasoning": f"Evaluation failed: {str(e)}",
                    "dimensions": {}
                }

                # Report error via EventService if user_profile_id is available
                if user_profile_id:
                    await EventService.emit_debug_info(
                        event_type="semantic_evaluation_error",
                        data={
                            "error": str(e),
                            "model": model,
                            "scenario_context": scenario_context[:100] + "..." if len(scenario_context) > 100 else scenario_context,
                            "agent_response": agent_response[:100] + "..." if len(agent_response) > 100 else agent_response
                        },
                        user_profile_id=user_profile_id
                    )

        # Add metadata to results
        results["_meta"] = {
            "models_used": evaluator_models,
            "primary_model": evaluator_models[0] if evaluator_models else None,
            "errors": errors,
            "criteria_dimensions": list(processed_criteria.keys()),
            "trust_level": trust_level,
            "phase": self._get_phase_for_trust_level(trust_level) if trust_level is not None else None
        }

        # Add normalized scores if we have multiple models
        if len(evaluator_models) > 1 and not all("error" in results[model] for model in evaluator_models):
            results["_meta"]["normalized_scores"] = self._normalize_scores(results, evaluator_models)

        # Calculate weighted tone score if tone analysis is available and appropriate for evaluation context
        if any("tone_analysis" in results[model] for model in evaluator_models):
            # Only include tone analysis for agent evaluations, not workflow evaluations
            if self._should_include_tone_analysis(evaluation_context, criteria):
                results["_meta"]["weighted_tone_score"] = self._calculate_weighted_tone_score(
                    results,
                    evaluator_models,
                    scenario_context,
                    user_profile_id,
                    trust_level
                )


        return results

    def _should_include_tone_analysis(self, evaluation_context: Optional[str], criteria: Dict[str, Any]) -> bool:
        """
        Determine if tone analysis should be included based on evaluation context.

        Args:
            evaluation_context: Context of the evaluation (e.g., 'agent', 'workflow')
            criteria: Evaluation criteria to check for tone-related dimensions

        Returns:
            True if tone analysis should be included, False otherwise
        """
        # If evaluation_context is explicitly provided, use it
        if evaluation_context:
            # Include tone analysis ONLY for agent evaluations, exclude for workflow evaluations
            return evaluation_context.lower() in ['agent', 'mentor']

        # Fallback: Check for workflow-specific keywords that indicate this is NOT a tone evaluation
        criteria_text = str(criteria).lower()
        workflow_keywords = ['output_relevance', 'content_quality', 'system_performance', 'efficiency', 'wheel_generation', 'workflow']
        has_workflow_criteria = any(keyword in criteria_text for keyword in workflow_keywords)

        # If we detect workflow-specific criteria, exclude tone analysis
        if has_workflow_criteria:
            return False

        # Default fallback: include tone analysis only if explicitly requested through criteria
        # This is a conservative approach - tone analysis should be explicit
        return False

    def _filter_tone_criteria(self, criteria: Dict[str, List[str]]) -> Dict[str, List[str]]:
        """
        Filter out tone-related criteria dimensions for workflow evaluations.

        Args:
            criteria: Processed criteria dictionary

        Returns:
            Filtered criteria dictionary without tone-related dimensions
        """
        tone_related_keys = ['tone', 'communication', 'empathy', 'supportive', 'trust_building']
        filtered_criteria = {}

        for dimension, dimension_criteria in criteria.items():
            # Check if this dimension is tone-related
            dimension_lower = dimension.lower()
            is_tone_related = any(tone_key in dimension_lower for tone_key in tone_related_keys)

            if not is_tone_related:
                filtered_criteria[dimension] = dimension_criteria
            else:
                logger.info(f"Filtering out tone-related dimension '{dimension}' for workflow evaluation")

        return filtered_criteria

    def _process_criteria(self, criteria: Dict[str, Any], trust_level: Optional[int] = None) -> Dict[str, List[str]]:
        """
        Process criteria based on structure and trust level.

        Args:
            criteria: Dictionary containing criteria (either flat or phase-aware)
            trust_level: Optional trust level (0-100) for phase-aware criteria

        Returns:
            Dictionary mapping dimension names to lists of criteria
        """
        # If criteria is already in the expected format (dimension -> list of criteria)
        if all(isinstance(value, list) for value in criteria.values()):
            return criteria

        # If criteria contains 'criteria' key directly
        if 'criteria' in criteria:
            return self._extract_dimensions_from_criteria(criteria['criteria'])

        # If criteria contains phase-aware structure
        if 'evaluation_criteria_by_phase' in criteria:
            phase = self._get_phase_for_trust_level(trust_level or 20)
            phase_criteria = criteria['evaluation_criteria_by_phase'].get(phase, {})

            # If phase has no criteria, fall back to foundation phase
            if not phase_criteria or 'criteria' not in phase_criteria:
                logger.warning(f"No criteria found for phase '{phase}'. Falling back to foundation phase.")
                phase_criteria = criteria['evaluation_criteria_by_phase'].get('foundation', {})

            if 'criteria' in phase_criteria:
                return self._extract_dimensions_from_criteria(phase_criteria['criteria'])

        # If we get here, the criteria structure is not recognized
        logger.warning(f"Unrecognized criteria structure: {criteria}. Using empty criteria.")
        return {}

    def _extract_dimensions_from_criteria(self, criteria_list: List[Dict[str, Any]]) -> Dict[str, List[str]]:
        """
        Extract dimensions from a list of criteria objects.

        Args:
            criteria_list: List of criteria objects with 'name' and 'description'

        Returns:
            Dictionary mapping dimension names to lists of criteria
        """
        dimensions = {}
        for criterion in criteria_list:
            if 'name' in criterion:
                dimension = criterion.get('dimension', 'General')
                if dimension not in dimensions:
                    dimensions[dimension] = []
                dimensions[dimension].append(criterion.get('description', criterion['name']))
        return dimensions

    def _get_phase_for_trust_level(self, trust_level: int) -> str:
        """
        Map a trust level (0-100) to a trust phase.

        Args:
            trust_level: Integer trust level between 0 and 100

        Returns:
            String phase name: 'foundation', 'expansion', or 'integration'
        """
        # Import the utility function
        from apps.main.services.evaluation_criteria_migration import map_trust_level_to_phase
        return map_trust_level_to_phase(trust_level)

    async def _evaluate_with_model(
        self,
        model_name: str,
        scenario_context: str,
        agent_response: str,
        criteria: Dict[str, List[str]],
        evaluation_context: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Evaluate an agent response using a specific LLM model.

        Args:
            model_name: Identifier of the LLM model to use
            scenario_context: Context of the scenario being evaluated
            agent_response: Agent's response to evaluate
            criteria: Dictionary mapping dimension names to lists of criteria

        Returns:
            Dict containing evaluation results
        """
        # Get or create LLM client for this model
        llm_client = await self._get_llm_client(model_name)

        # Construct the prompt
        prompt = self._construct_evaluation_prompt(
            scenario_context,
            agent_response,
            criteria,
            evaluation_context
        )

        # Send the prompt to the LLM
        messages = [
            {"role": "system", "content": "You are an expert evaluator of AI agent responses. Your task is to evaluate the quality of an agent's response based on specific criteria."},
            {"role": "user", "content": prompt}
        ]

        try:
            response = await llm_client.chat_completion(
                messages=messages,
                max_tokens=2000,
                temperature=0.2  # Low temperature for more consistent evaluations
            )

            # Parse the response
            logger.debug(f"Raw LLM response for semantic evaluation: {response.content}")
            result = self._parse_evaluation_response(response.content)

            # Add token usage information
            result["_tokens"] = {
                "input": response.input_tokens,
                "output": response.output_tokens,
                "total": response.input_tokens + response.output_tokens
            }

            return result
        except Exception as e:
            logger.error(f"Error in LLM evaluation with model {model_name}: {str(e)}", exc_info=True)
            raise

    def _construct_evaluation_prompt(
        self,
        scenario_context: str,
        agent_response: str,
        criteria: Dict[str, List[str]],
        evaluation_context: Optional[str] = None
    ) -> str:
        """
        Construct a prompt for evaluating an agent response.

        Args:
            scenario_context: Context of the scenario being evaluated
            agent_response: Agent's response to evaluate
            criteria: Dictionary mapping dimension names to lists of criteria

        Returns:
            Prompt string for the LLM
        """
        # Format criteria as a bulleted list for each dimension
        criteria_text = ""
        for dimension, dimension_criteria in criteria.items():
            criteria_text += f"\n## {dimension}\n"
            for criterion in dimension_criteria:
                criteria_text += f"- {criterion}\n"

        # Determine if tone analysis should be included
        include_tone = self._should_include_tone_analysis(evaluation_context, criteria)

        # Construct tone analysis instructions if needed
        tone_instructions = ""
        tone_json_example = ""

        if include_tone:
            tone_instructions = """3. Evaluate the tone of the agent's response considering the Scenario Context. Provide scores and reasoning for the following tone dimensions:
    - Formality (0.0 - 1.0, 1.0 being very formal)
    - Sentiment (0.0 - 1.0, 1.0 being very positive)
    - Empathy (0.0 - 1.0, 1.0 being very empathetic)
    - Helpfulness (0.0 - 1.0, 1.0 being very helpful)
4. Provide an overall score from 0.0 to 1.0 and overall reasoning.
5. Format your response as a JSON object with the following structure:"""

            tone_json_example = """,
  "tone_analysis": {
    "formality": {
      "score": 0.7,
      "reasoning": "Reasoning for formality score..."
    },
    "sentiment": {
      "score": 0.9,
      "reasoning": "Reasoning for sentiment score..."
    },
    "empathy": {
      "score": 0.8,
      "reasoning": "Reasoning for empathy score..."
    },
    "helpfulness": {
      "score": 0.95,
      "reasoning": "Reasoning for helpfulness score..."
    }
  }"""
        else:
            tone_instructions = """3. Provide an overall score from 0.0 to 1.0 and overall reasoning.
4. Format your response as a JSON object with the following structure:"""

        # Construct the full prompt
        prompt = f"""
# Evaluation Task

## Scenario Context
{scenario_context}

## Agent Response to Evaluate
{agent_response}

## Evaluation Criteria
{criteria_text}

## Instructions
1. Evaluate the agent's response based on the criteria for each dimension.
2. For each dimension, provide:
   - A score from 0.0 to 1.0 (where 1.0 is perfect)
   - Detailed reasoning for your score
{tone_instructions}

```json
{{
  "dimensions": {{
    "Dimension1": {{
      "score": 0.85,
      "reasoning": "Detailed reasoning for this dimension..."
    }},
    "Dimension2": {{
      "score": 0.75,
      "reasoning": "Detailed reasoning for this dimension..."
    }}
  }}{tone_json_example},
  "overall_score": 0.80,
  "overall_reasoning": "Overall reasoning for the combined score..."
}}
```

Ensure your response is valid JSON and includes all dimensions from the criteria{' and the specified tone dimensions within the \'tone_analysis\' object' if include_tone else ''}.
"""
        return prompt

    def _parse_evaluation_response(self, response_text: str) -> Dict[str, Any]:
        """
        Parse the LLM's evaluation response.

        Args:
            response_text: Text response from the LLM

        Returns:
            Dict containing parsed evaluation results
        """
        try:
            # Extract JSON from the response (it might be wrapped in markdown code blocks)
            json_match = re.search(r'```(?:json)?\s*([\s\S]*?)\s*```', response_text)
            if json_match:
                json_str = json_match.group(1)
            else:
                # If no code blocks, try to find JSON-like content
                json_str = response_text

            # Parse the JSON
            result = json.loads(json_str)

            # Validate the structure and extract tone analysis
            dimensions = result.get("dimensions", {})
            tone_analysis = dimensions.pop("Tone", None) # Extract Tone and remove from dimensions

            if "overall_score" not in result:
                # If overall score is missing, calculate average of remaining dimension scores
                dimension_scores = [dim["score"] for dim in dimensions.values()
                                   if isinstance(dim, dict) and "score" in dim]
                result["overall_score"] = sum(dimension_scores) / len(dimension_scores) if dimension_scores else 0.0

            if "overall_reasoning" not in result:
                result["overall_reasoning"] = "No overall reasoning provided."

            # Add extracted tone analysis back to the result
            result["dimensions"] = dimensions
            if tone_analysis:
                result["tone_analysis"] = tone_analysis

            return result
        except Exception as e:
            logger.error(f"Error parsing evaluation response: {str(e)}", exc_info=True)
            logger.debug(f"Raw response: {response_text}")

            # Return a minimal valid result
            return {
                "error": True,
                "error_message": f"Failed to parse evaluation response: {str(e)}",
                "overall_score": 0.0,
                "overall_reasoning": "Failed to parse evaluation response.",
                "dimensions": {},
                "raw_response": response_text
            }

    def _normalize_scores(
        self,
        results: Dict[str, Any],
        evaluator_models: List[str]
    ) -> Dict[str, float]:
        """
        Normalize scores across different evaluator models.

        Args:
            results: Dictionary of evaluation results from each model
            evaluator_models: List of evaluator model names

        Returns:
            Dict mapping dimension names to normalized scores
        """
        # Extract valid models (those without errors)
        valid_models = [model for model in evaluator_models
                       if model in results and not results[model].get("error", False)]

        if not valid_models:
            return {"overall": 0.0}

        # Get all dimensions across all models
        all_dimensions = set()
        for model in valid_models:
            all_dimensions.update(results[model].get("dimensions", {}).keys())

        # Calculate normalized scores
        normalized_scores = {}

        # Overall score (average across all models)
        overall_scores = [results[model].get("overall_score", 0.0) for model in valid_models]
        normalized_scores["overall"] = sum(overall_scores) / len(overall_scores)

        # Dimension scores (average across all models that have the dimension)
        for dimension in all_dimensions:
            dimension_scores = []
            for model in valid_models:
                model_dimensions = results[model].get("dimensions", {})
                if dimension in model_dimensions and "score" in model_dimensions[dimension]:
                    dimension_scores.append(model_dimensions[dimension]["score"])

            if dimension_scores:
                normalized_scores[dimension] = sum(dimension_scores) / len(dimension_scores)

        return normalized_scores

    def _calculate_weighted_tone_score(
        self,
        results: Dict[str, Any],
        evaluator_models: List[str],
        scenario_context: str,
        user_profile_id: Optional[str],
        trust_level: Optional[int]
    ) -> float:
        """
        Calculate a single weighted tone score based on individual tone dimension scores
        and context information.

        Args:
            results: Dictionary of evaluation results from each model
            evaluator_models: List of evaluator model names
            scenario_context: Context of the scenario being evaluated
            user_profile_id: Optional ID of the user initiating the evaluation
            trust_level: Optional trust level (0-100) for phase-aware criteria

        Returns:
            A single weighted tone score (0.0 - 1.0)
        """
        valid_models_with_tone = [
            model for model in evaluator_models
            if model in results and not results[model].get("error", False) and "tone_analysis" in results[model]
        ]

        if not valid_models_with_tone:
            return 0.0

        tone_dimensions = ["formality", "sentiment", "empathy", "helpfulness"]
        dimension_scores_sum: Dict[str, float] = {dim: 0.0 for dim in tone_dimensions}
        dimension_counts: Dict[str, int] = {dim: 0 for dim in tone_dimensions}

        for model in valid_models_with_tone:
            tone_analysis = results[model].get("tone_analysis", {})
            for dim in tone_dimensions:
                if dim in tone_analysis and isinstance(tone_analysis[dim], dict) and "score" in tone_analysis[dim]:
                    dimension_scores_sum[dim] += tone_analysis[dim]["score"]
                    dimension_counts[dim] += 1

        # Calculate average score for each dimension
        dimension_averages: Dict[str, float] = {}
        for dim in tone_dimensions:
            if dimension_counts[dim] > 0:
                dimension_averages[dim] = dimension_scores_sum[dim] / dimension_counts[dim]

        # Simple average of dimension averages for now
        if not dimension_averages:
            return 0.0

        overall_weighted_tone_score = sum(dimension_averages.values()) / len(dimension_averages)

        # TODO: Implement context-aware weighting logic here

        return overall_weighted_tone_score


    async def _get_llm_client(self, model_name: str) -> RealLLMClient:
        """
        Get or create an LLM client for a specific model.

        Args:
            model_name: Identifier of the LLM model (e.g., "mistral/mistral-small")

        Returns:
            RealLLMClient instance for the specified model
        """
        if model_name not in self.llm_services:
            # Import LLMConfig here to avoid circular imports
            from apps.main.models import LLMConfig
            from asgiref.sync import sync_to_async

            # Try to find an LLMConfig that matches this model name
            llm_config = None
            try:
                # First try exact match on model_name field
                llm_config = await sync_to_async(
                    lambda: LLMConfig.objects.filter(model_name=model_name).first(),
                    thread_sensitive=True
                )()

                # If no exact match, try to find by partial match (e.g., "mistral-small" for "mistral/mistral-small")
                if not llm_config:
                    # Extract the model part after the slash if present
                    if '/' in model_name:
                        model_part = model_name.split('/')[-1]
                        llm_config = await sync_to_async(
                            lambda: LLMConfig.objects.filter(model_name__icontains=model_part).first(),
                            thread_sensitive=True
                        )()

                # If still no match, try to get the default evaluation config
                if not llm_config:
                    llm_config = await sync_to_async(
                        lambda: LLMConfig.get_default(evaluation=True),
                        thread_sensitive=True
                    )()
                    if llm_config:
                        logger.warning(f"No LLMConfig found for model '{model_name}', using default evaluation config: {llm_config.name}")

                # If still no config, get the regular default
                if not llm_config:
                    llm_config = await sync_to_async(
                        lambda: LLMConfig.get_default(evaluation=False),
                        thread_sensitive=True
                    )()
                    if llm_config:
                        logger.warning(f"No LLMConfig found for model '{model_name}', using default config: {llm_config.name}")

                # If we still have no config, create a temporary one
                if not llm_config:
                    logger.error(f"No LLMConfig found for model '{model_name}' and no default available. Creating temporary config.")
                    # Create a temporary config (not saved to DB)
                    llm_config = LLMConfig(
                        name=f"temp-{model_name}",
                        model_name=model_name,
                        temperature=0.7,
                        is_default=False,
                        is_evaluation=True
                    )

            except Exception as e:
                logger.error(f"Error finding LLMConfig for model '{model_name}': {str(e)}")
                # Create a fallback temporary config
                llm_config = LLMConfig(
                    name=f"fallback-{model_name}",
                    model_name=model_name,
                    temperature=0.7,
                    is_default=False,
                    is_evaluation=True
                )

            # Create a new LLM client for this model using the found/created config
            try:
                self.llm_services[model_name] = RealLLMClient(llm_config=llm_config)
                logger.info(f"Created LLM client for model '{model_name}' using config '{llm_config.name}'")
            except Exception as e:
                logger.error(f"Error creating RealLLMClient for model '{model_name}': {str(e)}")
                raise

        return self.llm_services[model_name]

    def _extract_response_text(self, response_data: Any) -> str:
        """
        Extract text from various response formats.

        Args:
            response_data: Response data which could be a string, dict, or complex object

        Returns:
            Extracted text as a string
        """
        # If it's already a string, return it directly
        if isinstance(response_data, str):
            return response_data

        # If it's a dict, look for common response fields
        if isinstance(response_data, dict):
            # Check for common response fields
            if "response" in response_data:
                if isinstance(response_data["response"], str):
                    return response_data["response"]
                elif isinstance(response_data["response"], dict) and "text" in response_data["response"]:
                    return response_data["response"]["text"]

            # Check for message field
            if "message" in response_data:
                return response_data["message"]

            # Check for text field
            if "text" in response_data:
                return response_data["text"]

        # Fallback to string representation
        return str(response_data)

    def _get_scenario_context(self, scenario) -> str:
        """
        Extract context information from a benchmark scenario.

        Args:
            scenario: BenchmarkScenario instance

        Returns:
            Formatted context string for evaluation
        """
        context_parts = []

        # Add basic scenario information
        context_parts.append(f"Scenario Name: {scenario.name}")
        if hasattr(scenario, 'description') and scenario.description:
            context_parts.append(f"Description: {scenario.description}")

        # Add workflow type if available
        if hasattr(scenario, 'metadata') and scenario.metadata:
            if 'workflow_type' in scenario.metadata:
                context_parts.append(f"Workflow Type: {scenario.metadata['workflow_type']}")

            # Add user profile context if available
            if 'user_profile_context' in scenario.metadata:
                user_profile = scenario.metadata['user_profile_context']
                context_parts.append("User Profile:")

                # Extract key user information
                if isinstance(user_profile, dict):
                    for key, value in user_profile.items():
                        if isinstance(value, (str, int, float, bool)):
                            context_parts.append(f"  - {key}: {value}")
                        else:
                            # Summarize complex data
                            context_parts.append(f"  - {key}: <complex data>")

        # Add input data
        if hasattr(scenario, 'input_data') and scenario.input_data:
            context_parts.append("Input Data:")
            if isinstance(scenario.input_data, dict):
                for key, value in scenario.input_data.items():
                    if isinstance(value, (str, int, float, bool)):
                        context_parts.append(f"  - {key}: {value}")
                    else:
                        # Summarize complex data
                        context_parts.append(f"  - {key}: <complex data>")
            else:
                context_parts.append(f"  {scenario.input_data}")

        # Join all parts with newlines
        return "\n".join(context_parts)
