"""
Tool registration, execution, and management utilities for the multi-agent system.

This module provides the centralized functionality for:
1. Registering tools through decorators (marks functions for later discovery)
2. Synchronizing tools with the database (discovers marked functions)
3. Executing tools with standardized error handling and monitoring
4. Dynamic tool loading and discovery
"""

import asyncio
import time
import logging
import importlib
import inspect
import hashlib
import json
import os # Needed for TESTING check
import sys # Needed for module checks
from inspect import signature
from typing import Dict, Any, List, Optional, Union, Callable, Awaitable, Tuple
import traceback
from functools import wraps
import jsonschema

# --- DEFER IMPORT: EventService ---
# from apps.main.services.event_service import EventService
# --- END DEFER IMPORT ---

# Configure logging
logger = logging.getLogger(__name__)

# Type definition
ToolHandler = Callable[[Dict[str, Any]], Awaitable[Dict[str, Any]]]

# --- REMOVED GLOBAL REGISTRIES ---
# Tools are now discovered dynamically during sync
# _TOOL_REGISTRY: Dict[str, Too<PERSON><PERSON><PERSON>ler] = {}
# _TOOL_PATHS: Dict[str, str] = {}
# --- END REMOVED GLOBAL REGISTRIES ---

# Define the attribute name used to mark tool functions
_TOOL_CODE_ATTR = '_agent_tool_code'

def register_tool(tool_code: str):
    """
    Decorator to MARK a function as a tool handler for later discovery.
    It attaches the tool_code as an attribute to the function.
    It no longer populates a global registry at import time.

    Example:
        @register_tool('user_traits_query')
        async def user_traits_handler(input_data):
            # Implementation
            return result
    """
    def decorator(func: ToolHandler) -> ToolHandler:
        if not asyncio.iscoroutinefunction(func):
            raise ValueError(f"Tool handler for '{tool_code}' must be an async function")

        # Attach the tool code as an attribute for later discovery
        setattr(func, _TOOL_CODE_ATTR, tool_code)
        logger.debug(f"Marked function {func.__name__} as tool '{tool_code}'")
        return func

    return decorator

# --- validate_tool_input decorator remains the same ---
def validate_tool_input(schema: Dict[str, Any]):
    """
    Decorator to validate tool input against a JSON schema.
    Args:
        schema: JSON schema for validation

    Returns:
        Decorator function
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(**kwargs):
            try:
                jsonschema.validate(instance=kwargs, schema=schema)
                if asyncio.iscoroutinefunction(func):
                    return await func(**kwargs)
                else:
                    return await asyncio.get_event_loop().run_in_executor(
                        None, lambda: func(**kwargs)
                    )
            except jsonschema.exceptions.ValidationError as e:
                logger.error(f"Tool input validation failed: {str(e)}")
                raise ToolExecutionError(f"Invalid input for tool {func.__name__}: {str(e)}")
        return wrapper
    return decorator

# --- get_tool_handler, get_tool_path, list_available_tools are removed ---
# --- They relied on the global registry which no longer exists. ---
# --- Tool execution will now dynamically load handlers. ---

def discover_tools_in_modules(module_names: List[str]) -> Dict[str, Dict[str, Any]]:
    """
    Dynamically discovers functions marked with @register_tool in specified modules.

    Args:
        module_names: A list of fully qualified module names to search.

    Returns:
        A dictionary where keys are tool codes and values are dictionaries
        containing tool info ('name', 'description', 'path', 'input_schema',
        'output_schema', 'handler').
    """
    discovered_tools_info = {}
    logger.info(f"Starting tool discovery in modules: {module_names}")

    for module_name in module_names:
        try:
            # Ensure the module is loaded or reloaded
            if module_name in sys.modules:
                module = importlib.reload(sys.modules[module_name])
                logger.debug(f"Reloaded module: {module_name}")
            else:
                module = importlib.import_module(module_name)
                logger.debug(f"Imported module: {module_name}")

            # Inspect members of the module
            for name, obj in inspect.getmembers(module):
                if inspect.isfunction(obj) and hasattr(obj, _TOOL_CODE_ATTR):
                    tool_code = getattr(obj, _TOOL_CODE_ATTR)
                    if tool_code in discovered_tools_info:
                        logger.warning(f"Duplicate tool code '{tool_code}' found in {module_name} (already found). Skipping.")
                        continue

                    logger.debug(f"Discovered tool function: {name} with code: {tool_code}")
                    handler = obj
                    docstring = inspect.getdoc(handler) or "No documentation available"
                    # signature = inspect.signature(handler) # Signature not needed for basic info

                    # Parse docstring for basic info (can be enhanced)
                    input_schema = {"type": "object", "properties": {}} # Default/placeholder
                    output_schema = {"type": "object", "properties": {}} # Default/placeholder
                    tool_name = docstring.split('.')[0].strip() # Simple name extraction

                    # Construct path
                    function_path = f"{module_name}.{handler.__qualname__}"

                    discovered_tools_info[tool_code] = {
                        "name": tool_name or tool_code, # Use code as fallback name
                        "description": docstring,
                        "path": function_path,
                        "input_schema": input_schema, # TODO: Enhance schema extraction if needed
                        "output_schema": output_schema, # TODO: Enhance schema extraction if needed
                        "handler": handler # Store the actual handler function
                    }
        except ImportError:
            logger.warning(f"Could not import module {module_name} during tool discovery.")
        except Exception as e:
            logger.error(f"Error discovering tools in module {module_name}: {e}", exc_info=True)

    logger.info(f"Discovered {len(discovered_tools_info)} tools.")
    return discovered_tools_info


def _calculate_tool_hash(info: Dict[str, Any]) -> str:
    """Calculates a SHA-256 hash for a tool's definition."""
    hasher = hashlib.sha256()
    hasher.update(info['name'].encode('utf-8'))
    hasher.update(info['description'].encode('utf-8'))
    hasher.update(info['path'].encode('utf-8'))
    hasher.update(json.dumps(info['input_schema'], sort_keys=True).encode('utf-8'))
    hasher.update(json.dumps(info['output_schema'], sort_keys=True).encode('utf-8'))
    return hasher.hexdigest()


def sync_tool_registry_with_database():
    """
    Discovers tools marked with @register_tool in predefined modules
    and synchronizes them with the database idempotently using hashes.
    """
    # Import necessary Django components here, inside the function
    from django.db import transaction
    from apps.main.models import AgentTool

    logger.info("Starting tool registry synchronization with database...")

    # Define modules where tools are expected to be found
    tool_module_names = [
        'apps.main.agents.tools.tools',
        'apps.main.agents.tools.extra_tools',
        'apps.main.agents.tools.get_user_profile_tool',
        'apps.main.agents.tools.dispatcher_tools',
        'apps.main.agents.tools.mentor_tools',
        'apps.main.agents.tools.update_current_mood_tool',
        'apps.main.agents.tools.resource_tools',
        'apps.main.agents.tools.engagement_tools',
        'apps.main.agents.tools.psychological_tools',
        'apps.main.agents.tools.activity_tools',
        'apps.main.agents.tools.ethical_tools',
        # Add other tool modules here
    ]

    # 1. Discover tools from the code
    discovered_tools = discover_tools_in_modules(tool_module_names)
    logger.debug(f"Found {len(discovered_tools)} tools via discovery.")

    # 2. Get existing tools from the database
    existing_db_tools = {
        tool.code: {'id': tool.id, 'hash': tool.definition_hash, 'is_active': tool.is_active}
        for tool in AgentTool.objects.all()
    }
    logger.debug(f"Found {len(existing_db_tools)} tools in the database.")

    tools_to_create = []
    tools_to_update = []
    processed_db_codes = set()

    # 3. Compare discovered tools with database tools
    with transaction.atomic():
        for tool_code, info in discovered_tools.items():
            current_hash = _calculate_tool_hash(info)
            processed_db_codes.add(tool_code)

            if tool_code in existing_db_tools:
                db_tool = existing_db_tools[tool_code]
                if db_tool['hash'] != current_hash or not db_tool['is_active']:
                    logger.info(f"Updating tool '{tool_code}' (Hash changed or reactivated).")
                    tool = AgentTool.objects.get(id=db_tool['id'])
                    tool.name = info['name']
                    tool.description = info['description']
                    tool.function_path = info['path']
                    tool.input_schema = info['input_schema']
                    tool.output_schema = info['output_schema']
                    tool.definition_hash = current_hash
                    tool.is_active = True
                    tools_to_update.append(tool)
            else:
                logger.info(f"Creating new tool '{tool_code}'.")
                tools_to_create.append(
                    AgentTool(
                        code=tool_code, name=info['name'], description=info['description'],
                        function_path=info['path'], input_schema=info['input_schema'],
                        output_schema=info['output_schema'], definition_hash=current_hash,
                        is_active=True, allowed_agent_roles=[]
                    )
                )

        if tools_to_create:
            AgentTool.objects.bulk_create(tools_to_create)
            logger.info(f"Created {len(tools_to_create)} new tools.")
        if tools_to_update:
            AgentTool.objects.bulk_update(
                tools_to_update,
                ['name', 'description', 'function_path', 'input_schema', 'output_schema', 'definition_hash', 'is_active']
            )
            logger.info(f"Updated {len(tools_to_update)} existing tools.")

        # 4. Deactivate tools in DB no longer found by discovery
        codes_to_deactivate = set(existing_db_tools.keys()) - processed_db_codes
        if codes_to_deactivate:
            logger.info(f"Deactivating {len(codes_to_deactivate)} tools not found in discovery: {', '.join(codes_to_deactivate)}")
            AgentTool.objects.filter(code__in=codes_to_deactivate, is_active=True).update(is_active=False)

    logger.info("Tool registry synchronization finished.")


class ToolExecutionError(Exception):
    """Custom exception for tool execution errors."""
    pass


async def execute_tool(
    tool_code: str,
    tool_input: Dict[str, Any],
    run_id: Optional[str] = None,
    context: Optional[Dict[str, Any]] = None,
    user_profile_id: Optional[str] = None,
    session_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Execute a tool identified by its code with monitoring and error handling.
    Tool handlers are loaded dynamically.
    """
    # --- DEFER IMPORT: EventService ---
    from apps.main.services.event_service import EventService
    # --- END DEFER IMPORT ---

    start_time = time.time()
    context = context or {}
    logger.debug(f"Executing tool: {tool_code} with input: {tool_input}")

    try:
        # Dynamically load the tool handler function
        tool_func = await _load_tool_by_code(tool_code)
        if not tool_func:
            raise ToolExecutionError(f"Tool not found or failed to load: {tool_code}")

        logger.info(f"Attempting to execute tool handler: {tool_code} (Function: {tool_func.__name__})")
        result = await _execute_with_monitoring(tool_func, tool_input, start_time, run_id)

        if run_id:
            await _record_tool_usage(tool_code, tool_input, result, start_time, time.time(), run_id)

        return result

    except ToolExecutionError as e:
        error_message = str(e)
        if "Missing required arguments" in error_message:
            await EventService.emit_tool_argument_error(
                tool_name=tool_code, error_message=error_message, source="execute_tool",
                user_profile_id=user_profile_id, session_id=session_id
            )
        raise # Re-raise ToolExecutionError directly
    except Exception as e:
        error_message = str(e)
        logger.error(f"Error executing tool {tool_code}: {error_message}", exc_info=True)
        if run_id:
            await _record_tool_usage(
                tool_code, tool_input, {"error": error_message},
                start_time, time.time(), run_id, error=error_message
            )
        raise ToolExecutionError(f"Error executing tool {tool_code}: {error_message}") from e


async def _execute_with_monitoring(
    tool_func: Callable,
    tool_input: Dict[str, Any],
    start_time: float,
    run_id: Optional[str],
) -> Dict[str, Any]:
    """
    Execute a tool function with performance monitoring, filtering input arguments.
    (Logic remains largely the same)
    """
    sig = signature(tool_func)
    tool_params = sig.parameters
    filtered_input = { k: v for k, v in tool_input.items() if k in tool_params }
    required_params = {
        name for name, param in tool_params.items()
        if param.default == inspect.Parameter.empty and param.kind == inspect.Parameter.POSITIONAL_OR_KEYWORD
    }
    missing_params = required_params - set(filtered_input.keys())
    if missing_params:
        raise ToolExecutionError(
            f"Missing required arguments for tool {tool_func.__name__}: {', '.join(missing_params)}. "
            f"Provided input after filtering: {list(filtered_input.keys())}"
        )
    try:
        if asyncio.iscoroutinefunction(tool_func):
            result = await tool_func(**filtered_input)
        else:
            result = await asyncio.get_event_loop().run_in_executor(
                None, lambda: tool_func(**filtered_input)
            )
    except TypeError as e:
        logger.error(f"TypeError during tool execution {tool_func.__name__}: {e}. Filtered input: {filtered_input}", exc_info=True)
        raise ToolExecutionError(f"TypeError executing tool {tool_func.__name__}: {e}") from e

    duration = time.time() - start_time
    logger.debug(f"Tool {tool_func.__name__} executed in {duration:.2f}s")
    return result


async def _load_tool_by_code(tool_code: str) -> Optional[Callable]:
    """
    Dynamically load a tool function based on its code by checking the database first.
    """
    # Defer imports
    from apps.main.models import AgentTool
    from asgiref.sync import sync_to_async

    logger.debug(f"Attempting to load tool handler for code: {tool_code}")

    @sync_to_async
    def get_tool_path_db():
        # Defer model import inside sync function too
        from apps.main.models import AgentTool
        tool = AgentTool.objects.filter(code=tool_code, is_active=True).first()
        return tool.function_path if tool else None

    try:
        tool_path = await get_tool_path_db()
        if tool_path:
            logger.debug(f"Found tool path in DB for '{tool_code}': {tool_path}")
            return import_tool_handler(tool_path)
        else:
            logger.warning(f"Tool code '{tool_code}' not found in database or is inactive.")
            # Optional: Could add fallback to search modules if needed, but DB is primary source now
            return None
    except Exception as e:
        logger.error(f"Error loading tool {tool_code} from DB path: {str(e)}", exc_info=True)
        return None


# --- _get_tool_path_from_db is now integrated into _load_tool_by_code ---

async def _record_tool_usage(
    tool_code: str,
    input_data: Dict[str, Any],
    output_data: Dict[str, Any],
    start_time: float,
    end_time: float,
    run_id: str,
    error: Optional[str] = None
) -> None:
    """
    Record tool usage in the database for the given run.
    (Logic remains largely the same, ensure imports are deferred)
    """
    try:
        # Defer imports
        from apps.main.models import AgentRun, AgentTool
        from django.utils import timezone
        from asgiref.sync import sync_to_async

        duration = end_time - start_time

        @sync_to_async
        def update_run():
            # Defer model imports inside sync function
            from apps.main.models import AgentRun, AgentTool
            try:
                run = AgentRun.objects.get(id=run_id)
                run.add_tool_call(
                    tool_code=tool_code, input_data=input_data, output_data=output_data,
                    start_time=timezone.datetime.fromtimestamp(start_time),
                    end_time=timezone.datetime.fromtimestamp(end_time), error=error
                )
                tool = AgentTool.objects.filter(code=tool_code).first()
                if tool:
                    tool.usage_count += 1
                    total_time = tool.avg_response_time * (tool.usage_count - 1) + duration
                    tool.avg_response_time = total_time / tool.usage_count
                    if error:
                        tool.error_rate = (tool.error_rate * (tool.usage_count - 1) + 100) / tool.usage_count
                    else:
                        tool.error_rate = (tool.error_rate * (tool.usage_count - 1)) / tool.usage_count
                    tool.save(update_fields=['usage_count', 'avg_response_time', 'error_rate'])
            except AgentRun.DoesNotExist:
                logger.warning(f"Cannot record tool usage: Run {run_id} not found")
            except Exception as e:
                logger.error(f"Error updating run with tool call: {str(e)}", exc_info=True)

        await update_run()
    except Exception as e:
        logger.error(f"Error recording tool usage: {str(e)}", exc_info=True)


async def get_available_tools(agent_role: str) -> List[Dict[str, Any]]:
    """
    Get all available tools for a specific agent role from the database.
    (Logic remains largely the same, ensure imports are deferred)
    """
    try:
        # Defer imports
        from apps.main.models import GenericAgent, AgentTool
        from asgiref.sync import sync_to_async

        @sync_to_async
        def get_tools():
            # Defer model imports inside sync function
            from apps.main.models import GenericAgent, AgentTool
            agent = GenericAgent.objects.filter(role=agent_role, is_active=True).first()
            if not agent: return []
            return [
                {
                    "code": tool.code, "name": tool.name, "description": tool.description,
                    "input_schema": tool.input_schema, "output_schema": tool.output_schema
                }
                for tool in agent.available_tools.filter(is_active=True)
            ]
        return await get_tools()
    except Exception as e:
        logger.error(f"Error getting available tools for {agent_role}: {str(e)}", exc_info=True)
        return []


def import_tool_handler(path: str) -> Optional[ToolHandler]:
    """
    Dynamically import a tool handler function by its fully qualified path.
    (Logic remains the same)
    """
    try:
        module_path, function_name = path.rsplit('.', 1)
        module = importlib.import_module(module_path)
        return getattr(module, function_name)
    except (ImportError, AttributeError, ValueError) as e:
        logger.error(f"Error importing tool handler {path}: {str(e)}")
        return None


def get_tool_registry_info() -> Dict[str, Dict[str, Any]]:
    """
    Get information about all registered tools from the database.
    This function is used by the testing framework to discover tools.

    Returns:
        Dict[str, Dict[str, Any]]: A dictionary where keys are tool codes and values are dictionaries
        containing tool info ('name', 'description', 'path', 'input_schema', 'output_schema').
    """
    # Import SynchronousOnlyOperation at the top level to catch it properly
    from django.core.exceptions import SynchronousOnlyOperation

    try:
        # Check if we're in a testing environment
        if os.environ.get('TESTING', 'false').lower() == 'true':
            logger.info("Running in test environment, using mock tool registry")
            return _get_mock_tool_registry()

        # Import here to avoid circular imports
        from django.apps import apps
        # Check if Django apps are ready
        if not apps.apps_ready:
            logger.warning("Django apps not ready, using mock tool registry")
            return _get_mock_tool_registry()

        from apps.main.models import AgentTool

        # Try to get all active tools from the database
        tools = AgentTool.objects.filter(is_active=True)

        # Convert to dictionary
        tool_info = {}
        for tool in tools:
            tool_info[tool.code] = {
                'name': tool.name,
                'description': tool.description,
                'path': tool.function_path,
                'input_schema': tool.input_schema,
                'output_schema': tool.output_schema
            }

        return tool_info
    except SynchronousOnlyOperation as e:
        # Specifically catch SynchronousOnlyOperation first
        logger.warning(f"FIXED: Async context error caught by specific handler, using mock tool registry: {str(e)}")
        return _get_mock_tool_registry()
    except Exception as e:
        if "Apps aren't loaded yet" in str(e) or "AppRegistryNotReady" in str(e):
            logger.warning(f"Django AppRegistryNotReady error: {str(e)}")
            return _get_mock_tool_registry()
        # Log other errors
        logger.error(f"Error getting tool registry info: {str(e)}", exc_info=True)
        return _get_mock_tool_registry()


def _get_mock_tool_registry() -> Dict[str, Dict[str, Any]]:
    """
    Get a mock tool registry for testing.

    Returns:
        Dict[str, Dict[str, Any]]: A dictionary of mock tool definitions.
    """
    return {
        'analyze_psychological_state': {
            'name': 'Analyze Psychological State',
            'description': 'Analyzes the user\'s current psychological state based on context.',
            'path': 'apps.main.agents.tools.psychological_tools.analyze_psychological_state',
            'input_schema': {
                'type': 'object',
                'properties': {
                    'user_profile_id': {'type': 'string'},
                    'context': {'type': 'object'}
                },
                'required': ['user_profile_id']
            },
            'output_schema': {
                'type': 'object',
                'properties': {
                    'mood': {'type': 'string'},
                    'energy_level': {'type': 'string'},
                    'stress_level': {'type': 'string'},
                    'cognitive_load': {'type': 'string'},
                    'emotional_balance': {'type': 'string'},
                    'confidence': {'type': 'number'}
                }
            }
        },
        'get_trust_metrics': {
            'name': 'Get Trust Metrics',
            'description': 'Retrieves trust metrics for a user.',
            'path': 'apps.main.agents.tools.psychological_tools.get_trust_metrics',
            'input_schema': {
                'type': 'object',
                'properties': {
                    'user_profile_id': {'type': 'string'}
                },
                'required': ['user_profile_id']
            },
            'output_schema': {
                'type': 'object',
                'properties': {
                    'trust_level': {'type': 'number'},
                    'engagement_trust': {'type': 'number'},
                    'action_trust': {'type': 'number'},
                    'disclosure_trust': {'type': 'number'},
                    'phase_duration_days': {'type': 'number'},
                    'confidence': {'type': 'number'}
                }
            }
        },
        'get_trait_analysis': {
            'name': 'Get Trait Analysis',
            'description': 'Retrieves personality trait analysis for a user.',
            'path': 'apps.main.agents.tools.psychological_tools.get_trait_analysis',
            'input_schema': {
                'type': 'object',
                'properties': {
                    'user_profile_id': {'type': 'string'},
                    'framework': {'type': 'string'}
                },
                'required': ['user_profile_id']
            },
            'output_schema': {
                'type': 'object',
                'properties': {
                    'trait_values': {'type': 'object'},
                    'dominant_traits': {'type': 'array', 'items': {'type': 'string'}},
                    'underdeveloped_traits': {'type': 'array', 'items': {'type': 'string'}},
                    'trait_stability': {'type': 'object'},
                    'confidence': {'type': 'number'}
                }
            }
        }
    }
