# apps/main/agents/engagement_agent.py

from typing import Dict, <PERSON>, <PERSON><PERSON>, Optional
from pydantic import BaseModel
import logging
from asgiref.sync import sync_to_async
from django.utils import timezone
from typing import TYPE_CHECKING # Import TYPE_CHECKING

from apps.main.agents.base_agent import LangGraphAgent
# Defer imports that might depend on Django models
# from apps.main.services.database_service import RealDatabaseService
# from apps.main.llm.service import Real<PERSON><PERSON>lient
# from apps.main.agents.tools.tools_util import execute_tool, ToolExecutionError

logger = logging.getLogger(__name__)

# Use TYPE_CHECKING block for type hints if needed
if TYPE_CHECKING:
    from apps.main.services.database_service import RealDatabaseService
    from apps.main.llm.service import RealLLMClient
    from apps.main.agents.tools.tools_util import ToolExecutionError

class EngagementAndPatternAgent(LangGraphAgent):
    """
     Agent that analyzes user engagement patterns and historical preferences
      to inform activity selection and domain distribution.
      """
    def __init__(self, # Removed leading space
                 user_profile_id: str,
                 db_service=None,
                  llm_client=None,
                  llm_config: Optional['LLMConfig'] = None): # Changed to accept LLMConfig, use string literal
        # Import LLMConfig here for type hinting
        from apps.main.models import LLMConfig
        # Pass LLM config up to the base class constructor
        super().__init__(
            user_profile_id=user_profile_id,
            agent_role="engagement",
            llm_config=llm_config # Pass the LLMConfig object
        )

        # Import services here, inside __init__
        from apps.main.services.database_service import RealDatabaseService
        from apps.main.llm.service import RealLLMClient

        # Use provided dependencies or create defaults
        self.db_service = db_service or RealDatabaseService()

        # Instantiate LLM client using config from base class if not provided externally
        if llm_client:
            self.llm_client = llm_client
        else:
            # RealLLMClient now requires an LLMConfig object
            if not self.llm_config:
                logger.debug(f"Instantiating RealLLMClient for EngagementAgent without LLMConfig: will use default")

            logger.debug(f"Instantiating RealLLMClient for EngagementAgent with LLMConfig: {self.llm_config.name if self.llm_config else 'None'}")
            self.llm_client = RealLLMClient(llm_config=self.llm_config) # Pass the LLMConfig object

        # Initialize definition and tools to None. They will be loaded lazily.
        self.agent_definition = None
        self.available_tools = []
        self.run_id = None # Initialize run_id

    async def _ensure_loaded(self):
        """Ensures agent definition and tools are loaded asynchronously."""
        if self.agent_definition is not None:
            return True # Already loaded

        logger.debug(f"Loading definition and tools for {self.agent_role}...")
        try:
            # Wrap synchronous DB call for definition
            load_def_sync = sync_to_async(self.db_service.load_agent_definition, thread_sensitive=True)
            self.agent_definition = await load_def_sync(self.agent_role)

            if self.agent_definition:
                # Call the async load_tools method directly
                self.available_tools = await self.db_service.load_tools(self.agent_definition)
                logger.debug(f"Successfully loaded definition and {len(self.available_tools)} tools for {self.agent_role}")
                return True
            else:
                logger.error(f"Agent definition not found for role: {self.agent_role}")
                self.agent_definition = None
                self.available_tools = []
                return False
        except Exception as e:
            logger.error(f"Failed to load definition/tools for {self.agent_role}: {e}", exc_info=True)
            self.agent_definition = None
            self.available_tools = []
            raise RuntimeError(f"Failed to load agent configuration for {self.agent_role}") from e

    async def _get_memory(self, memory_key: str) -> Any: # Make async
        """
        Retrieve agent memory from the database service asynchronously.

        Args:
            memory_key: The specific memory key to retrieve

        Returns:
            The memory content if found, None otherwise
        """
        try:
            # Await the async get_memory call
            return await self.db_service.get_memory(
                agent_role=self.agent_role,
                user_profile_id=self.user_profile_id,
                memory_key=memory_key
            )
        except Exception as e:
            logger.warning(f"Error retrieving memory '{memory_key}': {str(e)}")
            return None

    async def _set_memory(self, memory_key: str, content: Any, confidence: float = 1.0, expires_at=None): # Make async, add expires_at
        """
        Store agent memory using the database service asynchronously.
            memory_key: The specific memory key to store
            content: The content to store
            confidence: Optional confidence level (0.0-1.0)
            expires_at: Optional datetime when the memory should expire

        Returns:
            None
        """
        try:
            # Await the async set_memory call
            await self.db_service.set_memory(
                agent_role=self.agent_role,
                user_profile_id=self.user_profile_id,
                memory_key=memory_key,
                content=content,
                confidence=confidence,
                expires_at=expires_at # Pass expires_at
            )
        except Exception as e:
            logger.warning(f"Error storing memory '{memory_key}': {str(e)}")

    async def _call_tool(self, tool_code: str, tool_input: Dict[str, Any]) -> Dict[str, Any]:
        """Executes a tool using the centralized utility."""
        # Import tool utility here
        from apps.main.agents.tools.tools_util import execute_tool, ToolExecutionError
        logger.debug(f"EngagementAgent attempting to call tool '{tool_code}'")
        try:
            # Assuming execute_tool handles async execution and error logging
            result = await execute_tool(
                tool_code=tool_code,
                tool_input=tool_input,
                run_id=getattr(self, 'run_id', None), # Pass run_id if available
                user_profile_id=self.user_profile_id # Pass user_id for context/events
            )
            logger.debug(f"Tool '{tool_code}' executed successfully by EngagementAgent.")
            return result
        except ToolExecutionError as e:
            logger.error(f"ToolExecutionError in EngagementAgent calling '{tool_code}': {e}", exc_info=True)
            # Re-raise or return an error structure
            raise # Re-raise to be handled by the main process loop
        except Exception as e:
            logger.error(f"Unexpected error in EngagementAgent calling tool '{tool_code}': {e}", exc_info=True)
            # Wrap in ToolExecutionError or handle appropriately
            raise ToolExecutionError(f"Unexpected error calling tool {tool_code}: {e}") from e

    async def process(self, state: BaseModel) -> Dict[str, Any]: # Return type changed
        """Analyze user engagement patterns and preferences."""
        current_operation = "ensuring_agent_loaded"
        try:
            self.start_stage('engagement_ensure_loaded')
            await self._ensure_loaded()
            self.stop_stage('engagement_ensure_loaded')
            if self.agent_definition is None:
                 raise RuntimeError(f"Agent definition for role '{self.agent_role}' could not be loaded.")
        except Exception as load_error:
            self.stop_stage('engagement_ensure_loaded') # Stop stage even on error if started
            error_message = f"Critical error: Failed to load agent configuration during '{current_operation}': {str(load_error)}"
            logger.error(error_message, exc_info=True)
            error_output_data = {"error": error_message, "debug": {"last_error": error_message, "failed_operation": current_operation}}
            # Match the structure returned by the main exception handler
            error_updates = {
                "error": error_message,
                "output_data": error_output_data
                # run_id is not available at this stage
            }
            return error_updates

        current_operation = "extracting_state"
        # Extract relevant data from state
        context_packet = getattr(state, "context_packet", {})
        resource_context = getattr(state, "resource_context", {})
        workflow_id = getattr(state, "workflow_id", None)

        # Convert user_profile_id to int for DB calls if it's not a test or benchmark ID
        try:
            # Check if this is a test ID (starts with 'test-') or benchmark ID (starts with 'benchmark-user-')
            if isinstance(self.user_profile_id, str) and (self.user_profile_id.startswith('test-') or self.user_profile_id.startswith('benchmark-user-')):
                # For test and benchmark IDs, use a default integer ID
                user_profile_id_int = 1  # Use a default test/benchmark ID
                logger.debug(f"Using default user_profile_id (1) for test/benchmark ID: {self.user_profile_id}")
            else:
                # For real IDs, convert to int
                user_profile_id_int = int(self.user_profile_id)
        except ValueError:
            error_message = f"Invalid user_profile_id format: {self.user_profile_id}"
            logger.error(f"{error_message}. Cannot convert to int for DB.")
            error_output_data = {"error": error_message, "debug": {"last_error": error_message}}
            return {"error": error_message, "output_data": error_output_data}

        current_operation = "starting_run"
        # Start profiling DB start
        self.start_stage('engagement_db_start_run')
        # Await the async start_run call
        run = await self.db_service.start_run(
            agent_definition=self.agent_definition, # Use loaded definition
            user_profile_id=user_profile_id_int, # Use int ID
            input_data={
                "context_packet": context_packet,
                "resource_context": resource_context
            },
            state={"workflow_id": workflow_id}
        )
        self.run_id = run.id if hasattr(run, 'id') else str(run.get('id', 'mock-run-id'))
        self.stop_stage('engagement_db_start_run')

        try:
            current_operation = "getting_memory"
            # Start profiling get memory
            self.start_stage('engagement_get_memory')
            # Await the async _get_memory call
            domain_engagement = await self._get_memory('domain_engagement_metrics') or {
                'domains': {},
                'temporal_patterns': {},
                'completion_trends': [],
                'confidence_scores': {}
            }
            self.stop_stage('engagement_get_memory')

            current_operation = "analyzing_engagement"
            # Analyze historical engagement with activities
            self.start_stage('engagement_analyze_domain_prefs')
            domain_preferences = await self._analyze_domain_preferences()
            self.stop_stage('engagement_analyze_domain_prefs')

            # Analyze completion and abandonment patterns
            self.start_stage('engagement_analyze_completion')
            completion_patterns = await self._analyze_completion_patterns()
            self.stop_stage('engagement_analyze_completion')

            # Analyze time-of-day preferences
            self.start_stage('engagement_analyze_temporal')
            temporal_patterns = await self._analyze_temporal_patterns(context_packet)
            self.stop_stage('engagement_analyze_temporal')

            # Analyze preference consistency
            self.start_stage('engagement_analyze_consistency')
            preference_consistency = await self._analyze_preference_consistency()
            self.stop_stage('engagement_analyze_consistency')

            # Analyze feedback sentiment
            self.start_stage('engagement_analyze_sentiment')
            sentiment_analysis = await self._analyze_feedback_sentiment()
            self.stop_stage('engagement_analyze_sentiment')

            current_operation = "calculating_recommendations"
            # Recommend domain distribution for wheel
            self.start_stage('engagement_calc_distribution')
            domain_distribution = self._calculate_domain_distribution(domain_engagement, domain_preferences) # Pass preferences
            self.stop_stage('engagement_calc_distribution')

            # Determine optimal timing
            self.start_stage('engagement_determine_timing')
            optimal_timing = self._determine_optimal_timing(temporal_patterns)
            self.stop_stage('engagement_determine_timing')

            # Identify focus areas
            self.start_stage('engagement_identify_focus')
            focus_areas = self._identify_focus_areas({
                'domain_preferences': domain_preferences,
                'completion_patterns': completion_patterns,
                'preference_consistency': preference_consistency
            })
            self.stop_stage('engagement_identify_focus')

            current_operation = "preparing_output"
            # Combine analyses into engagement analysis
            self.start_stage('engagement_prepare_output')
            engagement_analysis = {
                "domain_preferences": domain_preferences,
                "completion_patterns": completion_patterns,
                "temporal_patterns": temporal_patterns,
                "preference_consistency": preference_consistency,
                "sentiment_trends": sentiment_analysis.get('trends', {}),
                "analysis_timestamp": context_packet.get("session_timestamp", timezone.now().isoformat()), # Use current time if missing
                "confidence_metrics": domain_engagement.get('confidence_scores', {}), # Keep this if still relevant
                "user_id": self.user_profile_id,
                "recommendations": {
                    "domain_distribution": domain_distribution,
                    "optimal_timing": optimal_timing,
                    "focus_areas": focus_areas
                }
            }
            self.stop_stage('engagement_prepare_output')

            # Prepare memory updates (using the async helper)
            # Update the domain_engagement dict before saving
            # Example: Update confidence scores based on new analysis
            self.start_stage('engagement_set_memory')
            if 'confidence' in domain_preferences:
                 domain_engagement['confidence_scores']['overall_preference'] = domain_preferences['confidence']
            if 'confidence' in completion_patterns:
                 domain_engagement['confidence_scores']['completion'] = completion_patterns['confidence']

            # --- Debugging: Check domain_engagement before setting memory ---
            if domain_engagement is None:
                logger.error("Critical Error: domain_engagement is None before calling _set_memory!")
                # Optionally raise an error or handle appropriately
                # For now, let's log and try to proceed with an empty dict to avoid immediate crash
                domain_engagement = {} # Assign empty dict to prevent TypeError in _set_memory if it expects iterable
            else:
                logger.debug(f"Type of domain_engagement before _set_memory: {type(domain_engagement)}")
            # --- End Debugging ---

            await self._set_memory('domain_engagement_metrics', domain_engagement)
            self.stop_stage('engagement_set_memory')

            # Output data including engagement analysis and routing
            output_data = {
                "engagement_analysis": engagement_analysis,
                "next_agent": "psychological" # Assuming this routing logic is correct
            }

            # Prepare state updates dictionary
            state_updates = {
                "output_data": output_data,
                "run_id": self.run_id # Include run_id
                # Add other necessary state updates here if needed
            }

            current_operation = "completing_run_success"
            # Start profiling DB complete (success)
            self.start_stage('engagement_db_complete_run')
            # Await the async complete_run call
            # Pass memory_updates=None as we handled it separately with _set_memory
            await self.db_service.complete_run(
                run_id=self.run_id,
                output_data=output_data,
                state={"workflow_id": workflow_id},
                memory_updates={}, # Pass empty dict instead of None
                status='completed'
            )
            self.stop_stage('engagement_db_complete_run')

            logger.debug(f"EngagementAgent process completed successfully. Returning state updates.")
            return state_updates

        except Exception as e:
            error_type = type(e).__name__
            error_message = f"Error in engagement agent during '{current_operation}': {str(e)}"
            logger.error(f"Exception caught in EngagementAgent process ({error_type} during {current_operation}): {error_message}", exc_info=True)

            # Attempt to complete the run with status 'failed'
            try:
                current_operation = "completing_run_failure"
                # Start profiling DB complete (error)
                self.start_stage('engagement_db_complete_run_error')
                # Await the async complete_run call
                await self.db_service.complete_run(
                    run_id=self.run_id,
                    output_data={"error": error_message},
                    state={"workflow_id": workflow_id, "error_details": error_message},
                    status='failed',
                    error_message=error_message # Pass explicit error message
                )
                self.stop_stage('engagement_db_complete_run_error')
            except Exception as db_error:
                 self.stop_stage('engagement_db_complete_run_error') # Stop stage even if DB fails
                 logger.error(f"Failed to complete run in DB after agent error: {db_error}", exc_info=True)

            # Return error state
            error_output_data = {"error": error_message, "debug": {"last_error": error_message, "failed_operation": current_operation}}
            error_updates = {
                "error": error_message,
                "output_data": error_output_data
            }
            if hasattr(self, 'run_id') and self.run_id:
                error_updates["run_id"] = self.run_id
            return error_updates

    async def _analyze_domain_preferences(self):
        """Analyze user's historical domain preferences using a tool."""
        try:
            domain_data = await self._call_tool(
                tool_code="get_domain_preferences", # Use tool_code parameter
                tool_input={ # Use tool_input parameter
                    "user_profile_id": self.user_profile_id,
                    "time_period_days": 30
                }
            )
            # Basic validation or default values
            return {
                "preferred_domains": domain_data.get("preferred_domains", {}),
                "avoided_domains": domain_data.get("avoided_domains", {}),
                "trending_domains": domain_data.get("trending_domains", {}),
                "confidence": domain_data.get("confidence", 0.5) # Default confidence if missing
            }
        except Exception as e:
            logger.warning(f"Tool 'get_domain_preferences' failed: {e}. Returning default preferences.", exc_info=True)
            # Return structured default data
            return {
                "preferred_domains": {"creative": 0.8, "intellectual": 0.6},
                "avoided_domains": {"social": 0.3},
                "trending_domains": {"physical": 0.1},
                "confidence": 0.1 # Low confidence for default
            }

    async def _analyze_completion_patterns(self):
        """Analyze user's activity completion patterns using a tool."""
        try:
            completion_data = await self._call_tool(
                tool_code="get_completion_patterns", # Use tool_code parameter
                tool_input={ # Use tool_input parameter
                    "user_profile_id": self.user_profile_id,
                    "time_period_days": 30
                }
            )
            # Basic validation or default values
            return {
                "completion_rate": completion_data.get("completion_rate", 0.0),
                "domain_completion_rates": completion_data.get("domain_completion_rates", {}),
                "abandonment_factors": completion_data.get("abandonment_factors", []),
                "success_factors": completion_data.get("success_factors", []),
                "confidence": completion_data.get("confidence", 0.5) # Default confidence
            }
        except Exception as e:
            logger.warning(f"Tool 'get_completion_patterns' failed: {e}. Returning default patterns.", exc_info=True)
            # Return structured default data
            return {
                "completion_rate": 0.7, # Example default
                "domain_completion_rates": {"creative": 0.85, "intellectual": 0.75},
                "abandonment_factors": ["time_constraints"],
                "success_factors": ["clear_instructions"],
                "confidence": 0.1 # Low confidence
            }

    async def _analyze_temporal_patterns(self, context_packet):
        """Analyze user's temporal engagement patterns using a tool."""
        try:
            temporal_data = await self._call_tool(
                tool_code="get_temporal_patterns", # Use tool_code parameter
                tool_input={ # Use tool_input parameter
                    "user_profile_id": self.user_profile_id,
                    "current_time_context": context_packet.get("session_timestamp", timezone.now().isoformat()) # Use current time if missing
                }
            )
            # Basic validation or default values
            # Note: Logic merging with memory removed for simplicity, assuming tool provides comprehensive data
            return {
                'preferred_times': temporal_data.get('preferred_times', {}),
                'optimal_window': temporal_data.get('optimal_window', 'any'), # Default to 'any'
                'day_preferences': temporal_data.get('day_preferences', {})
            }
        except Exception as e:
            logger.warning(f"Tool 'get_temporal_patterns' failed: {e}. Returning default patterns.", exc_info=True)
            # Return structured default data
            return {
                "preferred_times": {"morning": 25, "afternoon": 25, "evening": 25, "night": 25},
                "optimal_window": "any",
                "day_preferences": {"weekday": 50, "weekend": 50}
            }

    async def _analyze_preference_consistency(self):
        """Analyze consistency between stated preferences and actual behaviors using a tool."""
        try:
            consistency_data = await self._call_tool(
                tool_code="get_preference_consistency", # Use tool_code parameter
                tool_input={ # Use tool_input parameter
                    "user_profile_id": self.user_profile_id
                }
            )
            # Basic validation or default values
            return consistency_data.get('consistency_analysis', {}) # Return empty dict if missing
        except Exception as e:
            logger.warning(f"Tool 'get_preference_consistency' failed: {e}. Returning default consistency.", exc_info=True)
            # Return structured default data
            return {
                "creative": {"consistency": "unknown"},
                "social": {"consistency": "unknown"}
            }

    async def _analyze_feedback_sentiment(self):
        """Analyze sentiment in user feedback using a tool."""
        try:
            sentiment_data = await self._call_tool(
                tool_code="get_feedback_sentiment", # Use tool_code parameter
                tool_input={ # Use tool_input parameter
                    "user_profile_id": self.user_profile_id,
                    "time_period_days": 30
                }
            )
            # Basic validation or default values
            return {
                "trends": sentiment_data.get("domain_sentiment", {}), # Default to empty dict
                "confidence": sentiment_data.get("confidence", 0.5) # Default confidence
            }
        except Exception as e:
            logger.warning(f"Tool 'get_feedback_sentiment' failed: {e}. Returning default sentiment.", exc_info=True)
            # Return structured default data
            return {
                "trends": {"general": "neutral"},
                "confidence": 0.1 # Low confidence
            }

    # Removed _update_domain_engagement as its logic seems integrated or redundant
    # async def _update_domain_engagement(self, domain_engagement): ...

    def _format_domain_preferences(self, domain_engagement):
        """Format domain preferences in a structured way"""
        # This method seems unused now, consider removing or refactoring if needed later
        preferences = {}
        for domain, metrics in domain_engagement.get('domains', {}).items():
            completion_rate = metrics.get('completion_rate', 0)
            total_activities = metrics.get('total_activities', 0)
            if total_activities > 0:
                strength = min(100, max(0, int(completion_rate * 100)))
                preferences[domain] = {
                    'strength': strength,
                    'evidence': {'completion_rate': completion_rate, 'activity_count': total_activities},
                    'confidence': domain_engagement.get('confidence_scores', {}).get(domain, 0)
                }
        return preferences

    def _calculate_domain_distribution(self, domain_engagement, domain_preferences): # Added domain_preferences
        """Calculate recommended domain distribution for wheel"""
        distribution = {}
        # Use the analyzed domain_preferences which includes confidence
        preferred_domains = domain_preferences.get('preferred_domains', {})
        confidence = domain_preferences.get('confidence', 0.5)

        if preferred_domains:
             # Simple distribution based on preference scores, weighted by confidence
             total_score = sum(preferred_domains.values())
             if total_score > 0:
                  distribution = {
                      domain: (score / total_score) * 100 * confidence
                      for domain, score in preferred_domains.items()
                  }
             # Normalize and ensure minimums if needed (complex logic omitted for brevity)
             # Placeholder normalization:
             current_total = sum(distribution.values())
             if current_total > 0:
                  distribution = {d: (v / current_total) * 100 for d, v in distribution.items()}
             else: # Fallback if scores were zero or missing
                  num_domains = len(preferred_domains) or 4 # Default to 4 if empty
                  distribution = {d: 100 / num_domains for d in preferred_domains or ["creative", "intellectual", "physical", "social"]}

        else:
            # Default distribution if no preference data
            distribution = {"creative": 25, "intellectual": 25, "physical": 25, "social": 25}

        return distribution

    def _determine_optimal_timing(self, temporal_patterns):
        """Determine optimal timing for activities"""
        optimal_window = temporal_patterns.get('optimal_window', 'any') # Default to 'any'
        confidence = 0.5 # Default confidence, could be refined based on pattern strength

        if optimal_window != 'any':
            # Map time block to hour ranges
            hour_ranges = {'morning': '6am-12pm', 'afternoon': '12pm-5pm', 'evening': '5pm-10pm', 'night': '10pm-6am'}
            # Example: Increase confidence if preferred_times score for this window is high
            preferred_score = temporal_patterns.get('preferred_times', {}).get(optimal_window, 0)
            if isinstance(preferred_score, (int, float)) and preferred_score > 50: # Example threshold
                 confidence = 0.8

            return {
                'primary_window': optimal_window,
                'hour_range': hour_ranges.get(optimal_window, 'any time'),
                'confidence': confidence
            }

        return {'primary_window': 'any time', 'confidence': 0.3} # Low confidence for default

    def _identify_focus_areas(self, engagement_profile):
        """Identify focus areas based on engagement analysis"""
        focus_areas = []
        # Use the results from the analysis methods directly, ensuring fallbacks handle None values
        domain_preferences = engagement_profile.get('domain_preferences') or {}
        completion_patterns = engagement_profile.get('completion_patterns') or {}
        preference_consistency = engagement_profile.get('preference_consistency') or {}

        # Example: Low engagement based on preference scores
        # Use .get() for confidence, but ensure the dicts themselves are not None before unpacking/iterating
        pref_confidence = domain_preferences.get('confidence', 0.1)
        if pref_confidence > 0.5: # Only act if preference confidence is reasonable
            preferred_domains_dict = domain_preferences.get('preferred_domains') # Get potentially None value
            avoided_domains_dict = domain_preferences.get('avoided_domains') # Get potentially None value
            # Explicitly handle None before unpacking using **
            all_prefs = {**(preferred_domains_dict or {}), **(avoided_domains_dict or {})}
            for domain, score in all_prefs.items(): # Iteration over the merged dict is safe
                 if isinstance(score, (int, float)) and score < 0.4: # Low preference/high avoidance
                      focus_areas.append({'domain': domain, 'reason': 'low_preference_score', 'priority': 'medium'})

        # Example: Preference inconsistency
        # Ensure preference_consistency is a dict before iterating
        if isinstance(preference_consistency, dict):
            for domain, data in preference_consistency.items():
                if isinstance(data, dict) and data.get('consistency') == 'low':
                    focus_areas.append({'domain': domain, 'reason': 'preference_inconsistency', 'priority': 'high'})

        # Example: Low completion rate in a domain
        comp_confidence = completion_patterns.get('confidence', 0.1)
        # Ensure domain_rates is a dict before iterating
        domain_rates = completion_patterns.get('domain_completion_rates') # Get potentially None value
        if comp_confidence > 0.5 and isinstance(domain_rates, dict): # Explicitly check if it's a dict
             valid_rates = {d: r for d, r in domain_rates.items() if isinstance(r, (int, float))}
             if valid_rates:
                  avg_rate = sum(valid_rates.values()) / len(valid_rates) if len(valid_rates) > 0 else 0
                  for domain, rate in valid_rates.items():
                       if rate < avg_rate * 0.7: # Significantly below average
                            focus_areas.append({'domain': domain, 'reason': 'low_completion_rate', 'priority': 'medium'})

        return focus_areas
