# apps/main/agents/mentor_agent.py

from typing import Dict, Any, List, Tuple, Optional, TYPE_CHECKING
from pydantic import BaseModel

from apps.main.agents.base_agent import LangGraphAgent
# Import from the new state_models file
from apps.main.graphs.state_models import WorkflowTransitionRequest, DiscussionState
from apps.main.services.database_service import RealDatabaseService
from apps.main.llm.service import RealLLMClient
# Import LLMConfig for type hinting only during type checking
if TYPE_CHECKING:
    from apps.main.models import LLMConfig
from apps.main.llm.response import LLMResponse, ResponseType
import json
import logging
from django.utils import timezone
import pprint # For pretty printing dicts/objects in logs
from asgiref.sync import sync_to_async # Import sync_to_async

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
class MentorAgent(LangGraphAgent):
    """
    Mentor agent that handles user interaction and communication.
     Enhanced with workflow transition capabilities to detect when a user
     request would be better handled by a different workflow.
     """

    def __init__(self,
                 user_profile_id: str,
                 db_service=None,
                 llm_client=None,
                 llm_config: Optional['LLMConfig'] = None): # Use string literal for type hint
        # Pass LLM config up to the base class constructor
        super().__init__(
            user_profile_id=user_profile_id,
            agent_role="mentor",
            llm_config=llm_config # Pass the LLMConfig object
        )

        # Use provided dependencies or create defaults
        self.db_service = db_service or RealDatabaseService()

        # Instantiate LLM client using config from base class if not provided externally
        if llm_client:
            self.llm_client = llm_client
        else:
            # RealLLMClient now requires an LLMConfig object
            if not self.llm_config:
                logger.debug(f"Instantiating RealLLMClient for MentorAgent without LLMConfig: will use default")

            logger.debug(f"Instantiating RealLLMClient for MentorAgent with LLMConfig: {self.llm_config.name if self.llm_config else 'None'}")
            self.llm_client = RealLLMClient(llm_config=self.llm_config) # Pass the LLMConfig object


        # Initialize definition and tools to None. They will be loaded lazily.
        self.agent_definition = None
        self.available_tools = []

    async def _ensure_loaded(self):
        """Ensures agent definition and tools are loaded asynchronously."""
        if self.agent_definition is not None:
            # Already loaded
            return True

        logger.debug(f"Loading definition and tools for {self.agent_role}...")
        try:
            # Wrap synchronous DB call for definition
            load_def_sync = sync_to_async(self.db_service.load_agent_definition, thread_sensitive=True)

            self.agent_definition = await load_def_sync(self.agent_role)
            if self.agent_definition:
                # Call the async load_tools method directly
                self.available_tools = await self.db_service.load_tools(self.agent_definition)
                logger.debug(f"Successfully loaded definition and {len(self.available_tools)} tools for {self.agent_role}")
                return True
            else:
                logger.error(f"Agent definition not found for role: {self.agent_role}")
                self.agent_definition = None # Ensure it's None if not found
                self.available_tools = []
                return False

        except Exception as e:
            logger.error(f"Failed to load definition/tools for {self.agent_role}: {e}", exc_info=True)
            self.agent_definition = None
            self.available_tools = []
            # Don't re-raise - let the process method handle the None definition
            return False

    async def process(self, state: BaseModel) -> Dict[str, Any]:
        """
        Process the input state and generate mentor agent output.
        Enhanced with workflow transition detection.

        Args:
            state: The current workflow state

        Returns:
            Dict[str, Any]: State updates dictionary, as expected by LangGraph
        """
        logger.debug(f"MentorAgent process started. Input state:\n{pprint.pformat(state)}")

        # --- Ensure Agent Definition and Tools are Loaded ---
        current_operation = "ensuring_agent_loaded"
        try:
            self.start_stage("mentor_ensure_loaded") # Profile loading
            await self._ensure_loaded()
            self.stop_stage("mentor_ensure_loaded") # Stop profiling loading
            # Check if loading failed (e.g., definition not found)
            if self.agent_definition is None:
                 raise RuntimeError(f"Agent definition for role '{self.agent_role}' could not be loaded.")
        except Exception as load_error:
            self.stop_stage("mentor_ensure_loaded") # Ensure stop is called on error
            error_message = f"Critical error: Failed to load agent configuration during '{current_operation}': {str(load_error)}"
            logger.error(error_message, exc_info=True)
            # Return error state immediately with standardized structure
            error_output_data = {
                "error": error_message,
                "debug": {
                    "last_error": error_message,
                    "failed_operation": current_operation,
                    "exception_type": type(load_error).__name__
                },
                "user_response": "I'm sorry, but I'm having trouble processing your request right now.",
                "context_packet": {},
                "next_agent": "mentor"  # Keep the agent active or adjust as needed
            }
            return {
                "error": error_message,
                "output_data": error_output_data
            }
        # --- End Loading Check ---

        current_operation = "extracting_state"
        # Extract relevant data from state
        input_data = getattr(state, "context_packet", {})
        workflow_id = getattr(state, "workflow_id", None)
        current_stage = getattr(state, "current_stage", "initial_conversation")
        conversation_history = getattr(state, "conversation_history", [])
        logger.debug(f"Extracted initial variables: workflow_id={workflow_id}, current_stage={current_stage}, input_data keys={list(input_data.keys())}, history_len={len(conversation_history)}") # DEBUG LOG

        # Add text from initial context packet if available for first conversation
        if not hasattr(state, "conversation_history") or not state.conversation_history:
            if hasattr(state, "initial_context_packet") and state.initial_context_packet:
                input_data["text"] = state.initial_context_packet.get("text", "")

        # Start a run in the database
        # Convert string user_profile_id to int ONLY for the database call if it's not a test or benchmark ID
        try:
            # Check if this is a test ID (starts with 'test-') or benchmark ID (starts with 'benchmark-user-')
            if isinstance(self.user_profile_id, str) and (self.user_profile_id.startswith('test-') or self.user_profile_id.startswith('benchmark-user-')):
                # For test and benchmark IDs, use a default integer ID
                user_profile_id_int = 1  # Use a default test/benchmark ID
                logger.debug(f"Using default user_profile_id (1) for test/benchmark ID: {self.user_profile_id}")
            else:
                # For real IDs, convert to int
                user_profile_id_int = int(self.user_profile_id)
        except ValueError:
            # Handle cases where the ID might not be a valid integer string
            error_message = f"Invalid user_profile_id format: {self.user_profile_id}"
            logger.error(f"{error_message}. Cannot convert to int for DB.")
            # Return consistent error structure including output_data
            error_output_data = {"error": error_message, "debug": {"last_error": error_message}}
            return {
                "error": error_message,
                "output_data": error_output_data
                # No run_id available here yet
            }

        current_operation = "starting_run"
        # Call the now-async start_run directly
        self.start_stage("mentor_db_start_run") # Profile DB start
        run = await self.db_service.start_run(
            agent_definition=self.agent_definition, # Now guaranteed to be loaded
            user_profile_id=user_profile_id_int, # Pass the integer ID to the DB service
            input_data=input_data,
            state={"workflow_id": workflow_id} # Pass as keyword argument 'state'
        )
        self.stop_stage("mentor_db_start_run") # Stop profiling DB start
        # Store the AgentRun's UUID (not the user profile ID)
        self.run_id = run.id if hasattr(run, 'id') else str(run.get('id', 'mock-run-id'))
        current_operation = "starting_process" # For logging

        try:
            current_operation = "accessing_context_packet"
            # Access context from the state directly
            # Assuming context_packet is passed in the initial state by the dispatcher/graph entry
            context_packet = getattr(state, "context_packet", {})
            if not context_packet:
                 # Attempt to get it from initial_context_packet if it's the very first run
                 context_packet = getattr(state, "initial_context_packet", {})
                 logger.warning("Using initial_context_packet as context_packet was empty.") # Add warning if fallback is used

            # Ensure text is included in the context_packet if not already present
            if "text" not in context_packet and input_data.get("text"):
                context_packet["text"] = input_data.get("text")

            logger.debug(f"Using context packet from state:\n{pprint.pformat(context_packet)}") # DEBUG LOG

            current_operation = "detecting_workflow_transition"
            # Check for workflow transition requests using the context from state
            # Ensure 'text' is available in the context_packet for transition detection
            message_text_for_transition = context_packet.get("text", input_data.get("text", "")) # Get text from context or input_data
            self.start_stage("mentor_detect_transition") # Profile transition detection
            transition_request = await self._detect_workflow_transition(
                message_text_for_transition, # Use extracted text
                context_packet, # Pass the context packet from state
                current_stage,
                conversation_history
            )
            self.stop_stage("mentor_detect_transition") # Stop profiling transition detection
            logger.debug(f"Detected transition request: {pprint.pformat(transition_request)}") # DEBUG LOG

            # Initialize state updates dictionary
            state_updates = {}

            # If transition detected, prepare transition response
            if transition_request:
                # Format a transition explanation for the user
                user_response = await self._format_transition_response(transition_request)

                # Store in output_data
                output_data = {
                    "user_response": user_response,
                    "context_packet": context_packet,
                    "transition_request": transition_request,
                    "next_agent": "end"  # Signal to end this workflow
                }

                # Create a WorkflowTransitionRequest instance
                try:
                    transition_model = WorkflowTransitionRequest(**transition_request)
                    state_updates["transition_request"] = transition_model
                except Exception as pydantic_error:
                    logger.error(f"Failed to create WorkflowTransitionRequest model: {pydantic_error}")
                    # Handle error - maybe return an error state or proceed without transition
                    state_updates["error"] = "Failed to process transition request."
                    # Fall through to normal completion might be problematic, consider returning error state directly
                    # For now, let's add the raw dict to output_data for debugging if needed
                    output_data["raw_transition_request"] = transition_request


                state_updates["output_data"] = output_data

                # Update conversation history if available
                if hasattr(state, "conversation_history"):
                    new_history = conversation_history + [{
                        "role": "assistant",
                        "content": user_response,
                        "timestamp": timezone.now() # Use timezone.now()
                    }]
                state_updates["conversation_history"] = new_history

                # Call the now-async complete_run directly
                await self.db_service.complete_run(
                    run_id=self.run_id,
                    output_data=output_data,
                    state={"workflow_id": workflow_id, "transition_requested": True},
                    status='completed'
                )

                # Add run_id to state updates before returning
                state_updates["run_id"] = self.run_id # Corrected indentation
                return state_updates

            # --- Start: Refactored LLM call and Response Handling ---
            current_operation = "getting_llm_response"
            # Add logging before the call
            logger.debug(f"Calling _get_llm_response with context from state:\n{pprint.pformat(context_packet)}") # DEBUG LOG (Updated formatting)
            # Get the LLM response (potentially including tool calls) using context from state
            self.start_stage("mentor_llm_call") # Profile LLM call
            llm_response = await self._get_llm_response(context_packet, current_stage, conversation_history) # Should be the primary LLM call now
            self.stop_stage("mentor_llm_call") # Stop profiling LLM call
            # Add logging after the call to inspect the response
            logger.debug(f"Received LLMResponse:\n{pprint.pformat(llm_response)}") # DEBUG LOG (Updated formatting)

            current_operation = "processing_llm_response"
            # Determine the user_response based on LLM response type
            if hasattr(llm_response, 'response_type') and llm_response.response_type == ResponseType.TOOL_CALL and llm_response.tool_calls:
                tool_results = []
                current_operation = "calling_tools"
                for tool_call in llm_response.tool_calls:
                    tool_stage_name = f"mentor_tool_{tool_call.tool_name}"
                    logger.debug(f"Calling tool: {tool_call.tool_name} with input: {tool_call.tool_input}")
                    self.start_stage(tool_stage_name) # Profile tool call
                    # AgentTestRunner patches _call_tool to use MockToolRegistry
                    tool_result = await self._call_tool(tool_call.tool_name, tool_call.tool_input)
                    self.stop_stage(tool_stage_name) # Stop profiling tool call
                    logger.debug(f"Tool {tool_call.tool_name} result: {tool_result}")
                    tool_results.append(tool_result)
                current_operation = "formatting_tool_response"
                # Simple response incorporating the first tool's result for the test
                first_result_str = json.dumps(tool_results[0]) if tool_results else 'No result'
                user_response = f"Tool executed. Result: {first_result_str}" # Response based on tool result

            elif hasattr(llm_response, 'response_type') and llm_response.response_type == ResponseType.TEXT:
                user_response = llm_response.content if llm_response.content else "I'm not sure how to respond to that." # Response based on text content
            else: # Fallback
                user_response = llm_response.content if hasattr(llm_response, 'content') and llm_response.content else "I'm processing that request."
            logger.debug(f"Determined final user_response: '{user_response}'") # DEBUG LOG
            # --- End: Refactored LLM call and Response Handling ---

            # Output data for next agents in workflow
            output_data = {
                "user_response": user_response,
                "context_packet": context_packet,
                "next_agent": "orchestrator" if current_stage == "initial_conversation" else "mentor"
            }

            # Determine the next stage based on the current stage
            if current_stage == "initial_conversation":
                next_stage = "context_deepening"
            elif current_stage == "context_deepening":
                next_stage = "guidance_preparation"
            else: # Includes guidance_preparation and any unexpected stages
                next_stage = "workflow_complete" # Signal completion

            # Add output_data, next_stage, and run_id to state updates
            state_updates["output_data"] = output_data
            state_updates["current_stage"] = next_stage # Explicitly update the stage
            state_updates["run_id"] = self.run_id # Add the run_id

            # Add token counts from the LLM response to state updates
            # Accumulate tokens if there were previous LLM calls in this run
            current_input_tokens = 0
            current_output_tokens = 0
            if hasattr(llm_response, 'input_tokens') and llm_response.input_tokens is not None:
                current_input_tokens = llm_response.input_tokens
            if hasattr(llm_response, 'output_tokens') and llm_response.output_tokens is not None:
                current_output_tokens = llm_response.output_tokens

            # Accumulate with any existing tokens from previous calls in this run
            existing_input = state.get("llm_input_tokens", 0) if hasattr(state, "get") else 0
            existing_output = state.get("llm_output_tokens", 0) if hasattr(state, "get") else 0

            state_updates["llm_input_tokens"] = existing_input + current_input_tokens
            state_updates["llm_output_tokens"] = existing_output + current_output_tokens

            logger.debug(f"Token tracking: Current call ({current_input_tokens} in, {current_output_tokens} out), "
                        f"Total accumulated ({state_updates['llm_input_tokens']} in, {state_updates['llm_output_tokens']} out)")

            # --- REMOVED: Unnecessary trigger_end addition ---
            # The graph routing handles the end of the turn based on
            # whether state_updates["transition_request"] is None or a model instance.
            # if not transition_request: # Check if we are NOT in the transition block
            #      state_updates["transition_request"] = {"trigger_end": True}
            # --- End of removal ---


            # Update conversation history if available
            # Ensure history is updated even if transition fails but processing continues
            if hasattr(state, "conversation_history"):
                    new_history = conversation_history + [{
                        "role": "assistant",
                        "content": user_response,
                        "timestamp": timezone.now() # Use timezone.now()
                    }]
                    state_updates["conversation_history"] = new_history

            current_operation = "completing_run_success"
            # Call the now-async complete_run directly
            self.start_stage("mentor_db_complete_run") # Profile DB complete
            await self.db_service.complete_run(
                run_id=self.run_id,
                output_data=output_data,
                state={"workflow_id": workflow_id},
                status='completed'
            )
            self.stop_stage("mentor_db_complete_run") # Stop profiling DB complete

            # Return the state updates dictionary
            logger.debug(f"Returning state updates:\n{pprint.pformat(state_updates)}") # DEBUG LOG
            return state_updates

        except Exception as e:
            # --- Improved Error Logging ---
            error_type = type(e).__name__
            error_message = f"Error in mentor agent during '{current_operation}': {str(e)}"
            # Log the error with type and location
            logger.error(f"Exception caught in MentorAgent process ({error_type} during {current_operation}): {error_message}", exc_info=True)
            # --- End Improved Error Logging ---

            # Complete the run with status 'failed', passing the current state
            # Remove the unexpected 'error_message' argument
            # Pass simpler metadata instead of the full state object
            try:
                # Call the now-async complete_run directly
                await self.db_service.complete_run(
                    run_id=self.run_id,
                    output_data={"error": error_message}, # Pass error in output field
                    state={"error_details": error_message}, # Pass simple metadata for the 'state' arg
                    status='failed',
                    # Pass the improved error message if the arg exists, otherwise rely on output_data
                    error_message=error_message # Pass improved error message explicitly if arg exists
                )
            except Exception as db_error:
                 logger.error(f"Failed to complete run in DB after agent error: {db_error}", exc_info=True)

            # Return error in state updates format, ensuring output_data is present
            # Include the operation stage in the debug info
            error_output_data = {
                "error": error_message,
                "debug": {"last_error": error_message, "failed_operation": current_operation},
                "user_response": error_message,
                "context_packet": {},
                "next_agent": "mentor"  # Keep the agent active or adjust as needed
            } # Include error and stage in output_data
            error_updates = {
                "error": error_message, # Keep the improved error message here too
                "output_data": error_output_data # Add output_data key
            }
            if hasattr(self, 'run_id') and self.run_id:
                error_updates["run_id"] = self.run_id
                # Optionally add run_id to output_data as well if needed by downstream
                # error_output_data["run_id"] = self.run_id
            return error_updates


    async def _gather_user_context(self, input_data):
        """Gather context from user input"""
        # Call tool to extract context
        try:
            context_result = await self._call_tool(
                "extract_message_context",
                {
                    "message": input_data.get("text", ""),
                    "user_profile_id": self.user_profile_id,
                    "extraction_level": "comprehensive"
                }
            )

            # Get the extracted context
            context = context_result.get("extracted_context", {})

             # Format into a context packet
            context_packet = {
                 "user_id": self.user_profile_id,
                 "text": input_data.get("text", ""), # Add original text back
                 "mood": context.get("mood", ""),
                 "environment": context.get("environment", ""),
                 "time_availability": context.get("time_availability", ""),
                 "focus": context.get("focus", ""),
                 "workflow_type": input_data.get("workflow_type", "None")
             }

            return context_packet

        except Exception as e:
            # If tool fails, return minimal context
            logger.error(f"Error gathering user context: {str(e)}")
            # Log the error but return an empty context packet to allow processing to continue if possible,
            # or re-raise if context is critical. For now, log and return empty.
            logger.error(f"Error gathering user context via tool: {str(e)}", exc_info=True)
            # Returning empty context here as the method is being removed.
            # If this method were kept, it should probably raise an error.
            # Log the error but return an empty context packet to allow processing to continue if possible,
            # or re-raise if context is critical. For now, log and return empty.
            logger.error(f"Error gathering user context via tool: {str(e)}", exc_info=True)
            # Returning empty context here as the method is being removed.
            # If this method were kept, it should probably raise an error.
            return {} # Return empty dict as this method is being removed

    # Removed _gather_user_context method entirely as it's redundant - Ensure correct class level indentation below

    async def _get_llm_response(self,
                                context_packet: Dict[str, Any], # Context now comes from state
                                current_stage: str,
                                conversation_history: List[Dict[str, str]]
                               ) -> LLMResponse: # Corrected return type hint
        """
        Gets a response from the LLM based on context, stage, and history.
        """
        # Prepare prompt for the LLM
        system_message = """You are a friendly and supportive mentor in a conversational context.
        Create a warm, thoughtful response that engages with the user's current message and context.
        Tailor your response to the current stage of the conversation."""

        # Format context and stage into a readable message
        context_str = "\n".join([f"{k}: {v}" for k, v in context_packet.items() if k != "user_id" and k != "text"]) # Exclude text from context display

        # Add stage-specific instructions
        stage_instructions = {
            "initial_conversation": "Keep your response welcoming and open-ended to encourage sharing. Ask thoughtful follow-up questions.",
            "context_deepening": "Explore the user's thoughts more deeply. Ask about their feelings, goals, or challenges.",
            "guidance_preparation": "Offer insights and gentle guidance based on what you've learned. Suggest possible directions or perspectives.",
            "reflection": "Help the user reflect on what they've shared. Summarize key points and identify potential patterns."
        }

        instruction = stage_instructions.get(
            current_stage,
            "Engage thoughtfully with the user's message and provide a supportive response."
        )

        # Extract latest user message text from context_packet if available
        latest_user_text = context_packet.get("text", "User provided no text.") # Use context_packet now

        # Include conversation history in the prompt if available
        history_str = ""
        if conversation_history:
             history_str = "\n\nConversation History:\n" + "\n".join([
                 f"{msg.get('role', 'unknown')}: {msg.get('content', '')}" for msg in conversation_history
             ])

        user_message = f"""
        Context:
        {context_str}

        Current conversation stage: {current_stage}

        Instructions:
        {instruction}

        User's latest message: "{latest_user_text}"

        {history_str}

        Create a thoughtful response to the user that moves the conversation forward.
        """

        # Get response from LLM
        response = await self.llm_client.chat_completion(
            messages=[
                {"role": "system", "content": system_message},
                {"role": "user", "content": user_message}
            ],
            tools=self.available_tools # Pass available tools to LLM
        )

        # Return the full LLMResponse object
        return response

    async def _detect_workflow_transition(
        self,
        message: str,
        context_packet: Dict[str, Any],
        current_stage: str,
        conversation_history: list
    ) -> Optional[Dict[str, Any]]:
        """
        Detect if the user's message indicates a need to transition to a different workflow.

        Args:
            message: The user's message text
            context_packet: Extracted context data
            current_stage: Current conversation stage
            conversation_history: Previous messages in the conversation

        Returns:
            Optional[Dict[str, Any]]: Transition request or None if no transition needed
        """
        try:
            # Don't check for transitions too early in the conversation
            if current_stage == "initial_conversation" and len(conversation_history) < 2:
                return None

            # Prepare prompt for workflow classification
            system_message = """You are an expert workflow classifier. Your task is to determine if a user message
            within an ongoing conversation should trigger a transition to a different workflow type.

            Available workflows:
            - discussion (current): Ongoing conversation and reflection.
            - wheel_generation: User wants activity suggestions or recommendations.
            - activity_feedback: User is giving feedback about a completed activity.
            - progress_review: User wants to review their progress or growth.

            Only recommend a transition if the user's message strongly indicates a need for a specific workflow.
            If the message can be handled well within the current discussion workflow, do not recommend a transition.
            """

            # Prepare conversation context
            if conversation_history:
                convo_context = "\n\n".join([
                    f"{msg.get('role', 'unknown')}: {msg.get('content', '')}"
                    for msg in conversation_history[-3:]  # Include last 3 messages for context
                ])
            else:
                convo_context = "No previous conversation"

            user_message = f"""
            Current workflow: discussion
            Current stage: {current_stage}

            Recent conversation:
            {convo_context}

            User's latest message: "{message}"

            Extracted context:
            Mood: {context_packet.get('mood', 'Unknown')}
            Focus: {context_packet.get('focus', 'Unknown')}
            Time availability: {context_packet.get('time_availability', 'Unknown')}

            Should this trigger a workflow transition? If so, to which workflow type?
            Answer ONLY in JSON format with the following structure:
            {{
              "transition_needed": boolean,
              "target_workflow": "workflow_name" or null,
              "confidence": float (0.0-1.0),
              "reasoning": "string"
            }}
            Ensure the key for the workflow type is "target_workflow".
            """

            # Get classification from LLM
            response = await self.llm_client.chat_completion(
                messages=[
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": user_message}
                ],
                temperature=0.2  # Lower temperature for more precise classification
            )

            # Log token usage for this additional LLM call
            if hasattr(response, 'input_tokens') and hasattr(response, 'output_tokens'):
                logger.debug(f"Workflow transition detection LLM call: {response.input_tokens} in, {response.output_tokens} out tokens")

            # Parse the response
            classification = self._parse_classification_response(response.content)

            # Check if transition is needed and target is valid
            if classification and classification.get("transition_needed") is True:
                target_workflow = classification.get("target_workflow")
                confidence = classification.get("confidence", 0.0)

                # Only transition with sufficient confidence and a valid target
                if target_workflow and confidence >= 0.7:
                    logger.info(f"Detected transition to '{target_workflow}' with confidence {confidence}.")
                    # Return dict matching WorkflowTransitionRequest structure implicitly
                    # (Pydantic will validate when creating the instance in `process`)
                    return {
                        "target_workflow": target_workflow,
                        "message": message,
                        "context": context_packet,
                        # Add confidence and reason to context if needed by next workflow,
                        # otherwise they are just for logging/decision here.
                        # "confidence": confidence, # Optional: Add if needed downstream
                        # "reason": classification.get("reasoning", "Based on user request") # Optional
                    }
                else:
                    logger.debug(f"Transition detected but confidence ({confidence}) or target ('{target_workflow}') insufficient.")

            return None # No transition needed or confidence too low

        except Exception as e:
            # Log the error but don't disrupt the conversation
            import logging
            logging.error(f"Error in workflow transition detection: {str(e)}")
            return None

    def _parse_classification_response(self, response_text: str) -> Optional[Dict[str, Any]]:
        """Parse LLM response for classification information"""
        try:
            # Try to extract JSON from the response
            import json
            import re

            # First, try direct JSON parsing
            try:
                return json.loads(response_text)
            except json.JSONDecodeError:
                # Look for JSON-like structure with regex
                json_match = re.search(r'(\{.*"transition_needed".*\})', response_text, re.DOTALL)
                if json_match:
                    try:
                        return json.loads(json_match.group(1))
                    except json.JSONDecodeError:
                        pass

                # Try to extract from code blocks
                json_block = re.search(r'```(?:json)?\s*([\s\S]*?)\s*```', response_text)
                if json_block:
                    try:
                        return json.loads(json_block.group(1))
                    except json.JSONDecodeError:
                        pass

            # Fallback: manual extraction
            transition_needed = "true" in response_text.lower() and "transition_needed" in response_text.lower()

            # If transition seems needed, extract other fields using the correct key
            if transition_needed:
                workflow_match = re.search(r'"target_workflow"[:\s]+"([^"]+)"', response_text) # Use target_workflow
                confidence_match = re.search(r'"confidence"[:\s]+(0\.\d+|1\.0)', response_text) # Allow 1.0
                reasoning_match = re.search(r'"reasoning"[:\s]+"([^"]+)"', response_text)

                return {
                    "transition_needed": True,
                    "target_workflow": workflow_match.group(1) if workflow_match else None, # Return None if not found
                    "confidence": float(confidence_match.group(1)) if confidence_match else 0.0, # Default 0
                    "reasoning": reasoning_match.group(1) if reasoning_match else "Fallback reasoning"
                }

            # If transition_needed is explicitly false or not found
            return {"transition_needed": False}

        except Exception:
            return None

    async def _format_transition_response(self, transition_request: Dict[str, Any]) -> str:
        """
        Format a response explaining the workflow transition.

        Args:
            transition_request: Details about the workflow transition

        Returns:
            str: Formatted response for the user
        """
        workflow_type = transition_request.get("workflow_type")

        # Prepare transition explanations for different workflows
        transition_explanations = {
            "wheel_generation": "I'd be happy to suggest some activities for you! Let me think about what might be a good fit based on our conversation.",
            "activity_feedback": "I'd like to hear more about your experience with that activity. Let me get some details about how it went.",
            "progress_review": "I'd be glad to help you review your progress. Let me gather some information about your journey so far."
        }

        # Get the appropriate explanation or use a default
        explanation = transition_explanations.get(
            workflow_type,
            "I think I can help you better by shifting our conversation a bit. Let me adjust my approach."
        )

        # Add a smooth transition phrase
        return f"{explanation} I'll adjust my approach to better address your needs."

    async def _call_tool(self, tool_code, tool_input):
        """Call a tool and record the usage"""
        try:
            from apps.main.agents.tools.tools_util import execute_tool
            logger.debug(f"Attempting to execute tool '{tool_code}' with input: {tool_input}")
            result = await execute_tool(tool_code, tool_input, self.run_id)
            logger.debug(f"Tool '{tool_code}' executed successfully.")
            return result
        except Exception as e:
            # Log the specific error and re-raise to ensure benchmark records the failure
            logger.error(f"Error executing tool '{tool_code}': {str(e)}", exc_info=True)
            raise # Re-raise the exception
