# apps/main/agents/error_handler.py

import logging # Import logging
from typing import Dict, Any, Tu<PERSON>, Optional # Import Optional
from pydantic import BaseModel
from asgiref.sync import sync_to_async # Import sync_to_async

from apps.main.agents.base_agent import LangGraphAgent
from apps.main.services.database_service import RealDatabaseService
from apps.main.llm.service import RealLLMClient
# Import LLMConfig for type hinting
from apps.main.models import LLMConfig

logger = logging.getLogger(__name__) # Add logger

# Define maximum retries allowed before forcing fallback
MAX_RETRIES = 3

class ErrorHandlerAgent(LangGraphAgent):
    """
    Agent that handles errors in the workflow, attempts recovery,
     and provides graceful degradation when necessary.
     """

    def __init__(self,
                 user_profile_id: str,
                 db_service=None,
                 llm_client=None,
                 llm_config: Optional[LLMConfig] = None): # Changed to accept LLMConfig
        # Pass LLM config up to the base class constructor
        super().__init__(
            user_profile_id=user_profile_id,
            agent_role="error_handler",
            llm_config=llm_config # Pass the LLMConfig object
        )

        # Use provided dependencies or create defaults
        self.db_service = db_service or RealDatabaseService()

        # Instantiate LLM client using config from base class if not provided externally
        if llm_client:
            self.llm_client = llm_client
        else:
            # RealLLMClient now requires an LLMConfig object
            if not self.llm_config:
                logger.debug(f"Instantiating RealLLMClient for ErrorHandlerAgent without LLMConfig: will use default")

            logger.debug(f"Instantiating RealLLMClient for ErrorHandlerAgent with LLMConfig: {self.llm_config.name if self.llm_config else 'None'}")
            self.llm_client = RealLLMClient(llm_config=self.llm_config) # Pass the LLMConfig object

        # Error handler might not need definition/tools loaded in the same way,
        # but initialize attributes for consistency if base class expects them.
        self.agent_definition = None
        self.available_tools = []
        self.run_id = None # Initialize run_id

    async def process(self, state: BaseModel) -> Dict[str, Any]: # Return type changed
        """Handle errors and attempt recovery."""
        # Note: ErrorHandlerAgent doesn't use _ensure_loaded as it might need to run even if DB is down.
        # It attempts to load its definition for logging purposes but proceeds if it fails.

        current_operation = "extracting_state"
        # Extract relevant data from state
        error = getattr(state, "error", "Unknown error")
        last_agent = getattr(state, "last_agent", "unknown")
        current_stage = getattr(state, "current_stage", "unknown")
        workflow_id = getattr(state, "workflow_id", None)

        # Convert user_profile_id to int for DB calls if it's not a test or benchmark ID
        try:
            # Check if this is a test ID (starts with 'test-') or benchmark ID (starts with 'benchmark-user-')
            if isinstance(self.user_profile_id, str) and (self.user_profile_id.startswith('test-') or self.user_profile_id.startswith('benchmark-user-')):
                # For test and benchmark IDs, use a default integer ID
                user_profile_id_int = 1  # Use a default test/benchmark ID
                logger.debug(f"Using default user_profile_id (1) for test/benchmark ID: {self.user_profile_id}")
            else:
                # For real IDs, convert to int
                user_profile_id_int = int(self.user_profile_id)
        except ValueError:
            # If user_id is invalid, we can't log to DB, proceed with basic fallback
            logger.error(f"Invalid user_profile_id format: {self.user_profile_id}. Cannot log error run to DB.")
            fallback_response = "I'm sorry, I encountered an unexpected issue. Please try again later."
            return {
                "error": "Invalid user profile ID",
                "output_data": {
                    "next_agent": "end",
                    "error_handled": False,
                    "user_response": fallback_response,
                    "meta_error": "Invalid user profile ID"
                },
                "completed": True
            }

        current_operation = "starting_run"
        # Start profiling DB start
        self.start_stage('error_db_start_run')
        # Start a run in the database (attempt to load definition, but proceed if fails)
        agent_definition = None
        try:
            # Try loading definition synchronously (wrapped)
            load_def_sync = sync_to_async(self.db_service.load_agent_definition, thread_sensitive=True)
            agent_definition = await load_def_sync(self.agent_role)
        except Exception as load_def_e:
            logger.warning(f"Could not load agent definition for error_handler: {load_def_e}")

        try:
            # Start run - WRAPPED
            run = await self.db_service.start_run(
                agent_definition=agent_definition, # Pass loaded definition (or None)
                user_profile_id=user_profile_id_int, # Use int ID
                input_data={ # Pass as input_data kwarg
                    "error": error,
                    "last_agent": last_agent,
                    "current_stage": current_stage
                },
                state={"workflow_id": workflow_id} # Pass as state kwarg
            )
            self.run_id = run.id if hasattr(run, 'id') else str(run.get('id', 'mock-run-id'))
            self.stop_stage('error_db_start_run')
        except Exception as start_run_e:
            self.stop_stage('error_db_start_run') # Stop stage even on error
            logger.error(f"Failed to start run for error handler: {start_run_e}", exc_info=True)
            # Proceed without a run_id if DB fails
            self.run_id = None

        try:
            current_operation = "analyzing_error"
            # Analyze the error, considering the current state (including retry count)
            self.start_stage('error_analyze_error')
            error_analysis = await self._analyze_error(error, last_agent, current_stage, state)
            self.stop_stage('error_analyze_error')

            # Determine if recovery is possible based on analysis
            can_recover = error_analysis.get("can_recover", False)
            recovery_path = error_analysis.get("recovery_path", None)

            if can_recover and recovery_path:
                current_operation = "creating_recovery_plan"
                # Create recovery plan
                self.start_stage('error_create_recovery_plan')
                recovery_plan = await self._create_recovery_plan(error_analysis, state)
                self.stop_stage('error_create_recovery_plan')

                current_operation = "preparing_recovery_output"
                # Output recovery data and routing
                output_data = {
                    "next_agent": recovery_path,
                    "recovery_plan": recovery_plan,
                    "error_handled": True,
                    "user_message": "I'm working to address a technical issue. Let me try again."
                }

                # Clear the error in state updates
                state_updates = {
                    "error": None # Clear the error field in the state
                }

            else:
                current_operation = "creating_fallback_response"
                # Create graceful degradation response
                self.start_stage('error_create_fallback')
                fallback_response = await self._create_fallback_response(error_analysis)
                self.stop_stage('error_create_fallback')

                current_operation = "preparing_fallback_output"
                # Output degradation response and end workflow
                output_data = {
                    "next_agent": "end",
                    "error_handled": False,
                    "user_response": fallback_response,
                    "recovery_plan": None,
                    "original_error": error # Include original error for context
                }

                # Mark workflow as completed with error in state updates
                state_updates = {
                    "completed": True,
                    "error": error # Keep the original error in the state for final logging/analysis
                }

            current_operation = "completing_run"
            # Complete the run
            if self.run_id:
                try:
                    self.start_stage('error_db_complete_run')
                    # Complete run - WRAPPED
                    await self.db_service.complete_run(
                        run_id=self.run_id, # Use run_id kwarg
                        output_data=output_data, # Use output_data kwarg
                        state={"workflow_id": workflow_id}, # Use state kwarg
                        status='completed' # Use status kwarg
                    )
                    self.stop_stage('error_db_complete_run')
                except Exception as complete_run_e:
                    self.stop_stage('error_db_complete_run') # Stop stage even on error
                    logger.error(f"Failed to complete run for error handler: {complete_run_e}", exc_info=True)
                    # Ignore completion errors in error handler itself

            # Add output_data and run_id to the final state updates
            state_updates["output_data"] = output_data
            if self.run_id:
                state_updates["run_id"] = self.run_id

            logger.debug(f"ErrorHandlerAgent returning state updates: {state_updates}")
            return state_updates

        except Exception as e:
            # Handle errors within the error handler itself (meta-error)
            error_type = type(e).__name__
            meta_error_message = f"Meta-error in error handler during '{current_operation}': {str(e)}"
            logger.critical(f"Exception caught in ErrorHandlerAgent process ({error_type} during {current_operation}): {meta_error_message}", exc_info=True)

            # Create simple fallback response
            fallback_response = "I'm very sorry, but I encountered an internal problem while trying to handle an earlier issue. Please try starting a new conversation."

            # Output basic error response and end workflow
            meta_error_output_data = {
                "next_agent": "end",
                "error_handled": False,
                "user_response": fallback_response,
                "error": meta_error_message, # For Mock DB check
                "meta_error": meta_error_message # Explicit meta-error field
            }

            # Mark workflow as completed in state updates
            meta_error_updates = {
                "completed": True,
                "error": meta_error_message, # Update state error to meta-error
                "output_data": meta_error_output_data
            }

            # Attempt to complete the run with error status, even in meta-error case
            if self.run_id:
                try:
                    current_operation = "completing_run_meta_failure"
                    self.start_stage('error_db_complete_run_meta_error')
                    # Complete run - WRAPPED
                    await self.db_service.complete_run(
                        run_id=self.run_id,
                        output_data=meta_error_output_data, # Pass meta-error details
                        state={"error_details": meta_error_message}, # Simple state
                        status='failed',
                        error_message=meta_error_message # Pass explicit error message if arg exists
                    )
                    self.stop_stage('error_db_complete_run_meta_error')
                except Exception as db_error:
                    self.stop_stage('error_db_complete_run_meta_error') # Stop stage even on error
                    logger.error(f"Failed to complete run in DB after meta-error: {db_error}", exc_info=True)

            if self.run_id:
                 meta_error_updates["run_id"] = self.run_id

            return meta_error_updates

    async def _analyze_error(self, error, last_agent, current_stage, state):
        """Analyze the error, considering retry count, to determine if recovery is possible"""
        retry_count = getattr(state, "retry_count", 0)

        # Check retry limit first
        if retry_count >= MAX_RETRIES:
            return {
                "error_type": "persistent_retry_limit",
                "severity": "high",
                "can_recover": False, # Force non-recovery due to retry limit
                "recovery_path": None,
                "failed_agent": last_agent,
                "failed_stage": current_stage
            }

        # If retry limit not hit, perform normal analysis
        recoverable_patterns = [
            "timeout", "temporary", "retry", "resource unavailable",
            "connection", "network", "throttled", "rate limit"
        ]
        
        # Check if error contains recoverable patterns
        can_recover = any(pattern in (error or "").lower() for pattern in recoverable_patterns)
        
        # Map stages to agent nodes
        stage_to_agent = {
            "mentor_context_gathering": "mentor",
            "orchestration_initial": "orchestrator",
            "resource_assessment": "resource",
            "engagement_analysis": "engagement",
            "psychological_assessment": "psychological", 
            "strategy_formulation": "strategy",
            "activity_selection": "activity",
            "ethical_validation": "ethical",
            "orchestration_final": "orchestrator",
            "mentor_presentation": "mentor"
        }
        
        # Determine recovery path
        recovery_path = None
        if can_recover:
            # Try to recover by returning to the agent that failed
            recovery_path = last_agent
        elif current_stage in stage_to_agent:
            # If we can't recover at the exact point, try to resume at the stage
            recovery_path = stage_to_agent[current_stage]
        
        return {
            "error_type": "transient" if can_recover else "persistent",
            "severity": "low" if can_recover else "high",
            "can_recover": can_recover,
            "recovery_path": recovery_path,
            "failed_agent": last_agent,
            "failed_stage": current_stage
        }
    
    async def _create_recovery_plan(self, error_analysis, state):
        """Create a plan for recovery"""
        recovery_path = error_analysis.get("recovery_path")
        failed_agent = error_analysis.get("failed_agent")
        
        return {
            "type": "agent_retry",
            "target_agent": recovery_path,
            "instructions": f"Retry processing from {recovery_path} agent",
            "state_modifications": {
                "skip_validation": True,
                "retry_count": getattr(state, "retry_count", 0) + 1,
                "previous_error": error_analysis.get("error_type")
            }
        }
    
    async def _create_fallback_response(self, error_analysis):
        """Create a graceful degradation response for the user"""
        system_message = """You are a helpful assistant that needs to craft a friendly message to a user
        when a technical error has occurred. Be apologetic but positive, and provide a simple explanation
        without technical details. Suggest they try again later."""
        
        user_message = f"""
        An error occurred in our system. The error type is: {error_analysis.get('error_type')}
        and the severity is: {error_analysis.get('severity')}.
        
        Please craft a friendly message to the user explaining that we cannot complete their request
        right now, but they can try again later. Do not include technical details.
        """
        
        try:
            # Get response from LLM
            response = await self.llm_client.chat_completion(
                messages=[
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": user_message}
                ]
            )
            
            # Extract content
            return response.content if hasattr(response, 'content') else str(response)
            
        except Exception:
            # Default fallback message if LLM fails
            return "I'm sorry, I wasn't able to complete your request at this time. Please try again later."
