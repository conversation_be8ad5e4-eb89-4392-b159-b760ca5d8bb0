"""
Agent Benchmarking Module

This module provides benchmarking capabilities for agents, including performance metrics,
stage timing, and tool call tracking. It's designed to work with the new workflow
benchmarking system while maintaining compatibility with existing agent benchmarking needs.
"""

import pprint
import time
import asyncio
import statistics
from typing import Dict, Any, List, Optional, Tuple, Callable, TYPE_CHECKING
import json
import logging
import traceback
from collections import defaultdict
from dataclasses import dataclass, field

# Import the custom exception
from apps.main.agents.exceptions import SimulatedToolException
# Import EventService for sending debug info
from apps.main.services.event_service import EventService

# Use TYPE_CHECKING for type hints to avoid import-time dependencies
if TYPE_CHECKING:
    from apps.main.models import LLMConfig

logger = logging.getLogger(__name__)


class StageTimer:
    """Helper class to time specific stages within a process."""
    def __init__(self):
        self._start_times: Dict[str, float] = {}
        # Store timings for each stage across multiple calls within a single run
        self._timings: Dict[str, List[float]] = defaultdict(list)

    def start(self, stage_name: str):
        """Start timing a stage."""
        self._start_times[stage_name] = time.monotonic()

    def stop(self, stage_name: str):
        """Stop timing a stage and record its duration."""
        if stage_name in self._start_times:
            duration = time.monotonic() - self._start_times[stage_name]
            self._timings[stage_name].append(duration)
            del self._start_times[stage_name]
        else:
            logger.warning(f"Attempted to stop timer for stage '{stage_name}' which was not started or already stopped.")

    def get_timings(self) -> Dict[str, List[float]]:
        """Return the recorded timings for the completed stages."""
        return self._timings.copy()

    def reset(self):
        """Clear all recorded start times and timings."""
        self._start_times.clear()
        self._timings.clear()


@dataclass
class BenchmarkResult:
    """Detailed benchmark results."""
    agent_role: str
    scenario_name: str
    runs: int
    durations: List[float]
    mean_duration: float
    median_duration: float
    min_duration: float
    max_duration: float
    std_dev: float = 0
    tool_call_counts: Dict[str, int] = field(default_factory=dict)
    memory_operations: int = 0
    success_rate: float = 1.0
    errors: List[str] = field(default_factory=list)
    last_output_data: Optional[Dict[str, Any]] = None
    stage_timings: Dict[str, List[float]] = field(default_factory=lambda: defaultdict(list))
    last_response_length: Optional[int] = None
    total_input_tokens: int = 0
    total_output_tokens: int = 0
    tool_call_details: Dict[str, Any] = field(default_factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        """Convert results to a dictionary."""
        return {
            'agent_role': self.agent_role,
            'scenario': self.scenario_name,
            'performance': {
                'runs': self.runs,
                'mean_duration_s': round(self.mean_duration, 4),
                'median_duration_s': round(self.median_duration, 4),
                'min_duration_s': round(self.min_duration, 4),
                'max_duration_s': round(self.max_duration, 4),
                'std_dev': round(self.std_dev, 4),
                'success_rate': self.success_rate,
            },
            'operations': {
                'tool_calls': dict(sorted(self.tool_call_counts.items())),
                'tool_call_details': self.tool_call_details,
                'memory_operations': self.memory_operations,
                'last_response_length': self.last_response_length,
                'total_input_tokens': self.total_input_tokens,
                'total_output_tokens': self.total_output_tokens,
            },
            'errors': self.errors,
            'last_output': self.last_output_data,
            'stage_timings_raw': {k: v for k, v in self.stage_timings.items()}
        }

    def to_markdown(self) -> str:
        """Generate markdown report of benchmark results."""
        result = f"## Benchmark: {self.agent_role} - {self.scenario_name}\n\n"

        # Performance metrics
        result += "### Performance Metrics\n"
        result += f"- **Runs**: {self.runs}\n"
        result += f"- **Mean Duration**: {self.mean_duration:.4f}s\n"
        result += f"- **Median Duration**: {self.median_duration:.4f}s\n"
        result += f"- **Min/Max**: {self.min_duration:.4f}s / {self.max_duration:.4f}s\n"
        result += f"- **Standard Deviation**: {self.std_dev:.4f}s\n"
        result += f"- **Success Rate**: {self.success_rate * 100:.1f}%\n"
        if self.last_response_length is not None:
            result += f"- **Last Response Length**: {self.last_response_length} characters\n"
        result += "\n"

        # Tool calls
        if self.tool_call_counts:
            result += "### Tool Call Analysis\n"
            for tool, count in sorted(self.tool_call_counts.items()):
                result += f"- **{tool}**: {count} calls\n"

        # Errors if any
        if self.errors:
            result += "\n### Errors\n"
            for error in self.errors:
                result += f"- {error}\n"

        return result


# Import mock types for type hinting
from apps.main.testing.mock_database_service import MockDatabaseService
from apps.main.testing.mock_tool_registry import MockToolRegistry


class AgentBenchmarkImproved:
    """Improved performance benchmarking for agents with detailed metrics."""

    def __init__(self,
                 agent_class,
                 user_profile_id="1",
                 mock_db_service: Optional[MockDatabaseService] = None,
                 mock_tool_registry: Optional[MockToolRegistry] = None,
                 agent_llm_config: Optional['LLMConfig'] = None):
        """Initialize the benchmark for an agent class, optionally with mocks and LLM config.

        Args:
            agent_class: The agent class to benchmark.
            user_profile_id: The user profile ID to use.
            mock_db_service: Optional pre-configured mock database service.
            mock_tool_registry: Optional pre-configured mock tool registry.
            agent_llm_config: Optional LLMConfig object defining the agent's LLM settings.
        """
        self.agent_class = agent_class
        self.user_profile_id = user_profile_id
        self.agent_role = getattr(agent_class, 'agent_role', agent_class.__name__)
        # Store mocks if provided
        self.mock_db_service = mock_db_service
        self.mock_tool_registry = mock_tool_registry
        # Store agent LLM config object if provided
        self.agent_llm_config = agent_llm_config
        self._current_agent_instance = None
        self.stage_timer = StageTimer()

    async def run_benchmark(self,
                           scenario_name: str,
                           input_data: Dict[str, Any],
                           initial_state: Optional[Dict[str, Any]] = None,
                           runs: int = 5,
                           warmup_runs: int = 1) -> BenchmarkResult:
        """
        Run a benchmark with detailed metrics collection.

        Args:
            scenario_name: Name of the benchmark scenario.
            input_data: Input data for the agent.
            initial_state: Initial state for the agent (defaults to workflow_id only).
            runs: Number of benchmark runs (after warmup).
            warmup_runs: Number of warmup runs.

        Returns:
            BenchmarkResult: Detailed benchmark results.
        """
        # Set default initial state if none provided
        if initial_state is None:
            initial_state = {"workflow_id": f"benchmark-{scenario_name}"}

        # Track runs
        durations = []
        success_count = 0
        errors = []
        total_tool_calls = {}
        total_memory_ops = 0
        total_input_tokens = 0
        total_output_tokens = 0
        last_output_data = None
        last_response_length = None
        aggregated_stage_timings: Dict[str, List[float]] = defaultdict(list)
        detailed_tool_calls = []  # Track detailed tool call information

        # For each run (including warmup)
        for run_num in range(warmup_runs + runs):
            is_warmup = run_num < warmup_runs
            self.stage_timer.reset()

            # Reset mock counters before each run (if mocks exist)
            if self.mock_db_service and hasattr(self.mock_db_service, 'reset_memory_op_count'):
                self.mock_db_service.reset_memory_op_count()
            if self.mock_tool_registry and hasattr(self.mock_tool_registry, 'reset'):
                self.mock_tool_registry.reset()

            try:
                # Instantiate the agent, passing the LLMConfig object if provided
                agent_kwargs = {
                    'user_profile_id': self.user_profile_id,
                    'llm_config': self.agent_llm_config
                }

                logger.debug(f"Instantiating agent {self.agent_class.__name__} with LLMConfig: {self.agent_llm_config.name if self.agent_llm_config else 'None'}")
                agent = self.agent_class(**agent_kwargs)
                self._current_agent_instance = agent

                # Inject DB mocks if they were provided during initialization
                if self.mock_db_service:
                    agent.db_service = self.mock_db_service
                # Note: Agents don't accept tool_registry parameter or attribute
                # They load tools through their database service instead
                if self.mock_tool_registry:
                    logger.debug(f"Mock tool registry available but not injected into {self.agent_class.__name__} (agents load tools via db_service)")
                # Inject the profiler/timer into the agent instance
                if hasattr(agent, 'profiler'):
                    agent.profiler = self.stage_timer
                else:
                    logger.warning(f"Agent class {self.agent_class.__name__} does not have a 'profiler' attribute. Stage timing will not be injected.")

                # Construct the state object for the agent's process method
                state_for_process = {
                    **initial_state,
                    "context_packet": input_data,
                    "initial_context_packet": input_data,
                    "conversation_history": [],
                    "current_stage": initial_state.get("current_stage", "initial_conversation")
                }
                logger.debug(f"Constructed state for agent.process:\n{pprint.pformat(state_for_process)}")

                # Run the agent's process method
                start_time = time.monotonic()
                state_updates = await agent.process(state_for_process)
                duration = time.monotonic() - start_time

                # Check for errors returned in the state updates
                if "error" in state_updates and state_updates["error"]:
                    raise RuntimeError(f"Agent process returned an error: {state_updates['error']}")

                # Extract output_data for metrics/evaluation
                output_data = state_updates.get("output_data", {})

                # Extract token counts from state_updates if available
                run_input_tokens = state_updates.get("llm_input_tokens", 0)
                run_output_tokens = state_updates.get("llm_output_tokens", 0)

                # Skip metrics collection for warmup runs
                if is_warmup:
                    continue

                # Record successful run
                durations.append(duration)
                success_count += 1
                last_output_data = output_data

                # Calculate response length from last_output_data
                response_text = None
                if isinstance(last_output_data, dict):
                    response_text = last_output_data.get('user_response') or \
                                    last_output_data.get('response_text') or \
                                    last_output_data.get('response')
                if isinstance(response_text, str):
                    last_response_length = len(response_text)
                else:
                    last_response_length = None

                logger.debug(f"Run {run_num - warmup_runs + 1} successful. Duration: {duration:.4f}s, Response Length: {last_response_length}")

                # Collect stage timings for this successful run
                run_stage_timings = self.stage_timer.get_timings()
                for stage, timings in run_stage_timings.items():
                    aggregated_stage_timings[stage].extend(timings)

                # Aggregate token counts for non-warmup runs
                total_input_tokens += run_input_tokens
                total_output_tokens += run_output_tokens

                # Collect metrics from mocks (DB and Tools only)
                run_memory_ops = 0
                if self.mock_db_service and hasattr(self.mock_db_service, 'get_memory_op_count'):
                    run_memory_ops = self.mock_db_service.get_memory_op_count()
                total_memory_ops += run_memory_ops

                # Use the recorded calls from the registry for this run
                if self.mock_tool_registry and hasattr(self.mock_tool_registry, 'calls'):
                    run_tool_calls_details = self.mock_tool_registry.calls
                    run_tool_counts = {}
                    for call in run_tool_calls_details:
                        tool_code = call['tool_code']
                        run_tool_counts[tool_code] = run_tool_counts.get(tool_code, 0) + 1
                        # Store detailed call information
                        detailed_tool_calls.append({
                            'run_number': run_num - warmup_runs + 1,
                            'tool_code': tool_code,
                            'is_mocked': call.get('is_mocked', True),
                            'mock_type': call.get('mock_type', 'registry'),
                            'call_count': call.get('call_count', 1),
                            'input_size': len(str(call.get('tool_input', {})))
                        })
                    # Add this run's counts to the total
                    for tool_code, count in run_tool_counts.items():
                        total_tool_calls[tool_code] = total_tool_calls.get(tool_code, 0) + count

            except SimulatedToolException as ste:
                # Skip error tracking for warmup runs
                if is_warmup:
                    logger.warning(f"Warmup run {run_num + 1} failed with simulated tool error: {ste}", exc_info=False)
                    continue
                # Record simulated error
                error_msg = f"Run {run_num - warmup_runs + 1}: {str(ste)}"
                errors.append(error_msg)
                logger.error(f"Benchmark run failed with simulated tool error: {error_msg}")

            except Exception as e:
                # Skip error tracking for warmup runs
                if is_warmup:
                    logger.warning(f"Warmup run {run_num + 1} failed: {e}", exc_info=True)
                    continue

                # Record error
                error_msg = f"Run {run_num - warmup_runs + 1}: {str(e)}"
                errors.append(error_msg + " (Full traceback sent via debug channel)")
                logger.error(f"Benchmark run failed: {error_msg}", exc_info=True)

                # Capture and send full traceback via EventService
                tb_str = traceback.format_exc()
                debug_message = f"Benchmark Run Error (Scenario: {scenario_name}, Agent: {self.agent_role}, Run: {run_num - warmup_runs + 1}):\n{tb_str}"
                try:
                    await EventService.emit_debug_info(
                        level='error',
                        user_profile_id=self.user_profile_id,
                        message=debug_message,
                        source="AgentBenchmark"
                    )
                    logger.info(f"Sent detailed benchmark error traceback via debug channel to user {self.user_profile_id}")
                except Exception as emit_err:
                    logger.error(f"Failed to emit debug info for benchmark error: {emit_err}", exc_info=True)
                    raise emit_err

        # Calculate statistics from successful runs
        if not durations:
            # If no successful runs, return error result
            return BenchmarkResult(
                agent_role=self.agent_role,
                scenario_name=scenario_name,
                runs=runs,
                durations=[],
                mean_duration=0,
                median_duration=0,
                min_duration=0,
                max_duration=0,
                std_dev=0,
                tool_call_counts={},
                memory_operations=0,
                success_rate=0,
                errors=errors or ["No successful runs completed"],
                last_output_data=None,
                stage_timings=defaultdict(list),
                last_response_length=None,
                total_input_tokens=0,
                total_output_tokens=0
            )

        # Calculate standard statistics
        mean_duration = statistics.mean(durations)
        median_duration = statistics.median(durations)
        min_duration = min(durations)
        max_duration = max(durations)
        std_dev = statistics.stdev(durations) if len(durations) > 1 else 0
        success_rate = success_count / runs

        # Prepare detailed tool call summary
        tool_call_summary = {
            'total_calls': len(detailed_tool_calls),
            'mocked_calls': len([call for call in detailed_tool_calls if call.get('is_mocked', True)]),
            'real_calls': len([call for call in detailed_tool_calls if not call.get('is_mocked', True)]),
            'calls_by_tool': {},
            'calls_by_run': {},
            'detailed_calls': detailed_tool_calls
        }

        # Group calls by tool
        for call in detailed_tool_calls:
            tool_code = call['tool_code']
            if tool_code not in tool_call_summary['calls_by_tool']:
                tool_call_summary['calls_by_tool'][tool_code] = {
                    'total': 0, 'mocked': 0, 'real': 0
                }
            tool_call_summary['calls_by_tool'][tool_code]['total'] += 1
            if call.get('is_mocked', True):
                tool_call_summary['calls_by_tool'][tool_code]['mocked'] += 1
            else:
                tool_call_summary['calls_by_tool'][tool_code]['real'] += 1

        # Create result object using accumulated totals
        result = BenchmarkResult(
            agent_role=self.agent_role,
            scenario_name=scenario_name,
            runs=runs,
            durations=durations,
            mean_duration=mean_duration,
            median_duration=median_duration,
            min_duration=min_duration,
            max_duration=max_duration,
            std_dev=std_dev,
            tool_call_counts=total_tool_calls,
            memory_operations=total_memory_ops,
            success_rate=success_rate,
            errors=errors,
            last_output_data=last_output_data,
            stage_timings=aggregated_stage_timings,
            last_response_length=last_response_length,
            total_input_tokens=total_input_tokens,
            total_output_tokens=total_output_tokens,
            tool_call_details=tool_call_summary
        )

        return result
