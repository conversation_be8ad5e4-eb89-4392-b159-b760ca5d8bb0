# backend/apps/main/graphs/wheel_generation_graph.py

from typing import Any, Dict, Optional, List, Literal
import uuid
import logging
from pydantic import BaseModel, Field
from langgraph.graph import END, StateGraph

# Import agent nodes
from apps.main.agents.orchestrator_agent import OrchestratorAgent
from apps.main.agents.resource_agent import ResourceAgent
from apps.main.agents.engagement_agent import EngagementAndPatternAgent
from apps.main.agents.psy_agent import PsychologicalMonitoringAgent
from apps.main.agents.strategy_agent import StrategyAgent
from apps.main.agents.wheel_activity_agent import WheelAndActivityAgent
from apps.main.agents.ethical_agent import EthicalAgent
from apps.main.agents.error_handler import ErrorHandlerAgent

# Set up a dedicated logger for the wheel generation workflow
logger = logging.getLogger(__name__)

async def _configure_agent_for_execution_mode(state: 'WheelGenerationState', agent_name: str, user_profile_id: str) -> Dict[str, Any]:
    """
    Helper function to configure agent parameters based on execution mode.

    This function now tracks actual execution mode and prevents silent fallbacks.
    If real mode is requested but cannot be configured, it raises an error instead
    of silently falling back to mock mode.

    Args:
        state: The workflow state containing execution mode parameters
        agent_name: Name of the agent for logging purposes
        user_profile_id: User profile ID to pass to agent

    Returns:
        Dictionary of agent constructor arguments

    Raises:
        RuntimeError: If real mode is requested but cannot be configured
    """
    from asgiref.sync import sync_to_async

    agent_kwargs = {
        'user_profile_id': user_profile_id,
    }

    # Track actual execution mode for this agent
    actual_real_llm = False
    actual_real_db = False
    actual_real_tools = False

    # Configure LLM client based on execution mode
    if state.use_real_llm:
        from apps.main.llm.service import RealLLMClient
        from apps.main.models import LLMConfig

        try:
            # Use sync_to_async to access the database from async context
            get_llm_config = sync_to_async(
                lambda: LLMConfig.objects.filter(model_name__icontains='gpt-4o-mini').first(),
                thread_sensitive=True
            )
            llm_config = await get_llm_config()

            if not llm_config:
                get_first_config = sync_to_async(
                    lambda: LLMConfig.objects.first(),
                    thread_sensitive=True
                )
                llm_config = await get_first_config()

            if llm_config:
                agent_kwargs['llm_config'] = llm_config
                agent_kwargs['llm_client'] = RealLLMClient(llm_config)
                actual_real_llm = True
                logger.debug(f"🧠 {agent_name} using real LLM: {llm_config.model_name}")
            else:
                # PREVENT SILENT FALLBACK: Raise error instead of falling back to mock
                error_msg = f"Real LLM mode requested for {agent_name} but no LLM config found in database"
                logger.error(f"❌ REAL MODE FAILURE: {error_msg}")
                raise RuntimeError(error_msg)
        except Exception as e:
            if isinstance(e, RuntimeError):
                raise  # Re-raise our own RuntimeError
            # PREVENT SILENT FALLBACK: Raise error instead of falling back to mock
            error_msg = f"Real LLM mode requested for {agent_name} but configuration failed: {str(e)}"
            logger.error(f"❌ REAL MODE FAILURE: {error_msg}")
            raise RuntimeError(error_msg) from e

    # Configure database service based on execution mode
    if state.use_real_db:
        try:
            from apps.main.services.database_service import RealDatabaseService
            agent_kwargs['db_service'] = RealDatabaseService()
            actual_real_db = True
            logger.debug(f"🗄️ {agent_name} using real database service")
        except Exception as e:
            # PREVENT SILENT FALLBACK: Raise error instead of falling back to mock
            error_msg = f"Real database mode requested for {agent_name} but configuration failed: {str(e)}"
            logger.error(f"❌ REAL MODE FAILURE: {error_msg}")
            raise RuntimeError(error_msg) from e

    # Configure tool registry based on execution mode
    if state.use_real_tools:
        try:
            # Check if required tools are available in the database
            from asgiref.sync import sync_to_async
            from apps.main.models import AgentTool

            # Check for critical tools that agents might need
            critical_tools = ['get_environment_context', 'parse_time_availability', 'get_available_resources']
            get_tool_count = sync_to_async(
                lambda: AgentTool.objects.filter(code__in=critical_tools, is_active=True).count(),
                thread_sensitive=True
            )
            available_critical_tools = await get_tool_count()

            if available_critical_tools == 0:
                # No critical tools available - this might cause issues but let's continue with a warning
                logger.warning(f"⚠️ {agent_name} using real tools but some critical tools may not be available in database")

            # Use the real tool registry from the database
            # Agents will load tools through their database service automatically
            # No need to pass tool_registry parameter as agents discover tools from database
            actual_real_tools = True
            logger.debug(f"🛠️ {agent_name} using real tools (via database service)")
        except Exception as e:
            # PREVENT SILENT FALLBACK: Raise error instead of falling back to mock
            error_msg = f"Real tools mode requested for {agent_name} but configuration failed: {str(e)}"
            logger.error(f"❌ REAL MODE FAILURE: {error_msg}")
            raise RuntimeError(error_msg) from e
    else:
        # Mock mode: explicitly configure mock components
        try:
            from apps.main.services.mock_services import MockDatabaseService
            from apps.main.llm.service import MockLLMClient

            # Override with mock services if not already configured for real mode
            if not actual_real_db:
                agent_kwargs['db_service'] = MockDatabaseService()
                logger.debug(f"🎭 {agent_name} configured with mock database service")

            if not actual_real_llm:
                agent_kwargs['llm_client'] = MockLLMClient()
                logger.debug(f"🎭 {agent_name} configured with mock LLM client")

            actual_real_tools = False
            logger.debug(f"🎭 {agent_name} configured for mock tools")
        except Exception as e:
            # PREVENT SILENT FALLBACK: Raise error if mock mode cannot be configured
            error_msg = f"Mock tools mode requested for {agent_name} but configuration failed: {str(e)}"
            logger.error(f"❌ MOCK MODE FAILURE: {error_msg}")
            raise RuntimeError(error_msg) from e

    # Update state to reflect actual execution mode (not just requested)
    # Note: In LangGraph, state updates must be returned by node functions to be persisted
    try:
        # Check if state has actual_execution_modes attribute and it's a dict
        if hasattr(state, 'actual_execution_modes') and isinstance(state.actual_execution_modes, dict):
            state.actual_execution_modes[agent_name] = {
                'real_llm': actual_real_llm,
                'real_db': actual_real_db,
                'real_tools': actual_real_tools
            }
            logger.debug(f"✅ {agent_name} execution mode tracked: LLM={actual_real_llm}, DB={actual_real_db}, Tools={actual_real_tools}")
        else:
            # For test environments or when state doesn't have proper actual_execution_modes
            logger.warning(f"⚠️ {agent_name} execution mode tracking skipped - state.actual_execution_modes not available or not a dict")
            # Store execution mode in agent_kwargs for return to caller
            agent_kwargs['_execution_mode_used'] = {
                'real_llm': actual_real_llm,
                'real_db': actual_real_db,
                'real_tools': actual_real_tools
            }
    except Exception as e:
        # Handle cases where state is a Mock object or doesn't support item assignment
        logger.warning(f"⚠️ Failed to update execution mode tracking for {agent_name}: {e}")
        # Store execution mode in agent_kwargs for return to caller instead of failing
        agent_kwargs['_execution_mode_used'] = {
            'real_llm': actual_real_llm,
            'real_db': actual_real_db,
            'real_tools': actual_real_tools
        }

    # Note: We don't pass _actual_execution_mode to agent constructors since they don't accept it
    # The execution mode tracking is handled through the state.actual_execution_modes field
    # The calling node function must return the updated actual_execution_modes to persist the changes

    return agent_kwargs

# Define the possible workflow stages based on the flow documentation
WorkflowStage = Literal[
    "orchestration_initial",
    "resource_assessment",
    "engagement_analysis",
    "psychological_assessment", 
    "strategy_formulation",
    "activity_selection",
    "ethical_validation",
    "orchestration_final",
    "error_handling",
    "workflow_complete"
]

class WheelGenerationState(BaseModel):
    """
    State model for the wheel generation workflow.
    Tracks data flow between agents and current workflow stage.
    """
    # Workflow identification
    workflow_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    user_profile_id: str
    user_ws_session_name: Optional[str] = None

    # Input/context data
    context_packet: Dict[str, Any] = Field(default_factory=dict)

    # Execution mode configuration (for benchmarking and real vs mock execution)
    use_real_llm: bool = Field(default=False, description="Whether to use real LLM services instead of mocks")
    use_real_tools: bool = Field(default=False, description="Whether to use real tool implementations instead of mocks")
    use_real_db: bool = Field(default=False, description="Whether to use real database operations instead of mocks")
    mock_tools: Optional[Any] = Field(default=None, description="Mock tool registry for partial mocking")

    # Execution mode tracking (tracks what was actually used by each agent)
    actual_execution_modes: Dict[str, Dict[str, bool]] = Field(
        default_factory=dict,
        description="Tracks actual execution mode used by each agent (real_llm, real_db, real_tools)"
    )

    # Token usage tracking (for benchmarking)
    token_usage: Dict[str, int] = Field(
        default_factory=lambda: {"input_tokens": 0, "output_tokens": 0},
        description="Tracks total token usage across all agents for benchmarking"
    )

    # Agent outputs
    resource_context: Optional[Dict[str, Any]] = None
    engagement_analysis: Optional[Dict[str, Any]] = None
    psychological_assessment: Optional[Dict[str, Any]] = None
    strategy_framework: Optional[Dict[str, Any]] = None
    wheel: Optional[Dict[str, Any]] = None
    ethical_validation: Optional[Dict[str, Any]] = None

    # Output data from the most recent agent (for communication between agents)
    output_data: Dict[str, Any] = Field(default_factory=dict)

    # Routing information
    next_agent: Optional[str] = None

    # Workflow state tracking
    current_stage: WorkflowStage = "orchestration_initial"
    last_agent: Optional[str] = None
    error: Optional[str] = None
    error_context: Optional[Dict[str, Any]] = None
    completed: bool = False


def create_wheel_generation_graph(user_profile_id: str) -> StateGraph:
    """
    Create a LangGraph workflow for wheel generation based on the defined flow document.

    This implements the multi-agent workflow where:
    1. Orchestrator Agent coordinates specialized agents
    2. Resource & Capacity Agent analyzes user resources/environment
    3. Engagement & Pattern Agent analyzes user history
    4. Psychological Monitoring Agent assesses user psychological state
    5. Strategy Agent synthesizes inputs into a strategy
    6. Wheel/Activity Agent selects activities and builds the wheel
    7. Ethical Oversight Agent validates the wheel
    8. Orchestrator Agent handles final integration

    Args:
        user_profile_id: The ID of the user profile this workflow is for

    Returns:
        StateGraph: The configured wheel generation workflow graph
    """
    # Enable logging immediately
    logger.info(f"Creating wheel generation graph for user {user_profile_id}")

    # Initialize the graph with the state model
    workflow = StateGraph(WheelGenerationState)

    # Create execution-mode-aware agent wrapper functions
    # These functions will check the state's execution mode parameters and create appropriate agents

    async def orchestrator_node(state: WheelGenerationState) -> Dict[str, Any]:
        """Orchestrator agent node that respects execution mode parameters."""
        logger.debug(f"🎭 Orchestrator executing with real_llm={state.use_real_llm}, real_tools={state.use_real_tools}, real_db={state.use_real_db}")

        # Configure agent based on execution mode
        agent_kwargs = await _configure_agent_for_execution_mode(state, "Orchestrator", user_profile_id)

        # Create agent with configured parameters
        agent = OrchestratorAgent(**agent_kwargs)

        try:
            # Process the state and return updates using __call__ method
            updated_state = await agent(state)  # Use __call__ method instead of process

            # Return only the fields that need to be updated, not the entire state
            # This preserves the state attributes set by the base agent
            result = {
                'last_agent': 'orchestrator',
                'actual_execution_modes': updated_state.actual_execution_modes,
                'output_data': updated_state.output_data,
                'next_agent': updated_state.next_agent,
                'current_stage': updated_state.current_stage,
                'error': updated_state.error,
                'error_context': updated_state.error_context,
                'run_id': getattr(updated_state, 'run_id', None)
            }

            # Add execution mode tracking to the output_data
            if result['output_data']:
                result['output_data']['execution_mode_used'] = {
                    'real_llm': state.use_real_llm,
                    'real_tools': state.use_real_tools,
                    'real_db': state.use_real_db
                }

            logger.debug(f"🔍 Orchestrator returning actual_execution_modes: {updated_state.actual_execution_modes}")

            return result

        except ValueError as e:
            # Catch LLM configuration errors - in mock mode, this indicates missing mock implementation
            if "No LLMConfig provided" in str(e):
                raise NotImplementedError(
                    f"Mock mode execution not yet fully implemented for Orchestrator Agent. "
                    f"The agent is trying to use real LLM services even in mock mode, which indicates "
                    f"that proper mock implementations are not yet available. "
                    f"Please use the benchmark manager's mock workflow instead."
                ) from e
            else:
                # Re-raise other ValueError exceptions
                raise

    async def resource_node(state: WheelGenerationState) -> Dict[str, Any]:
        """Resource agent node that respects execution mode parameters."""
        logger.debug(f"🎭 Resource executing with real_llm={state.use_real_llm}, real_tools={state.use_real_tools}, real_db={state.use_real_db}")

        # Configure agent based on execution mode
        agent_kwargs = await _configure_agent_for_execution_mode(state, "Resource", user_profile_id)

        # Create agent with configured parameters
        agent = ResourceAgent(**agent_kwargs)
        updated_state = await agent(state)  # Use __call__ method instead of process

        # Return only the fields that need to be updated, not the entire state
        # This preserves the state attributes set by the base agent
        result = {
            'last_agent': 'resource',
            'actual_execution_modes': updated_state.actual_execution_modes,
            'output_data': updated_state.output_data,
            'resource_context': updated_state.resource_context,  # Preserve the state attribute
            'next_agent': updated_state.next_agent,
            'current_stage': updated_state.current_stage,
            'error': updated_state.error,
            'error_context': updated_state.error_context,
            'run_id': getattr(updated_state, 'run_id', None)
        }

        return result

    async def engagement_node(state: WheelGenerationState) -> Dict[str, Any]:
        """Engagement agent node that respects execution mode parameters."""
        logger.debug(f"🎭 Engagement executing with real_llm={state.use_real_llm}, real_tools={state.use_real_tools}, real_db={state.use_real_db}")

        # Configure agent based on execution mode
        agent_kwargs = await _configure_agent_for_execution_mode(state, "Engagement", user_profile_id)

        agent = EngagementAndPatternAgent(**agent_kwargs)
        updated_state = await agent(state)  # Use __call__ method instead of process

        # Return only the fields that need to be updated, not the entire state
        # This preserves the state attributes set by the base agent
        result = {
            'last_agent': 'engagement',
            'actual_execution_modes': updated_state.actual_execution_modes,
            'output_data': updated_state.output_data,
            'engagement_analysis': updated_state.engagement_analysis,  # Preserve the state attribute
            'next_agent': updated_state.next_agent,
            'current_stage': updated_state.current_stage,
            'error': updated_state.error,
            'error_context': updated_state.error_context,
            'run_id': getattr(updated_state, 'run_id', None)
        }

        return result

    async def psychological_node(state: WheelGenerationState) -> Dict[str, Any]:
        """Psychological agent node that respects execution mode parameters."""
        logger.debug(f"🎭 Psychological executing with real_llm={state.use_real_llm}, real_tools={state.use_real_tools}, real_db={state.use_real_db}")

        # Configure agent based on execution mode
        agent_kwargs = await _configure_agent_for_execution_mode(state, "Psychological", user_profile_id)

        agent = PsychologicalMonitoringAgent(**agent_kwargs)
        updated_state = await agent(state)  # Use __call__ method instead of process

        # Return only the fields that need to be updated, not the entire state
        # This preserves the state attributes set by the base agent
        result = {
            'last_agent': 'psychological',
            'actual_execution_modes': updated_state.actual_execution_modes,
            'output_data': updated_state.output_data,
            'psychological_assessment': updated_state.psychological_assessment,  # Preserve the state attribute
            'next_agent': updated_state.next_agent,
            'current_stage': updated_state.current_stage,
            'error': updated_state.error,
            'error_context': updated_state.error_context,
            'run_id': getattr(updated_state, 'run_id', None)
        }

        return result

    async def strategy_node(state: WheelGenerationState) -> Dict[str, Any]:
        """Strategy agent node that respects execution mode parameters."""
        logger.debug(f"🎭 Strategy executing with real_llm={state.use_real_llm}, real_tools={state.use_real_tools}, real_db={state.use_real_db}")

        # Configure agent based on execution mode
        agent_kwargs = await _configure_agent_for_execution_mode(state, "Strategy", user_profile_id)

        agent = StrategyAgent(**agent_kwargs)
        updated_state = await agent(state)  # Use __call__ method instead of process

        # Return only the fields that need to be updated, not the entire state
        # This preserves the state attributes set by the base agent
        result = {
            'last_agent': 'strategy',
            'actual_execution_modes': updated_state.actual_execution_modes,
            'output_data': updated_state.output_data,
            'strategy_framework': updated_state.strategy_framework,  # Preserve the state attribute
            'next_agent': updated_state.next_agent,
            'current_stage': updated_state.current_stage,
            'error': updated_state.error,
            'error_context': updated_state.error_context,
            'run_id': getattr(updated_state, 'run_id', None)
        }

        return result

    async def activity_node(state: WheelGenerationState) -> Dict[str, Any]:
        """Activity agent node that respects execution mode parameters."""
        logger.debug(f"🎭 Activity executing with real_llm={state.use_real_llm}, real_tools={state.use_real_tools}, real_db={state.use_real_db}")

        # Configure agent based on execution mode
        agent_kwargs = await _configure_agent_for_execution_mode(state, "Activity", user_profile_id)

        agent = WheelAndActivityAgent(**agent_kwargs)
        updated_state = await agent(state)  # Use __call__ method instead of process

        # Return only the fields that need to be updated, not the entire state
        # This preserves the state attributes set by the base agent
        result = {
            'last_agent': 'activity',
            'actual_execution_modes': updated_state.actual_execution_modes,
            'output_data': updated_state.output_data,
            'wheel': updated_state.wheel,  # Preserve the state attribute
            'next_agent': updated_state.next_agent,
            'current_stage': updated_state.current_stage,
            'error': updated_state.error,
            'error_context': updated_state.error_context,
            'run_id': getattr(updated_state, 'run_id', None)
        }

        return result

    async def ethical_node(state: WheelGenerationState) -> Dict[str, Any]:
        """Ethical agent node that respects execution mode parameters."""
        logger.debug(f"🎭 Ethical executing with real_llm={state.use_real_llm}, real_tools={state.use_real_tools}, real_db={state.use_real_db}")

        # Configure agent based on execution mode
        agent_kwargs = await _configure_agent_for_execution_mode(state, "Ethical", user_profile_id)

        agent = EthicalAgent(**agent_kwargs)
        updated_state = await agent(state)  # Use __call__ method instead of process

        # Return only the fields that need to be updated, not the entire state
        # This preserves the state attributes set by the base agent
        result = {
            'last_agent': 'ethical',
            'actual_execution_modes': updated_state.actual_execution_modes,
            'output_data': updated_state.output_data,
            'ethical_validation': updated_state.ethical_validation,  # Preserve the state attribute
            'next_agent': updated_state.next_agent,
            'current_stage': updated_state.current_stage,
            'error': updated_state.error,
            'error_context': updated_state.error_context,
            'run_id': getattr(updated_state, 'run_id', None)
        }

        return result

    async def error_handler_node(state: WheelGenerationState) -> Dict[str, Any]:
        """Error handler node that respects execution mode parameters."""
        logger.debug(f"🎭 Error Handler executing with real_llm={state.use_real_llm}, real_tools={state.use_real_tools}, real_db={state.use_real_db}")

        # Configure agent based on execution mode
        agent_kwargs = await _configure_agent_for_execution_mode(state, "ErrorHandler", user_profile_id)

        agent = ErrorHandlerAgent(**agent_kwargs)
        updated_state = await agent(state)  # Use __call__ method instead of process

        # Return only the fields that need to be updated, not the entire state
        # This preserves the state attributes set by the base agent
        result = {
            'last_agent': 'error_handler',
            'actual_execution_modes': updated_state.actual_execution_modes,
            'output_data': updated_state.output_data,
            'next_agent': updated_state.next_agent,
            'current_stage': updated_state.current_stage,
            'error': updated_state.error,
            'error_context': updated_state.error_context,
            'run_id': getattr(updated_state, 'run_id', None)
        }

        return result

    # Add all agent nodes to the graph using the wrapper functions
    workflow.add_node("orchestrator", orchestrator_node)
    workflow.add_node("resource", resource_node)
    workflow.add_node("engagement", engagement_node)
    workflow.add_node("psychological", psychological_node)
    workflow.add_node("strategy", strategy_node)
    workflow.add_node("activity", activity_node)
    workflow.add_node("ethical", ethical_node)
    workflow.add_node("error_handler", error_handler_node)
    
    # Define routing logic for each node
    
    # 1. Orchestrator Agent: Workflow coordination
    def route_from_orchestrator(state: WheelGenerationState):
        """Route from the Orchestrator Agent to the next agent in the flow."""
        logger.debug(f"🔄 ROUTING - Orchestrator → ? [Stage: {state.current_stage}, Last: {state.last_agent}]")
        
        # Check for errors first
        if state.error:
            logger.debug(f"⚠️ Detected error in state: '{state.error}'. Routing to error_handler")
            state.current_stage = "error_handling"
            return "error_handler"

        # Prioritize explicit routing decision from the orchestrator agent's output
        next_agent_from_output = state.output_data.get("next_agent")
        
        if next_agent_from_output:
            logger.debug(f"🧭 Orchestrator specified next_agent: '{next_agent_from_output}'")
            if next_agent_from_output == "end":
                logger.debug(f"🏁 Workflow completion signaled by orchestrator. Ending workflow.")
                state.completed = True
                state.current_stage = "workflow_complete"
                return END
            else:
                # Map agent name to the corresponding stage for state tracking
                stage_map = {
                    "resource": "resource_assessment",
                    "engagement": "engagement_analysis",
                    "psychological": "psychological_assessment",
                    "strategy": "strategy_formulation",
                    "activity": "activity_selection",
                    "ethical": "ethical_validation",
                    "orchestrator": "orchestration_final" # Handle routing back to orchestrator
                }
                if next_agent_from_output in stage_map:
                    logger.debug(f"✅ Setting current_stage to: '{stage_map[next_agent_from_output]}'")
                    state.current_stage = stage_map[next_agent_from_output]
                    logger.debug(f"🔄 ROUTING DECISION: Orchestrator → {next_agent_from_output}")
                    return next_agent_from_output
                else:
                    # If next_agent is unknown, treat as error
                    error_msg = f"Orchestrator specified unknown next_agent: {next_agent_from_output}"
                    logger.error(f"❌ {error_msg}")
                    state.error = error_msg
                    state.current_stage = "error_handling"
                    logger.debug(f"🔄 ROUTING DECISION: Orchestrator → error_handler (unknown agent)")
                    return "error_handler"

        # Fallback logic (should ideally not be needed if orchestrator always provides next_agent)
        # This covers the initial call where output_data might be empty before the first mock runs
        if state.current_stage == "orchestration_initial":
             # On the very first entry, route to resource
             logger.debug(f"🧪 Initial orchestration detected. Default routing to resource agent.")
             logger.debug(f"🔄 ROUTING DECISION: Orchestrator → resource (initial default)")
             return "resource"

        # Additional fallback for when orchestrator doesn't provide next_agent but we're still in initial stage
        if state.last_agent == "orchestrator" and not next_agent_from_output:
            logger.debug(f"🧪 Orchestrator completed but didn't specify next_agent. Defaulting to resource.")
            logger.debug(f"🔄 ROUTING DECISION: Orchestrator → resource (fallback)")
            return "resource"

        # If no explicit next_agent and not initial stage, consider it an error
        error_msg = f"Orchestrator did not specify next_agent. Current stage: {state.current_stage}, Last agent: {state.last_agent}"
        logger.error(f"❌ {error_msg}")
        state.error = error_msg
        state.current_stage = "error_handling"
        logger.debug(f"🔄 ROUTING DECISION: Orchestrator → error_handler (missing next_agent)")
        return "error_handler"
    
    # 2. Resource & Capacity Agent
    def route_from_resource(state: WheelGenerationState):
        """Route from the Resource Agent to the next agent in the flow."""
        logger.debug(f"🔄 ROUTING - Resource → ? [Stage: {state.current_stage}, Last: {state.last_agent}]")

        # Check for errors
        if state.error:
            logger.debug(f"⚠️ Detected error in state: '{state.error}'. Routing to error_handler")
            state.current_stage = "error_handling"
            logger.debug(f"🔄 ROUTING DECISION: Resource → error_handler (error in state)")
            return "error_handler"

        # After resource assessment, go to engagement analysis
        # Accept both orchestration_initial (if stage wasn't updated) and resource_assessment
        if (state.last_agent == "resource" and
            state.current_stage in ["orchestration_initial", "resource_assessment"]):
            # According to the flow, engagement analysis happens next
            # Note: We don't require resource_context to be present as it might be empty in some cases
            logger.debug(f"✅ Resource agent completed. Proceeding to engagement analysis.")
            state.current_stage = "engagement_analysis"  # This ensures engagement agent gets the correct stage
            logger.debug(f"🔄 ROUTING DECISION: Resource → engagement")
            return "engagement"

        # Error case - unexpected state
        error_msg = f"Unexpected state in resource routing: stage={state.current_stage}, last_agent={state.last_agent}, resource_context_present={bool(state.resource_context)}"
        logger.error(f"❌ {error_msg}")
        state.error = error_msg
        state.current_stage = "error_handling"
        logger.debug(f"🔄 ROUTING DECISION: Resource → error_handler (unexpected state)")
        return "error_handler"
    
    # 3. Engagement & Pattern Analytics Agent
    def route_from_engagement(state: WheelGenerationState):
        """Route from the Engagement Agent to the next agent in the flow."""
        # Check for errors
        if state.error:
            state.current_stage = "error_handling"
            return "error_handler"

        # Simple and elegant routing condition
        if state.last_agent == "engagement":
            state.current_stage = "psychological_assessment"
            return "psychological"

        # Error case - unexpected state
        state.error = f"Unexpected state in engagement routing: stage={state.current_stage}, last_agent={state.last_agent}, engagement_analysis_present={bool(state.engagement_analysis)}"
        state.current_stage = "error_handling"
        return "error_handler"
    
    # 4. Psychological Monitoring Agent
    def route_from_psychological(state: WheelGenerationState):
        """Route from the Psychological Agent to the next agent in the flow."""
        logger.debug(f"🔄 ROUTING - Psychological → ? [Stage: {state.current_stage}, Last: {state.last_agent}]")

        # Check for errors
        if state.error:
            logger.debug(f"⚠️ Detected error in state: '{state.error}'. Routing to error_handler")
            state.current_stage = "error_handling"
            logger.debug(f"🔄 ROUTING DECISION: Psychological → error_handler (error in state)")
            return "error_handler"

        # After psychological assessment, go to strategy formulation
        if state.last_agent == "psychological":
            logger.debug(f"✅ Psychological agent completed. Proceeding to strategy formulation.")
            state.current_stage = "strategy_formulation"
            logger.debug(f"🔄 ROUTING DECISION: Psychological → strategy")
            return "strategy"

        # Error case - unexpected state
        error_msg = f"Unexpected state in psychological routing: stage={state.current_stage}, last_agent={state.last_agent}, psychological_assessment_present={bool(state.psychological_assessment)}"
        logger.error(f"❌ {error_msg}")
        state.error = error_msg
        state.current_stage = "error_handling"
        logger.debug(f"🔄 ROUTING DECISION: Psychological → error_handler (unexpected state)")
        return "error_handler"
    
    # 5. Strategy Agent
    def route_from_strategy(state: WheelGenerationState):
        """Route from the Strategy Agent to the next agent in the flow."""
        logger.debug(f"🔄 ROUTING - Strategy → ? [Stage: {state.current_stage}, Last: {state.last_agent}]")

        # Check for errors
        if state.error:
            logger.debug(f"⚠️ Detected error in state: '{state.error}'. Routing to error_handler")
            state.current_stage = "error_handling"
            logger.debug(f"🔄 ROUTING DECISION: Strategy → error_handler (error in state)")
            return "error_handler"

        # After strategy formulation, go to activity selection
        if state.last_agent == "strategy":
            logger.debug(f"✅ Strategy agent completed. Proceeding to activity selection.")
            state.current_stage = "activity_selection"
            logger.debug(f"🔄 ROUTING DECISION: Strategy → activity")
            return "activity"

        # Error case - unexpected state
        error_msg = f"Unexpected state in strategy routing: stage={state.current_stage}, last_agent={state.last_agent}, strategy_framework_present={bool(state.strategy_framework)}"
        logger.error(f"❌ {error_msg}")
        state.error = error_msg
        state.current_stage = "error_handling"
        logger.debug(f"🔄 ROUTING DECISION: Strategy → error_handler (unexpected state)")
        return "error_handler"
    
    # 6. Wheel/Activity Agent
    def route_from_activity(state: WheelGenerationState):
        """Route from the Activity Agent to the next agent in the flow."""
        logger.debug(f"🔄 ROUTING - Activity → ? [Stage: {state.current_stage}, Last: {state.last_agent}]")

        # Check for errors
        if state.error:
            logger.debug(f"⚠️ Detected error in state: '{state.error}'. Routing to error_handler")
            state.current_stage = "error_handling"
            logger.debug(f"🔄 ROUTING DECISION: Activity → error_handler (error in state)")
            return "error_handler"

        # After activity selection, go to ethical validation
        if state.last_agent == "activity":
            logger.debug(f"✅ Activity agent completed. Proceeding to ethical validation.")
            state.current_stage = "ethical_validation"
            logger.debug(f"🔄 ROUTING DECISION: Activity → ethical")
            return "ethical"

        # Error case - unexpected state
        error_msg = f"Unexpected state in activity routing: stage={state.current_stage}, last_agent={state.last_agent}, wheel_present={bool(state.wheel)}"
        logger.error(f"❌ {error_msg}")
        state.error = error_msg
        state.current_stage = "error_handling"
        logger.debug(f"🔄 ROUTING DECISION: Activity → error_handler (unexpected state)")
        return "error_handler"
    
    # 7. Ethical Oversight Agent
    def route_from_ethical(state: WheelGenerationState):
        """Route from the Ethical Agent to the next agent in the flow."""
        logger.debug(f"🔄 ROUTING - Ethical → ? [Stage: {state.current_stage}, Last: {state.last_agent}]")

        # Check for errors
        if state.error:
            logger.debug(f"⚠️ Detected error in state: '{state.error}'. Routing to error_handler")
            state.current_stage = "error_handling"
            logger.debug(f"🔄 ROUTING DECISION: Ethical → error_handler (error in state)")
            return "error_handler"

        # After ethical validation, go back to orchestrator for final integration
        if state.last_agent == "ethical":
            logger.debug(f"✅ Ethical agent completed. Proceeding to final orchestration.")
            state.current_stage = "orchestration_final"
            logger.debug(f"🔄 ROUTING DECISION: Ethical → orchestrator (final)")
            return "orchestrator"

        # Error case - unexpected state
        error_msg = f"Unexpected state in ethical routing: stage={state.current_stage}, last_agent={state.last_agent}, ethical_validation_present={bool(state.ethical_validation)}"
        logger.error(f"❌ {error_msg}")
        state.error = error_msg
        state.current_stage = "error_handling"
        logger.debug(f"🔄 ROUTING DECISION: Ethical → error_handler (unexpected state)")
        return "error_handler"
    
    # 8. Error Handler
    def route_from_error_handler(state: WheelGenerationState):
        """Route from the Error Handler to the next agent in the flow."""
        logger.debug(f"🔄 ROUTING - Error Handler → ? [Stage: {state.current_stage}, Error: {state.error and 'Yes' or 'No'}]")
        
        # If the error handler output indicates recovery is possible
        recovery_agent = None

        # If the state already shows we're coming from error_handler, we may be in a loop
        if state.last_agent == "error_handler" and state.current_stage == "error_handling":
            logger.warning("Potential loop detected! Ending workflow.")
            state.completed = True
            state.output_data["user_response"] = "I apologize, but I'm having trouble recovering from an error."
            return END
        
        if "next_agent" in state.output_data:
            recovery_agent = state.output_data["next_agent"]
            logger.debug(f"✅ Error handler specified next_agent: '{recovery_agent}'")
        elif state.output_data.get("error_handled", False):
            # If error is handled but no specific next agent, use current stage to determine
            stage_to_agent = {
                "orchestration_initial": "orchestrator",
                "resource_assessment": "resource",
                "engagement_analysis": "engagement",
                "psychological_assessment": "psychological", 
                "strategy_formulation": "strategy",
                "activity_selection": "activity",
                "ethical_validation": "ethical",
                "orchestration_final": "orchestrator"
            }
            recovery_agent = stage_to_agent.get(state.current_stage)
            logger.debug(f"ℹ️ Error handled, determining recovery agent from stage '{state.current_stage}': '{recovery_agent}'")
        
        # If we have a recovery target, proceed there
        if recovery_agent and recovery_agent != "end":
            # Clear the error state since we're recovering
            logger.debug(f"✅ Error recovery possible. Clearing error state and routing to '{recovery_agent}'")
            state.error = None
            state.error_context = None
            
            # Clear next_agent to avoid conflicts
            state.next_agent = None
            
            logger.debug(f"🔄 ROUTING DECISION: Error Handler → {recovery_agent} (recovery)")
            return recovery_agent
        
        # Cannot recover, end the workflow
        if recovery_agent == "end":
            logger.debug(f"🏁 Error handler signaled workflow end.")
        else:
            logger.error(f"❌ Cannot recover from error, no valid recovery agent determined.")
            
        state.error = state.error or "Unrecoverable error"
        state.completed = True
        state.current_stage = "workflow_complete"
        logger.debug(f"🔄 ROUTING DECISION: Error Handler → END (unrecoverable)")
        return END
    
    # Set up conditional edges for all agents
    workflow.add_conditional_edges(
        "orchestrator",
        route_from_orchestrator,
        {
            "resource": "resource",
            "engagement": "engagement",
            "psychological": "psychological",
            "strategy": "strategy",
            "activity": "activity",
            "ethical": "ethical",
            "error_handler": "error_handler",
            END: END
        }
    )
    
    workflow.add_conditional_edges(
        "resource",
        route_from_resource,
        {
            "engagement": "engagement",
            "error_handler": "error_handler"
        }
    )
    
    workflow.add_conditional_edges(
        "engagement",
        route_from_engagement,
        {
            "psychological": "psychological",
            "error_handler": "error_handler"
        }
    )
    
    workflow.add_conditional_edges(
        "psychological",
        route_from_psychological,
        {
            "strategy": "strategy",
            "error_handler": "error_handler"
        }
    )
    
    workflow.add_conditional_edges(
        "strategy",
        route_from_strategy,
        {
            "activity": "activity",
            "error_handler": "error_handler"
        }
    )
    
    workflow.add_conditional_edges(
        "activity",
        route_from_activity,
        {
            "ethical": "ethical",
            "error_handler": "error_handler"
        }
    )
    
    workflow.add_conditional_edges(
        "ethical",
        route_from_ethical,
        {
            "orchestrator": "orchestrator",
            "error_handler": "error_handler"
        }
    )
    
    workflow.add_conditional_edges(
        "error_handler",
        route_from_error_handler,
        {
            "orchestrator": "orchestrator",
            "resource": "resource",
            "engagement": "engagement",
            "psychological": "psychological",
            "strategy": "strategy",
            "activity": "activity",
            "ethical": "ethical",
            END: END
        }
    )
    
    # Set the entry point to the Orchestrator Agent, which handles initial routing
    workflow.set_entry_point("orchestrator")
    
    return workflow


def _extract_tool_usage(result: WheelGenerationState, mock_tools, use_real_tools: bool) -> Dict[str, int]:
    """
    Extract tool usage statistics from workflow result for benchmarking.

    Args:
        result: The workflow state result
        mock_tools: Mock tool registry (if used)
        use_real_tools: Whether real tools were used

    Returns:
        Dict mapping tool names to usage counts
    """
    tool_usage = {}

    if use_real_tools:
        # For real tools, we would need to implement tool usage tracking
        # This is a placeholder for future implementation
        logger.debug("Real tool usage tracking not yet implemented")
        # TODO: Implement real tool usage tracking
    else:
        # For mock tools, extract from mock registry
        if mock_tools and hasattr(mock_tools, 'call_counts'):
            tool_usage = dict(mock_tools.call_counts)
            logger.debug(f"Extracted mock tool usage: {tool_usage}")

    return tool_usage


def _extract_token_usage(result: WheelGenerationState, actual_execution_modes: Dict[str, Dict[str, bool]], use_real_llm: bool) -> Dict[str, int]:
    """
    Extract token usage statistics from workflow result for benchmarking.

    Enhanced version that provides more accurate token tracking and better cost estimation.

    Args:
        result: The workflow state result
        actual_execution_modes: Dictionary of actual execution modes used by each agent
        use_real_llm: Whether real LLM was requested

    Returns:
        Dict containing input_tokens and output_tokens counts
    """
    logger.debug(f"🔍 Extracting token usage from workflow result")
    logger.debug(f"📊 Actual execution modes: {actual_execution_modes}")
    logger.debug(f"🤖 Real LLM requested: {use_real_llm}")

    # First, try to get token usage from the workflow state if it was tracked
    if hasattr(result, 'token_usage') and isinstance(result.token_usage, dict):
        token_usage = result.token_usage
        if token_usage.get('input_tokens', 0) > 0 or token_usage.get('output_tokens', 0) > 0:
            logger.info(f"✅ Found tracked token usage: {token_usage}")
            return token_usage

    # Try to extract from LangGraph state if available
    if hasattr(result, 'get') and callable(getattr(result, 'get')):
        state_token_usage = result.get('token_usage', {})
        if isinstance(state_token_usage, dict) and (state_token_usage.get('input_tokens', 0) > 0 or state_token_usage.get('output_tokens', 0) > 0):
            logger.info(f"✅ Found token usage in LangGraph state: {state_token_usage}")
            return state_token_usage

    # If no tracked token usage, estimate based on actual LLM usage
    if use_real_llm and actual_execution_modes:
        # Count how many agents actually used real LLM
        agents_using_real_llm = sum(1 for mode in actual_execution_modes.values()
                                   if mode.get('real_llm', False))

        if agents_using_real_llm > 0:
            # Enhanced estimation based on agent types and typical usage patterns
            # Different agents have different token usage patterns
            agent_token_estimates = {
                'orchestrator': {'input': 200, 'output': 150},  # Coordination and planning
                'resource': {'input': 180, 'output': 120},      # Context analysis
                'engagement': {'input': 160, 'output': 100},    # Engagement analysis
                'psychological': {'input': 220, 'output': 180}, # Complex psychological assessment
                'strategy': {'input': 200, 'output': 160},      # Strategy formulation
                'activity': {'input': 250, 'output': 200},     # Activity generation (most complex)
                'ethical': {'input': 180, 'output': 140}       # Ethical validation
            }

            total_input = 0
            total_output = 0

            for agent_name, mode in actual_execution_modes.items():
                if mode.get('real_llm', False):
                    estimates = agent_token_estimates.get(agent_name, {'input': 150, 'output': 100})
                    total_input += estimates['input']
                    total_output += estimates['output']
                    logger.debug(f"📊 Agent '{agent_name}' estimated tokens: {estimates['input']} input, {estimates['output']} output")

            logger.info(f"💰 Estimated token usage: {agents_using_real_llm} agents using real LLM, "
                       f"{total_input} input tokens, {total_output} output tokens")

            return {
                "input_tokens": total_input,
                "output_tokens": total_output
            }

    # Default to zero if no real LLM usage
    logger.debug("❌ No real LLM usage detected, returning zero token usage")
    return {"input_tokens": 0, "output_tokens": 0}


def _extract_agent_communications(result: WheelGenerationState) -> List[Dict[str, Any]]:
    """
    Extract agent communication data from workflow result for benchmarking.

    Args:
        result: The workflow state result

    Returns:
        List of agent communication records
    """
    communications = []

    # Extract agent data from the workflow state
    agent_data = [
        ("orchestrator", getattr(result, 'output_data', {})),
        ("resource", getattr(result, 'resource_context', {})),
        ("engagement", getattr(result, 'engagement_analysis', {})),
        ("psychological", getattr(result, 'psychological_assessment', {})),
        ("strategy", getattr(result, 'strategy_framework', {})),
        ("activity", getattr(result, 'wheel', {})),
        ("ethical", getattr(result, 'ethical_validation', {}))
    ]

    for agent_name, agent_output in agent_data:
        if agent_output:
            # Enhanced communication record with metadata for Mentor processing
            communication_record = {
                "agent": agent_name,
                "output_fields": len(agent_output) if isinstance(agent_output, dict) else 1,
                "has_data": bool(agent_output),
                "data_summary": _extract_agent_data_summary(agent_name, agent_output),
                "mentor_relevant_insights": _extract_mentor_insights(agent_name, agent_output)
            }
            communications.append(communication_record)

    logger.debug(f"Extracted {len(communications)} agent communications with enhanced metadata")
    return communications


def _extract_agent_data_summary(agent_name: str, agent_output: Dict[str, Any]) -> str:
    """
    Extract a concise summary of agent output for Mentor context.

    Args:
        agent_name: Name of the agent
        agent_output: Agent's output data

    Returns:
        String summary of key insights
    """
    if not isinstance(agent_output, dict):
        return f"{agent_name} provided basic output"

    summaries = {
        "orchestrator": lambda data: f"Orchestrated workflow with {data.get('orchestration_status', 'unknown')} status",
        "resource": lambda data: f"Analyzed environment ({data.get('environment', {}).get('analyzed_type', 'unknown')}) and resources",
        "engagement": lambda data: f"Found {len(data.get('domain_preferences', {}))} domain preferences with {data.get('overall_engagement', 'unknown')} engagement",
        "psychological": lambda data: f"Assessed trust phase: {data.get('trust_phase', {}).get('phase', 'unknown')} (level: {data.get('trust_phase', {}).get('trust_level', 'unknown')})",
        "strategy": lambda data: f"Created strategy for {data.get('domain_distribution', {}).get('summary', {}).get('primary_domain', 'unknown')} domain focus",
        "activity": lambda data: f"Generated wheel with {len(data.get('items', []))} activities across {len(set(item.get('domain', 'unknown') for item in data.get('items', [])))} domains",
        "ethical": lambda data: f"Validated {len(data.get('activity_validations', []))} activities with {data.get('wheel_validation', {}).get('status', 'unknown')} status"
    }

    summary_func = summaries.get(agent_name, lambda data: f"{agent_name} completed processing")
    return summary_func(agent_output)


def _extract_mentor_insights(agent_name: str, agent_output: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extract insights relevant for Mentor's user message crafting.

    Args:
        agent_name: Name of the agent
        agent_output: Agent's output data

    Returns:
        Dictionary of insights for Mentor processing
    """
    if not isinstance(agent_output, dict):
        return {}

    insights = {}

    if agent_name == "psychological":
        trust_phase = agent_output.get('trust_phase', {})
        insights.update({
            "trust_level": trust_phase.get('trust_level'),
            "trust_phase": trust_phase.get('phase'),
            "communication_style": "supportive" if trust_phase.get('trust_level', 0) < 50 else "collaborative"
        })

    elif agent_name == "activity":
        wheel_data = agent_output
        insights.update({
            "activity_count": len(wheel_data.get('items', [])),
            "domains_covered": list(set(item.get('domain', 'unknown') for item in wheel_data.get('items', []))),
            "wheel_name": wheel_data.get('metadata', {}).get('name', 'Activity Wheel'),
            "personalization_level": "high" if len(wheel_data.get('value_propositions', {})) > 0 else "basic"
        })

    elif agent_name == "ethical":
        validation = agent_output.get('wheel_validation', {})
        insights.update({
            "safety_approved": validation.get('status') == 'Approved',
            "safety_concerns": len(agent_output.get('modification_recommendations', [])),
            "trust_phase_appropriate": True  # Ethical agent ensures this
        })

    return insights


def _extract_mentor_context(result: WheelGenerationState, actual_execution_modes: Dict[str, Dict[str, bool]]) -> Dict[str, Any]:
    """
    Extract comprehensive context for Mentor to craft the perfect user message.

    Args:
        result: The workflow state result
        actual_execution_modes: Dictionary of actual execution modes used by each agent

    Returns:
        Dictionary of context information for Mentor processing
    """
    context = {
        "workflow_summary": {
            "completed_successfully": getattr(result, 'completed', False),
            "agents_participated": list(actual_execution_modes.keys()),
            "execution_quality": "real" if any(mode.get('real_llm', False) for mode in actual_execution_modes.values()) else "mock"
        },
        "user_insights": {},
        "wheel_characteristics": {},
        "communication_guidance": {}
    }

    # Extract psychological insights for communication style
    if hasattr(result, 'psychological_assessment') and result.psychological_assessment:
        psych_data = result.psychological_assessment
        trust_phase = psych_data.get('trust_phase', {})

        context["user_insights"] = {
            "trust_level": trust_phase.get('trust_level', 50),
            "trust_phase": trust_phase.get('phase', 'Unknown'),
            "dominant_traits": psych_data.get('trait_analysis', {}).get('dominant_traits', []),
            "current_mood": psych_data.get('current_state', {}).get('mood', 'neutral'),
            "energy_level": psych_data.get('current_state', {}).get('energy_level', 'medium')
        }

        # Determine communication style based on trust level
        trust_level = trust_phase.get('trust_level', 50)
        if trust_level < 40:
            communication_style = "supportive_gentle"
        elif trust_level < 70:
            communication_style = "encouraging_balanced"
        else:
            communication_style = "collaborative_confident"

        context["communication_guidance"] = {
            "style": communication_style,
            "tone": "supportive" if trust_level < 50 else "collaborative",
            "detail_level": "simple" if trust_level < 40 else "moderate",
            "encouragement_level": "high" if trust_level < 50 else "balanced"
        }

    # Extract wheel characteristics for message crafting
    if hasattr(result, 'wheel') and result.wheel:
        wheel_data = result.wheel
        activities = wheel_data.get('activities', [])
        items = wheel_data.get('items', [])

        # Get domains covered
        domains_covered = set()
        if activities:
            domains_covered = set(activity.get('domain', 'unknown') for activity in activities)
        elif items:
            domains_covered = set(item.get('domain', 'unknown') for item in items)

        context["wheel_characteristics"] = {
            "activity_count": len(activities) or len(items),
            "domains_covered": list(domains_covered),
            "wheel_name": wheel_data.get('metadata', {}).get('name', 'Activity Wheel'),
            "personalization_level": "high" if wheel_data.get('value_propositions') else "basic",
            "challenge_level": "gentle" if context["user_insights"].get("trust_level", 50) < 50 else "moderate"
        }

    # Extract safety and ethical considerations
    if hasattr(result, 'ethical_validation') and result.ethical_validation:
        ethical_data = result.ethical_validation
        wheel_validation = ethical_data.get('wheel_validation', {})

        context["safety_context"] = {
            "approved": wheel_validation.get('status') == 'Approved',
            "safety_level": "high" if len(ethical_data.get('modification_recommendations', [])) == 0 else "moderate",
            "trust_phase_appropriate": True  # Ethical agent ensures this
        }

    return context


def _extract_workflow_insights(result: WheelGenerationState) -> Dict[str, Any]:
    """
    Extract high-level insights about the workflow execution for Mentor understanding.

    Args:
        result: The workflow state result

    Returns:
        Dictionary of workflow insights
    """
    insights = {
        "execution_summary": {
            "workflow_completed": getattr(result, 'completed', False),
            "error_occurred": bool(getattr(result, 'error', None)),
            "stages_completed": []
        },
        "personalization_quality": "unknown",
        "user_readiness_indicators": {},
        "next_interaction_suggestions": []
    }

    # Determine stages completed based on available data
    if hasattr(result, 'resource_context') and result.resource_context:
        insights["execution_summary"]["stages_completed"].append("resource_assessment")
    if hasattr(result, 'engagement_analysis') and result.engagement_analysis:
        insights["execution_summary"]["stages_completed"].append("engagement_analysis")
    if hasattr(result, 'psychological_assessment') and result.psychological_assessment:
        insights["execution_summary"]["stages_completed"].append("psychological_assessment")
    if hasattr(result, 'strategy_framework') and result.strategy_framework:
        insights["execution_summary"]["stages_completed"].append("strategy_formulation")
    if hasattr(result, 'wheel') and result.wheel:
        insights["execution_summary"]["stages_completed"].append("activity_selection")
    if hasattr(result, 'ethical_validation') and result.ethical_validation:
        insights["execution_summary"]["stages_completed"].append("ethical_validation")

    # Assess personalization quality
    personalization_indicators = 0
    if hasattr(result, 'wheel') and result.wheel:
        wheel_data = result.wheel
        if wheel_data.get('value_propositions'):
            personalization_indicators += 2
        if wheel_data.get('metadata', {}).get('trust_phase'):
            personalization_indicators += 1
        if len(wheel_data.get('activities', [])) > 0:
            personalization_indicators += 1

    if personalization_indicators >= 3:
        insights["personalization_quality"] = "high"
    elif personalization_indicators >= 2:
        insights["personalization_quality"] = "moderate"
    else:
        insights["personalization_quality"] = "basic"

    # Generate next interaction suggestions based on workflow results
    if hasattr(result, 'psychological_assessment') and result.psychological_assessment:
        trust_level = result.psychological_assessment.get('trust_phase', {}).get('trust_level', 50)
        if trust_level < 40:
            insights["next_interaction_suggestions"] = [
                "Encourage gentle exploration",
                "Emphasize safety and support",
                "Offer simple next steps"
            ]
        elif trust_level < 70:
            insights["next_interaction_suggestions"] = [
                "Encourage activity selection",
                "Provide balanced guidance",
                "Offer moderate challenges"
            ]
        else:
            insights["next_interaction_suggestions"] = [
                "Encourage autonomous choice",
                "Discuss growth opportunities",
                "Explore advanced options"
            ]

    return insights


async def run_wheel_generation_workflow(user_profile_id: Optional[str] = None,
                                      context_packet: Optional[Dict[str, Any]] = None,
                                      workflow_id: Optional[str] = None,
                                      workflow_input: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Run the wheel generation workflow for a specific user.

    This function supports both the legacy interface (individual parameters) and the new
    benchmarking interface (workflow_input dictionary) for maximum compatibility.

    Args:
        user_profile_id: The ID of the user profile (legacy interface)
        context_packet: Initial context information from the user (legacy interface)
        workflow_id: Optional workflow ID (will generate a new one if not provided)
        workflow_input: Dictionary containing all workflow parameters (new benchmarking interface)
                       Expected keys: user_profile_id, context_packet, workflow_id,
                       use_real_llm, use_real_tools, use_real_db, mock_tools

    Returns:
        Dict[str, Any]: Final state and output from the workflow, including benchmarking metadata
    """
    # Handle both legacy and new interfaces
    if workflow_input is not None:
        # New benchmarking interface - extract parameters from workflow_input
        user_profile_id = workflow_input.get("user_profile_id", user_profile_id)
        context_packet = workflow_input.get("context_packet", context_packet)
        workflow_id = workflow_input.get("workflow_id", workflow_id)
        use_real_llm = workflow_input.get("use_real_llm", False)
        use_real_tools = workflow_input.get("use_real_tools", False)
        use_real_db = workflow_input.get("use_real_db", False)
        mock_tools = workflow_input.get("mock_tools")

        logger.info(f"Running workflow with benchmarking interface: real_llm={use_real_llm}, "
                   f"real_tools={use_real_tools}, real_db={use_real_db}")
    else:
        # Legacy interface - use provided parameters
        use_real_llm = False
        use_real_tools = False
        use_real_db = False
        mock_tools = None

        logger.info("Running workflow with legacy interface (all operations mocked)")

    # Validate required parameters
    if not user_profile_id:
        raise ValueError("user_profile_id is required")
    if not context_packet:
        raise ValueError("context_packet is required")
    # Create the workflow
    workflow = create_wheel_generation_graph(user_profile_id)
    
    # Compile the workflow
    app = workflow.compile()
    
    # Set up the initial state
    if not workflow_id:
        workflow_id = str(uuid.uuid4())
        
    # Create initial state with user details, context, and execution mode configuration
    initial_state = WheelGenerationState(
        workflow_id=workflow_id,
        user_profile_id=user_profile_id,
        context_packet=context_packet,
        user_ws_session_name=context_packet.get('user_ws_session_name'),
        use_real_llm=use_real_llm,
        use_real_tools=use_real_tools,
        use_real_db=use_real_db,
        mock_tools=mock_tools
    )
    
    # Log workflow initiation
    logger.info(f"🚀 Starting wheel generation workflow {workflow_id} for user {user_profile_id}")
    
    try:
        # Execute the workflow
        result = await app.ainvoke(initial_state)
        logger.info(f"✅ Completed wheel generation workflow {workflow_id}")
        
        # Format output data for the response
        output_data = {}
        
        # Include the wheel in output_data if available
        wheel_data = result.get('wheel')
        if wheel_data:
            output_data["wheel"] = wheel_data
            # Check if wheel has activities (new format) or items (old format)
            activity_count = 0
            if isinstance(wheel_data, dict):
                if 'activities' in wheel_data:
                    activity_count = len(wheel_data.get('activities', []))
                elif 'items' in wheel_data:
                    activity_count = len(wheel_data.get('items', []))
            logger.debug(f"📊 Wheel included in output with {activity_count} activities")
        else:
            logger.debug(f"❌ No wheel data found in result")

        # Include other important workflow outputs
        for key in ['strategy_framework', 'psychological_assessment', 'ethical_validation']:
            if key in result and result[key]:
                output_data[key] = result[key]
                logger.debug(f"📋 Included {key} in output_data")

        # Include user_response from output_data if available
        logger.debug(f"🔍 Final result output_data: {getattr(result, 'output_data', 'NOT_FOUND')}")

        # Try to find user_response in multiple places
        user_response = None

        # First, try the output_data field
        if hasattr(result, 'output_data') and isinstance(result.output_data, dict):
            if 'user_response' in result.output_data:
                user_response = result.output_data['user_response']
                logger.debug(f"💬 Found user_response in output_data: {user_response[:50]}...")
            else:
                logger.debug(f"❌ No 'user_response' found in result.output_data: {result.output_data}")

        # If not found, try to extract from the workflow state directly (LangGraph state)
        if not user_response and hasattr(result, 'get'):
            # Try to get user_response directly from the state
            if 'user_response' in result:
                user_response = result['user_response']
                logger.debug(f"💬 Found user_response in state: {user_response[:50]}...")
            # Also try to get it from output_data in the state
            elif 'output_data' in result and isinstance(result['output_data'], dict):
                if 'user_response' in result['output_data']:
                    user_response = result['output_data']['user_response']
                    logger.debug(f"💬 Found user_response in state.output_data: {user_response[:50]}...")

        # If still not found, check if it's in the last agent's output
        logger.debug(f"🔍 Checking last_agent: {getattr(result, 'last_agent', 'NOT_FOUND')}")
        logger.debug(f"🔍 Checking completed: {getattr(result, 'completed', 'NOT_FOUND')}")

        if not user_response:
            # The orchestrator always generates a user response at the end, so use a default
            # This is a temporary fix until we can properly capture the orchestrator's final output
            user_response = "I've prepared some activities for you based on your preferences and context."
            logger.debug(f"💬 Using default user response since orchestrator completed the workflow")

        if user_response:
            output_data["user_response"] = user_response
            logger.debug(f"✅ User response included in final output: {user_response[:50]}...")
        else:
            logger.debug(f"❌ No user_response found anywhere in the workflow result")
                
        # Calculate actual execution mode based on what was actually used
        # The result from LangGraph is an AddableValuesDict, so access the field as a dictionary key
        actual_execution_modes = result.get('actual_execution_modes', {})

        logger.debug(f"🔍 Final result type: {type(result)}")
        logger.debug(f"🔍 Final result keys: {list(result.keys()) if hasattr(result, 'keys') else 'No keys method'}")
        logger.debug(f"🔍 Final result actual_execution_modes value: {actual_execution_modes}")

        logger.debug(f"Extracted actual_execution_modes: {actual_execution_modes}")

        # Determine if any agent actually used real components
        any_real_llm = any(mode.get('real_llm', False) for mode in actual_execution_modes.values())
        any_real_db = any(mode.get('real_db', False) for mode in actual_execution_modes.values())
        any_real_tools = any(mode.get('real_tools', False) for mode in actual_execution_modes.values())

        logger.debug(f"Calculated execution mode flags: LLM={any_real_llm}, DB={any_real_db}, Tools={any_real_tools}")

        # Determine overall execution mode
        if any_real_llm or any_real_db or any_real_tools:
            execution_mode = "real"
        else:
            execution_mode = "mock"

        # Create the workflow result with benchmarking metadata and enhanced Mentor context
        workflow_result = {
            "workflow_id": workflow_id,
            "user_profile_id": user_profile_id,
            "completed": getattr(result, 'completed', True),
            "output_data": output_data,
            "session_timestamp": context_packet.get('session_timestamp'),
            "user_ws_session_name": context_packet.get('user_ws_session_name'),
            "workflow_type": "wheel_generation",  # Explicitly include for result handling

            # Benchmarking metadata (based on actual usage, not just requested)
            "execution_mode": execution_mode,
            "real_llm_used": any_real_llm,
            "real_tools_used": any_real_tools,
            "real_db_used": any_real_db,

            # Include requested vs actual execution mode for debugging
            "requested_execution_mode": {
                "use_real_llm": use_real_llm,
                "use_real_tools": use_real_tools,
                "use_real_db": use_real_db
            },
            "actual_execution_modes": actual_execution_modes,

            # Tool usage tracking (for benchmarking)
            "tool_usage": _extract_tool_usage(result, mock_tools, use_real_tools),

            # Token usage tracking (for benchmarking)
            "token_usage": _extract_token_usage(result, actual_execution_modes, use_real_llm),

            # Agent communications (for benchmarking)
            "agent_communications": _extract_agent_communications(result),

            # Enhanced metadata for Mentor processing
            "mentor_context": _extract_mentor_context(result, actual_execution_modes),
            "workflow_insights": _extract_workflow_insights(result)
        }
        
        return workflow_result
            
    except Exception as e:
        logger.error(f"❌ Error in wheel generation workflow {workflow_id}: {str(e)}", exc_info=True)
        # Return error state
        return {
            "error": str(e),
            "workflow_id": workflow_id,
            "completed": False,
            "user_profile_id": user_profile_id,
            "user_ws_session_name": context_packet.get('user_ws_session_name'),
            "workflow_type": "wheel_generation",
            "output_data": {
                "user_response": "I encountered an issue while processing your request. Please try again."
            }
        }
