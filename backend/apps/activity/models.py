from django.db import models
from apps.common.fields import *
from django.contrib.contenttypes.fields import GenericForeign<PERSON>ey, GenericRelation
from django.contrib.contenttypes.models import ContentType
import slugify

###############################################################################
# Tagging system
###############################################################################
class Tag(models.Model):
    """
    Represents a reusable label that can be applied to any model in the system.
    
    This model provides a centralized vocabulary of tags that can be used
    to categorize and find related items across different entity types.

    To make any model taggable, add a GenericRelation field:
    class ModelX(models.Model):
    # Existing fields...
    tags = GenericRelation(
        'TaggedItem',
        content_type_field='content_type',
        object_id_field='object_id',
        related_query_name='model_x'
    )
    """
    name = models.CharField(max_length=100, unique=True)
    slug = models.SlugField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    
    def __str__(self):
        return self.name
        
    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)

class TaggedItem(models.Model):
    """
    Associates a tag with any model instance in the system.
    
    This model uses Django's ContentType framework to create a flexible
    relationship between tags and any model, allowing for unified tagging.
    """
    tag = models.ForeignKey(Tag, on_delete=models.CASCADE, related_name="tagged_items")
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.CharField(max_length=255)
    content_object = GenericForeignKey('content_type', 'object_id')
    
    class Meta:
        unique_together = ['tag', 'content_type', 'object_id']
        indexes = [
            models.Index(fields=['content_type', 'object_id']),
            models.Index(fields=['tag']),
        ]

###############################################################################
# Generic Domain & Relationship Models
###############################################################################

class GenericDomain(models.Model):
    """
    A catalog of generic domains that can be associated with various entities.
    Allows for hierarchical categorization and metadata about each domain.
    """
    class PrimaryCategoryChoices(models.TextChoices):
        PHYSICAL = 'physical', 'Physical'
        SOCIAL = 'social', 'Social'
        CREATIVE = 'creative', 'Creative'
        INTELLECTUAL = 'intellectual', 'Intellectual'
        REFLECTIVE = 'reflective', 'Reflective'
        EMOTIONAL = 'emotional', 'Emotional'
        SPIRITUAL_EXISTENTIAL = 'spiritual_existential', 'Spiritual/Existential'
        EXPLORATORY_ADVENTUROUS = 'exploratory_adventurous', 'Exploratory/Adventurous'
        PRODUCTIVE_PRACTICAL = 'productive_practical', 'Productive/Practical'
        LEISURE_RECREATIONAL = 'leisure_recreational', 'Leisure/Recreational'
    
    id = models.AutoField(primary_key=True)
    code = models.CharField(max_length=50, unique=True, 
                            help_text="Unique identifier code for the domain (e.g., 'creative_artistic')")
    name = models.CharField(max_length=100, 
                           help_text="Display name of the domain (e.g., 'Artistic Creation')")
    description = models.TextField(blank=True, 
                                  help_text="Detailed description of the domain and associated activities")
    primary_category = models.CharField(
                                     max_length=30,
                                     choices=PrimaryCategoryChoices.choices,
                                     blank=True,
                                     help_text="Primary category - only applicable for top-level domains")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['primary_category', 'name']
        verbose_name = "Generic Domain"
        verbose_name_plural = "Generic Domains"
    
    def __str__(self):
        return self.name
        
    def save(self, *args, **kwargs):
            
        super().save(*args, **kwargs)

class EntityDomainRelationship(models.Model):
    """
    Maps the relationship between any entity (via ContentType) and domains with a strength indicator.
    Uses Django's ContentType framework to create a generic relationship.
    """
    class RelationshipStrength(models.IntegerChoices):
        MINIMAL = 10, 'Minimal'
        MODERATE = 30, 'Moderate'
        SIGNIFICANT = 70, 'Significant'
        PRIMARY = 100, 'Primary'

    # Generic relationship fields
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.CharField(max_length=255)  # Increased max_length to accommodate potentially longer object IDs
    content_object = GenericForeignKey('content_type', 'object_id')
    
    # Domain relationship fields
    domain = models.ForeignKey(
        'GenericDomain',
        on_delete=models.CASCADE,
        help_text="Domain associated with the entity"
    )
    strength = models.PositiveSmallIntegerField(
        choices=RelationshipStrength.choices,
        default=RelationshipStrength.MODERATE,
        help_text="How strongly this activity relates to this domain"
    )

    class Meta:
        unique_together = ['content_type', 'object_id', 'domain']
        verbose_name = "Entity-Domain Relationship"
        verbose_name_plural = "Entity-Domain Relationships"
        indexes = [
            models.Index(fields=['content_type', 'object_id']),
            models.Index(fields=['domain']),
        ]

    def __str__(self):
        return f"{self.content_object} - {self.domain.name} ({self.get_strength_display()})"


class BaseActivity(models.Model):
    """
    Abstract base class that holds common fields for activity models.
    
    This class provides shared attributes that describe the basic properties
    of an activity, such as its name, description, creation date, duration range,
    instructions, and physical/social requirements.
    
    Attributes:
        name (CharField): The name of the activity.
            Example: "Nature Walk Meditation"
        description (TextField): A brief summary of the activity that explains its purpose and benefits.
            Example: "A mindful walking exercise in nature to help connect with your surroundings and reduce overthinking."
        domain_relationships (GenericRelation): Links to the EntityDomainRelationship model, defining the connection to GenericDomains with strength.
            Example: An activity may be linked to domains such as "mindfulness" (primary), "nature", and "physical_activity" with varying relationship strengths via EntityDomainRelationship records.
        created_on (DateField): The date when the activity was added to the catalog.
            Example: "2025-03-10"
        duration_range (CharField): The expected time commitment range for completing the activity.
            Example: "20-40 minutes"
        instructions (TextField): Step-by-step directions for performing the activity.
            Example: "Find a quiet natural area, preferably with trees or water. Walk slowly, focusing on each step. Notice 5 things you can see, 4 things you can hear, 3 things you can touch, 2 things you can smell, and 1 thing you can taste. When your mind starts to race, gently bring attention back to your senses."
        social_requirements (JSONField): 
            Example: {"min_particpants": 1, "max_particpants": 4, "not_particpants": 3} _ok for 1, 2 or 4 particpants. Not 3._
    """
    name = models.CharField(max_length=255, help_text="The name of the activity.")
    description = models.TextField(help_text="A brief summary of the activity.")
    created_on = models.DateField(help_text="Date the activity was added.")
    duration_range = models.CharField(max_length=50, help_text="Time range for the activity.")
    instructions = models.TextField(help_text="Step-by-step directions for the activity.", blank=True)
    social_requirements = models.JSONField(help_text="JSON specifying social requirements.")

    class Meta:
        abstract = True

    def get_primary_domain(self):
        """Return the primary domain of this activity, if any"""
        try:
            relationship = self.domain_relationships.filter( # Use the renamed GenericRelation field name
                strength=EntityDomainRelationship.RelationshipStrength.PRIMARY # Use the renamed model name
            ).first()
            return relationship.domain if relationship else None
        except Exception:
            return None
            
    def get_domains_by_strength(self, min_strength=None):
        """
        Return related domains ordered by relationship strength (strongest first).
        Optionally filter by minimum strength.
        """
        relationships = self.domain_relationships.all() # Use the renamed GenericRelation field name
        if min_strength is not None:
            relationships = relationships.filter(strength__gte=min_strength)
        # Return the domains themselves, ordered by the relationship strength
        return GenericDomain.objects.filter(
            entitydomainrelationship__in=relationships
        ).order_by('-entitydomainrelationship__strength').distinct()

class GenericActivityManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset()
    
    # This method seems less relevant now as 'domain' M2M is removed.
    # Keeping it commented out for now, might need removal or rethink.
    # def for_user_with_domains(self, user_profile):
    #     """Get activities for a user with preloaded domains."""
    #     return self.filter(user_profile=user_profile).prefetch_related('domain_relationships__domain')

class GenericActivity(BaseActivity):
    """
    A generic activity serves as a template in the activity catalog from which personalized activities are created. 
    This model holds essential information about the activity, forming the foundation for tailored user experiences.

    Attributes:
        name (CharField) *_inherited_:
            Example: "Nature Walk Meditation"
        description (TextField) *_inherited_: 
            Example: "A mindful walking exercise in nature to help connect with your surroundings and reduce overthinking."
        instructions (TextField) *_inherited_: 
            Example: "Find a quiet natural area, preferably with trees or water. Walk slowly, focusing on each step. Notice 5 things you can see, 4 things you can hear, 3 things you can touch, 2 things you can smell, and 1 thing you can taste. When your mind starts to race, gently bring attention back to your senses."
        code (CharField):
            Example: "refl_mind_walking"
        domain_relationships (GenericRelation): Links to the EntityDomainRelationship model, defining the connection to GenericDomains with strength.
            Example: An activity may be linked to domains such as "mindfulness" (primary), "nature", and "physical_activity" with varying relationship strengths via EntityDomainRelationship records.
    """
    code = models.CharField(max_length=50, unique=True, help_text="Unique, human-readable code for this activity (e.g., 'refl_mind_walking')")
    # Removed direct M2M 'domain' field. Use domain_relationships instead.
    domain_relationships = GenericRelation(
        'EntityDomainRelationship', # Renamed target model
        content_type_field='content_type',
        object_id_field='object_id',
        related_query_name='generic_activity', # Keep related_query_name specific
        help_text="Relationship mapping this activity to GenericDomains with strength."
    )
    tags = GenericRelation(
        'TaggedItem',
        content_type_field='content_type',
        object_id_field='object_id',
        related_query_name='generic_activity'
    )
    objects = GenericActivityManager()

    def get_tailored_activities(self, user_profile=None):
        """Get tailored activities for this generic activity with optimized queries."""
        queryset = self.tailored_activities.all()
        if user_profile:
            queryset = queryset.filter(user_profile=user_profile)
        return queryset.select_related('user_profile')

    class Meta:
        ordering = ['name']
        indexes = [
            models.Index(fields=['code']),  # Add index for faster lookups by code
        ]

    def __str__(self):
        return self.name
    
    
class ActivityTailoredManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().select_related('user_profile', 'generic_activity')
    
    def with_full_relations(self):
        """Get queryset with all commonly needed relations prefetched."""
        return self.get_queryset().prefetch_related(
            'domain_relationships__domain',
            'influences__content_type',
            'user_requirements__content_type',
            'tags__tag'
        )

class ActivityTailored(BaseActivity):
    """
    Represents a personalized activity tailored to a specific user's context, preferences, and growth goals.

    This model extends the GenericActivity by customizing it for an individual user, taking into account 
    their psychological profile, environmental context, resources, and developmental goals. ActivityTailored 
    objects are what actually appear on the activity wheel for users to select.

    Attributes:
        user_profile (ForeignKey): The user for whom the activity is tailored.
        generic_activity (ForeignKey): The base generic activity template being customized.
        name (CharField) *_inherited_: 
            Example: "Farm Boundary Exploration Walk"
        description (TextField) *_inherited_: 
            Example: "A guided walk around the perimeter of your friend's farm in Lyon, focusing on both the natural environment and your internal emotional landscape. This activity helps break your pattern of overthinking by connecting physical movement with mindful observation."
        instructions (TextField) *_inherited_: 
            Example: "1. Begin at the farm's main entrance. 2. Walk clockwise around the boundary, focusing on the sensations in your feet with each step. 3. At each corner of the property, stop and take 5 deep breaths while observing something you've never noticed before. 4. Mentally note one abandoned project thought that arises, then intentionally return focus to your surroundings. 5. Complete the circuit and journal three observations about what you discovered."
        domain_relationships (GenericRelation): Links to the EntityDomainRelationship model, defining the connection to GenericDomains with strength.
            Example: An activity may be linked to domains such as "mindfulness" (primary), "nature", and "physical_activity" with varying relationship strengths via EntityDomainRelationship records.
        base_challenge_rating (IntegerField): 
            Example: 40
        social_requirements (JSONField): 
            Example: {"interaction_level": "solo", "assertiveness": "low"}
        challengingness (JSONField): 
            Example: {"openness": 65, "conscientiousness": 35, "extraversion": 20, "agreeableness": 45, "neuroticism": 55}
        version (IntegerField): 
            Example: 1
        tailorization_level (IntegerField): 
            Example: 75
    """
    user_profile = models.ForeignKey("user.UserProfile", on_delete=models.CASCADE, related_name="%(class)s_tailored_activities")
    generic_activity = models.ForeignKey(GenericActivity, on_delete=models.CASCADE, related_name="%(class)s_tailored_activities")
    base_challenge_rating = models.IntegerField(help_text="Overall difficulty level from 0-100, calibrated for the user's current trust level and growth stage.")
    challengingness = models.JSONField(help_text="JSON mapping challenge levels across the Big Five personality dimensions for gap analysis.")
    version = models.IntegerField(help_text="Sequential version number tracking revisions to this tailored activity.")
    tailorization_level = models.IntegerField(help_text="Metric from 0-100 indicating how extensively the activity was customized from its generic template.")
    # Removed direct M2M 'domain' field. Use domain_relationships instead.
    domain_relationships = GenericRelation(
        'EntityDomainRelationship', # Renamed target model
        content_type_field='content_type',
        object_id_field='object_id',
        related_query_name='tailored_activity', # Keep related_query_name specific
        help_text="Relationship mapping this tailored activity to GenericDomains with strength."
    )
    tags = GenericRelation(
        'TaggedItem',
        content_type_field='content_type',
        object_id_field='object_id',
        related_query_name='activity_tailored'
    )
    objects = ActivityTailoredManager()

    class Meta:
        ordering = ['-created_on']
        constraints = [
            models.UniqueConstraint(
                fields=['user_profile', 'generic_activity', 'version'],
                name='unique_activity_version'
            )
        ]

    def __str__(self):
        return self.name

class ActivityTailoredQueryIndexes(models.Model):
    """
    Materialized query indexes for rapid activity recommendation lookups.
    
    This model stores pre-computed indexes for the wheel selection algorithm,
    significantly improving query performance for activity recommendations.
    It's updated asynchronously via Celery tasks when activities or user profiles change.
    
    Attributes:
        activity_tailored (OneToOneField): The related tailored activity
        user_profile_id (UUIDField): Indexed ID of the user profile for faster joins
        challenge_score (IntegerField): Indexed challenge score for filtering
        domain_primary (CharField): Primary domain code for domain-specific queries
        domain_categories (JSONField): {"physical": 80, "creative": 30} mapping of domain categories to relevance
        hexaco_profile (JSONField): {"openness": 60, "conscientiousness": 70, ...} mapping of HEXACO traits to scores
        suitable_environments (JSONField): [{"env_id": "uuid", "compatibility": 90}, ...] list of compatible environments
        last_updated (DateTimeField): When this index was last refreshed
    """
    activity_tailored = models.OneToOneField(
        'ActivityTailored',
        on_delete=models.CASCADE,
        related_name='query_indexes'
    )
    user_profile_id = models.UUIDField(db_index=True, help_text="Indexed foreign key to user profile for faster joins")
    challenge_score = models.IntegerField(db_index=True, help_text="Pre-computed overall challenge score (0-100)")
    domain_primary = models.CharField(max_length=50, db_index=True, help_text="Primary domain code for this activity")
    domain_categories = models.JSONField(help_text="Mapping of domain categories to relevance scores (0-100)")
    hexaco_profile = models.JSONField(help_text="Activity's HEXACO trait requirements as a score mapping (0-100)")
    suitable_environments = models.JSONField(help_text="List of environment UUIDs with compatibility scores (0-100)")
    last_updated = models.DateTimeField(auto_now=True, help_text="When this index was last refreshed")
    
    class Meta:
        verbose_name = "Activity Query Index"
        verbose_name_plural = "Activity Query Indexes"
        indexes = [
            models.Index(fields=['user_profile_id', 'challenge_score']),
            models.Index(fields=['user_profile_id', 'domain_primary']),
            # Add composite index for common wheel queries
            models.Index(fields=['user_profile_id', 'challenge_score', 'domain_primary'], name='wheel_selection_idx'),
        ]
    
    def __str__(self):
        return f"Query Index for {self.activity_tailored.name}"
    
    def update_from_activity(self):
        """
        Updates the index values based on the current state of the related activity.
        Call this method after significant changes to the activity.
        """
        activity = self.activity_tailored
        
        # Set user profile ID for faster joins
        self.user_profile_id = activity.user_profile.id
        
        # Set challenge score
        self.challenge_score = activity.base_challenge_rating
        
        # Set primary domain (using the updated get_primary_domain method which accesses the relationship)
        primary_domain_obj = activity.get_primary_domain() # This now returns a GenericDomain object
        self.domain_primary = primary_domain_obj.code if primary_domain_obj else ""
        
        # Build domain categories mapping from the relationships
        self.domain_categories = {}
        # Access relationships via the renamed GenericRelation field
        for rel in activity.domain_relationships.select_related('domain').all():
            # Access the domain object through the relationship
            category = rel.domain.primary_category 
            if category:
                # Use maximum relevance if multiple domains in same category (using relationship strength)
                current = self.domain_categories.get(category, 0)
                self.domain_categories[category] = max(current, rel.strength)
        
        # Extract HEXACO profile from challengingness field
        self.hexaco_profile = activity.challengingness
        
        # Determine suitable environments
        # This would be more complex in practice, potentially querying environment compatibility
        self.suitable_environments = []
        
        self.save()

class ActivityInfluencedBy(models.Model):
    """
    Represents the influence of various entities (e.g., goals, inspirations) on a tailored activity.

    This model tracks how external entities like goals or inspirations influence the design or 
    difficulty of a tailored activity. It captures the strength of that influence as well as any notes.

    Attributes:
        activity_tailored (ForeignKey): The tailored activity being influenced.
        content_type (ForeignKey): A ForeignKey to Django's ContentType model that identifies which model class the related object belongs to.
            Example: userGoal.id, inspiration.id, belief.id...
        object_id (UUIDField): Stores the primary key value of the specific related object
        influencing_entity (ForeignKey): This is a GenericForeignKey that combines content_type and object_id to provide a direct reference to the actual object. Django accessor property (not a database field) that allows direct access the related object in the code
        influence_strength (IntegerField): 
            Example: 75
        note (TextField): 
            Example: "This activity directly supports Philipp's goal of breaking his cycle of abandoned projects by creating a small, completable task with clear boundaries and a defined endpoint."

    """
    activity_tailored = models.ForeignKey(ActivityTailored, on_delete=models.CASCADE, related_name="influences")
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.UUIDField(help_text="UUID of the influencing entity")
    influencing_entity = GenericForeignKey('content_type', 'object_id')
    influence_strength = ValidatedRangeField(help_text="Numerical rating from 0-100 indicating how strongly this entity shaped the activity design.")
    note = models.TextField(help_text="Explanation of how the referenced entity influenced the activity's design, challenge level, or framing.")

    class Meta:
        indexes = [
            models.Index(fields=["content_type", "object_id"]),
            models.Index(fields=["activity_tailored"]),
        ]
        constraints = [
            models.UniqueConstraint(
                fields=['activity_tailored', 'content_type', 'object_id'],
                name='unique_activity_influence'
            )
        ]

    def __str__(self):
        return f"Influence on {self.activity_tailored.name}"


###############################################################################
# Activity Requirement Models
###############################################################################
"""
When creating activity requirements, consider both:

1. Personality Trait Requirements - aspects of the HEXACO model that make an activity 
   naturally more comfortable or challenging based on personality

2. Skill Requirements - specific learned abilities needed to perform the activity,
   independent of personality traits

For example, a public speaking activity might require:
- Extraversion trait (personality dimension that affects comfort level)
- Communication skill (learned ability to structure and deliver speeches)
"""

class GenericActivityResourceRequirement(models.Model):
    """
    Associates a GenericActivity with the necessary resource requirements.

    This model links a generic activity to the resources it requires. Each activity may need 
    specific resources, and this model ensures that the requirements are clearly defined.

    Attributes:
        generic_activity (ForeignKey): The associated generic activity.
        resource_base (ForeignKey): The required resource from GenericResource.
        quantity_required (IntegerField): 
            Example: 2
        optional (BooleanField): 
            Example: False

    """
    generic_activity = models.ForeignKey(GenericActivity, on_delete=models.CASCADE, related_name="resource_requirements")
    resource_base = models.ForeignKey("user.GenericResource", on_delete=models.CASCADE, related_name="activity_requirements")
    quantity_required = models.IntegerField(help_text="Numerical quantity of the resource needed to complete the activity.")
    optional = models.BooleanField(default=False, help_text="Whether the resource is required (False) or merely suggested (True) for activity completion.")

    class Meta:
        ordering = ['generic_activity']

    def __str__(self):
        return f"Resource Req for {self.generic_activity.name}"


class GenericActivityUserRequirement(models.Model):
    """
    A flexible model connecting GenericActivity to different user-related models.
    
    This model uses a generic foreign key approach to link activities to various
    user requirements like traits, skills, and limitations, allowing for a unified
    way to handle all user requirements.
    
    Attributes:
        generic_activity (ForeignKey): The generic activity with the requirement.
        content_type (ForeignKey): The type of the required entity (GenericTrait, GenericSkill, etc.).
        object_id (IntegerField): The primary key of the specific required entity.
        required_entity (GenericForeignKey): The required entity object (trait, skill, etc.).
        level_required (IntegerField): Minimum level (0-100) needed for the activity.
        optional (BooleanField): Whether this requirement is essential (False) or helpful but not required (True).
        impact_type (CharField): How this requirement impacts the activity (requirement, limitation, etc.).
        notes (TextField): Additional context or adaptation notes.
    """
    class ImpactType(models.TextChoices):
        REQUIREMENT = 'requirement', 'Requirement'           # Something needed to perform the activity
        ENHANCEMENT = 'enhancement', 'Enhancement'          # Something that improves the experience
        LIMITATION = 'limitation', 'Limitation'             # Something that may limit participation
        ADAPTATION = 'adaptation', 'Adaptation'             # Adaptation for specific user needs
    
    generic_activity = models.ForeignKey(
        'activity.GenericActivity', 
        on_delete=models.CASCADE,
        related_name="user_requirements",
        help_text="The generic activity that has this requirement."
    )
    
    # Generic Foreign Key fields
    content_type = models.ForeignKey(
        ContentType, 
        on_delete=models.CASCADE,
        help_text="The type of entity required (trait, skill, limitation, etc.)."
    )
    object_id = models.PositiveIntegerField(
        help_text="The ID of the specific entity instance."
    )
    required_entity = GenericForeignKey('content_type', 'object_id')
    
    # Requirement details
    level_required = ValidatedRangeField(
        help_text="Minimum level (0-100) needed for the activity, used for gap analysis.",
        null=True, 
        blank=True  # Allow null for cases like limitations where level might not apply
    )
    optional = models.BooleanField(
        default=False, 
        help_text="Whether this requirement is essential (False) or helpful but not required (True)."
    )
    impact_type = models.CharField(
        max_length=20,
        choices=ImpactType.choices,
        default=ImpactType.REQUIREMENT,
        help_text="How this entity impacts the activity."
    )
    notes = models.TextField(
        blank=True,
        help_text="Additional context, adaptation suggestions, or other relevant notes."
    )

    class Meta:
        ordering = ['generic_activity', 'content_type', 'object_id']
        unique_together = ['generic_activity', 'content_type', 'object_id']
        verbose_name = "Generic Activity User Requirement"
        verbose_name_plural = "Generic Activity User Requirements"

    def __str__(self):
        entity_name = str(self.required_entity)
        if hasattr(self.required_entity, 'name'):
            entity_name = self.required_entity.name
        elif hasattr(self.required_entity, 'description'):
            entity_name = self.required_entity.description[:30]
            
        return f"{self.get_impact_type_display()} for {self.generic_activity.name}: {entity_name}"
    
    @property
    def entity_type_name(self):
        """Returns a human-readable name for the content type."""
        return self.content_type.model_class().__name__
        
    def has_adaptation(self):
        """Check if this requirement has adaptation notes."""
        return bool(self.notes) and 'adapt' in self.notes.lower()
    
class GenericActivityUserRequirementSummary(models.Model):
    """
    Provides an overview of all user requirements for a GenericActivity.
    
    This model acts as a summary cache of all requirements for quick filtering
    and recommendation calculations without needing to join multiple tables.
    
    Attributes:
        generic_activity (OneToOneField): The associated generic activity.
        difficulty_rating (IntegerField): Overall difficulty score (0-100).
        requirements_summary (JSONField): Summary of all requirements.
        limitations_summary (JSONField): Summary of all limitations and adaptations.
        last_updated (DateTimeField): When this summary was last updated.
    """
    generic_activity = models.OneToOneField(
        'activity.GenericActivity',
        on_delete=models.CASCADE,
        related_name="user_requirements_summary",
        help_text="The generic activity this summary is for."
    )
    difficulty_rating = ValidatedRangeField(
        help_text="Overall difficulty rating (0-100) based on all requirements."
    )
    requirements_summary = models.JSONField(
        help_text="Summary of all requirements categorized by type."
    )
    limitations_summary = models.JSONField(
        help_text="Summary of how limitations affect this activity and available adaptations."
    )
    last_updated = models.DateTimeField(
        auto_now=True,
        help_text="When this summary was last updated."
    )

    class Meta:
        ordering = ['generic_activity']
        verbose_name = "Generic Activity User Requirement Summary"
        verbose_name_plural = "Generic Activity User Requirement Summaries"

    def __str__(self):
        return f"Requirement Summary for {self.generic_activity.name}"

    def update_summary(self):
        """
        Updates all summary fields based on the current requirements.
        
        This method should be called whenever requirements are added, modified, or removed.
        It aggregates requirements by type and calculates the overall difficulty rating.
        """
        requirements = self.generic_activity.user_requirements.all()
        
        # Initialize summaries
        req_summary = {}
        lim_summary = {}
        
        # Process requirements
        for req in requirements:
            entity_type = req.entity_type_name
            
            # Handle based on impact type
            if req.impact_type == GenericActivityUserRequirement.ImpactType.LIMITATION:
                if entity_type not in lim_summary:
                    lim_summary[entity_type] = []
                
                lim_summary[entity_type].append({
                    'id': req.object_id,
                    'name': str(req.required_entity),
                    'level': req.level_required,
                    'has_adaptation': req.has_adaptation(),
                    'notes': req.notes
                })
            else:
                if entity_type not in req_summary:
                    req_summary[entity_type] = []
                
                req_summary[entity_type].append({
                    'id': req.object_id,
                    'name': str(req.required_entity),
                    'level': req.level_required,
                    'optional': req.optional,
                    'impact_type': req.impact_type
                })
        
        # Calculate difficulty rating based on required levels
        required_levels = [
            req.level_required for req in requirements 
            if req.level_required is not None and not req.optional
        ]
        
        if required_levels:
            self.difficulty_rating = int(sum(required_levels) / len(required_levels))
        else:
            self.difficulty_rating = 0
            
        # Update summary fields
        self.requirements_summary = req_summary
        self.limitations_summary = lim_summary
        self.save()


class GenericActivityEnvRequirement(models.Model):
    """
    Defines environmental requirements for a GenericActivity.

    This model outlines the environmental factors necessary to perform a generic activity, 
    such as the type of environment or specific settings required.

    Attributes:
        generic_activity (ForeignKey): The associated generic activity.
        env (ForeignKey): The required GenericEnvironment.
        optional (BooleanField): 
            Example: False

    """
    generic_activity = models.ForeignKey(GenericActivity, on_delete=models.CASCADE, related_name="env_requirements")
    env = models.ForeignKey("user.GenericEnvironment", on_delete=models.CASCADE, related_name="activity_env_requirements")
    optional = models.BooleanField(default=False, help_text="Whether this environmental requirement is mandatory (False) or preferable but not essential (True).")

    class Meta:
        ordering = ['generic_activity']

    def __str__(self):
        return f"Env Req for {self.generic_activity.name}"


class ActivityUserRequirement(models.Model):
    """
    Connects a tailored activity with required user resources such as skills, traits, or limitations.
    Uses ContentType framework to create flexible relationships to different user resource types.

    This model defines the resources that a user must have, such as specific skills or traits, 
    in order to participate in a tailored activity. It specifies whether these resources are required 
    or optional, and the level of proficiency needed.

    Attributes:
        activity_tailored (ForeignKey): The tailored activity that has this requirement.
        content_type (ForeignKey): The type of the required resource (Skill, Trait, etc.).
        object_id (UUIDField): The UUID of the specific required resource.
        resource (GenericForeignKey): The actual resource object (skill, trait, etc.).
        required_level (IntegerField): Minimum proficiency level needed (0-100).
        quantity_required (IntegerField): Number of instances or amount of the resource needed.
        optional (BooleanField): Whether this resource is mandatory or beneficial but not required.
    """
    activity_tailored = models.ForeignKey(
        'ActivityTailored', 
        on_delete=models.CASCADE, 
        related_name="user_requirements"
    )
    
    # Generic Foreign Key fields
    content_type = models.ForeignKey(
        ContentType, 
        on_delete=models.CASCADE,
        help_text="The type of user resource required (Skill, Trait, UserLimitation, etc.)."
    )
    object_id = models.UUIDField(
        help_text="UUID of the specific user resource."
    )
    resource = GenericForeignKey('content_type', 'object_id')
    
    # Requirement details
    required_level = ValidatedRangeField(
        help_text="Minimum proficiency level (0-100) needed for the activity to be appropriate."
    )
    quantity_required = models.IntegerField(
        help_text="Number of instances or amount of the resource needed."
    )
    optional = models.BooleanField(
        default=False, 
        help_text="Whether having this resource is mandatory (False) or beneficial but not required (True)."
    )

    class Meta:
        ordering = ['activity_tailored', 'content_type', 'object_id']
        unique_together = ['activity_tailored', 'content_type', 'object_id']
        indexes = [
            models.Index(fields=["content_type", "object_id"]),
        ]

    def __str__(self):
        resource_name = str(self.resource)
        if hasattr(self.resource, 'name'):
            resource_name = self.resource.name
        elif hasattr(self.resource, 'description'):
            resource_name = self.resource.description[:30]
            
        return f"Resource {resource_name} for {self.activity_tailored.name}"
    
    @property
    def resource_type_name(self):
        """Returns a human-readable name for the resource type."""
        return self.content_type.model_class().__name__


class ActivityEnvRequirement(models.Model):
    """
    Specifies environmental requirements for a tailored activity.

    This model defines the environmental factors needed for a tailored activity to be performed, 
    such as the specific type of environment and the level of requirement.

    Attributes:
        env_type (CharField): 
            Example: "Outdoor"
        env_detail (JSONField): 
            Example: {"weather": "dry", "time_of_day": "daylight", "privacy_level": "moderate"}
        required_level (IntegerField): 
            Example: 80
        optional (BooleanField): 
            Example: False

    """
    activity_tailored = models.ForeignKey(
        'ActivityTailored', 
        on_delete=models.CASCADE, 
        related_name="env_requirements"
    )
    env_type = models.CharField(max_length=100, help_text="General category of environment needed (e.g., 'Indoor', 'Outdoor', 'Social', 'Private').")
    env_detail = models.JSONField(help_text="Detailed specifications about the environmental requirements including conditions and characteristics.")
    required_level = ValidatedRangeField(help_text="How strictly (0-100) the environmental conditions must be met for the activity to be viable.")
    optional = models.BooleanField(default=False, help_text="Whether this environmental requirement is essential (False) or preferable but not required (True).")

    class Meta:
        ordering = ['env_type']

    def __str__(self):
        return f"Env Req: {self.env_type}"


class ActivityTailoredResourceRequirement(models.Model):
    """
    Captures resource requirements specific to a tailored activity.

    This model captures the resources required for a tailored activity, detailing the type and 
    quantity of resources needed for completion.

    Attributes:
        activity_tailored (ForeignKey): Link to the tailored activity.
        personal_resource (ForeignKey): Required personal resource.
        quantity_required (IntegerField): 
            Example: 1
        optional (BooleanField): 
            Example: False

    """
    activity_tailored = models.ForeignKey(ActivityTailored, on_delete=models.CASCADE, related_name="resource_requirements")
    personal_resource = models.ForeignKey("user.UserResource", on_delete=models.CASCADE, related_name="tailored_resource_requirements")
    quantity_required = models.IntegerField(help_text="Specific amount of the personal resource needed for this tailored activity.")
    optional = models.BooleanField(default=False, help_text="Whether this resource is mandatory (False) or beneficial but not essential (True).")

    class Meta:
        ordering = ['activity_tailored']

    def __str__(self):
        return f"Tailored Resource Req for {self.activity_tailored.name}"
