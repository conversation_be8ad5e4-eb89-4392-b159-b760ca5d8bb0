{"name": "Mentor - General Chat - Foundation", "description": "Tests mentor's response to casual greeting from a Foundation phase user. Uses contextual evaluation that adapts to low trust level, neutral mood, and moderate stress environment to ensure appropriate gentle, reassuring approach.", "agent_role": "mentor", "input_data": {"user_message": "Just wanted to say hi! How are things today?", "context_packet": {"workflow_type": "discussion", "trust_level": 25}}, "metadata": {"workflow_type": "discussion", "evaluation_template_id": 2, "context": {"trust_level": 25, "mood": {"valence": 0.1, "arousal": -0.2}, "environment": {"stress_level": 45, "time_pressure": 40}}, "user_profile_context": {"trust_phase": "Foundation", "communication_preferences": {"tone": "friendly", "detail_level": "low"}, "personality_traits": {"extraversion": 0.6, "neuroticism": 0.5, "openness": 0.4, "conscientiousness": 0.4, "agreeableness": 0.7}, "engagement_pattern": "irregular", "interaction_frequency": "occasional", "last_interaction": "5 days ago", "new_user": false}, "expected_quality_criteria": {"Conversational Naturalness": ["Does it respond to the greeting in a natural, friendly way?", "Does it avoid sounding formulaic or overly scripted?", "Does it maintain appropriate conversational flow?", "Does it use simple, clear, and reassuring language appropriate for Foundation trust phase?"], "Agenda Balance": ["Does it respond appropriately without immediately pushing toward activities?", "Does it allow space for casual conversation in Foundation phase?", "Does it avoid making the user feel like they need to 'get to the point'?", "Does it focus on safe options and low-risk interactions given the trust level?"], "Trust Appropriateness": ["Is the familiarity level appropriate for Foundation phase?", "Does it avoid language that assumes high trust or deep relationship?", "Does it match the 'friendly but not intimate' tone appropriate for this trust level?", "Does it demonstrate gentle, understanding, and patient approach for neutral mood?"], "Engagement Sensitivity": ["Does it acknowledge the user's return after 5 days in a non-pressuring way?", "Does it gently encourage conversation without demanding it?", "Does it avoid making assumptions about the reason for the user's greeting?", "Does it provide balanced detail appropriate for moderate stress environment?"], "Natural Progression": ["Does it include a gentle, optional opening for the user to share more if they wish?", "Does it avoid abrupt transitions to system objectives?", "Does it maintain focus on building connection rather than task completion?", "Does it offer manageable challenges and clear outcomes suitable for moderate stress?"]}, "mock_tool_responses": {"get_user_profile": {"response": "{\"profile_name\":\"Foundation Phase Social\",\"trust_phase\":\"Foundation\",\"trust_level\":28,\"communication_preferences\":{\"tone\":\"friendly\",\"detail_level\":\"low\"},\"last_interaction_date\":\"5 days ago\",\"interaction_count\":7,\"completed_activities\":2}"}, "get_conversation_history": {"response": "{\"last_conversations\":[{\"date\":\"5 days ago\",\"summary\":\"Brief check-in, user mentioned feeling tired but interested in trying meditation sometime\"},{\"date\":\"2 weeks ago\",\"summary\":\"Discussed potential reading activities, user was hesitant but curious\"}]}"}}, "warmup_runs": 1, "benchmark_runs": 3}, "is_active": true}