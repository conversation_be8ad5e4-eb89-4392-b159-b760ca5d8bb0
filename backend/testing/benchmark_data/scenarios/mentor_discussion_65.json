[{"name": "Mentor - Handle Preference Change - Refinement", "description": "Tests mentor adapting suggestions when user expresses a change in preferences during conversation, demonstrating conversational agility and preference tracking.", "agent_role": "mentor", "input_data": {"user_message": "Actually, I'm feeling more in the mood for something creative today, not analytical. Something with writing perhaps?", "context_packet": {"workflow_type": "discussion", "trust_level": 65}}, "metadata": {"workflow_type": "discussion", "user_profile_context": {"trust_phase": "Expansion", "communication_preferences": {"tone": "flexible", "detail_level": "moderate"}, "personality_traits": {"extraversion": 0.6, "neuroticism": 0.4, "openness": 0.8, "conscientiousness": 0.5, "agreeableness": 0.7}, "skills": {"data_analysis": 4.2, "writing": 3.8, "creative_thinking": 3.5, "coding": 4.0}, "interests": ["data science", "creative writing", "design", "philosophy"]}, "expected_quality_criteria": {"Adaptive Response": ["Does it immediately pivot away from analytical activities without resistance?", "Does it acknowledge and validate the change in preference?", "Does it demonstrate flexibility in adapting to the user's current mood?"], "Preference Integration": ["Does it note this preference shift for future reference?", "Does it reorient suggestions toward creative writing activities?", "Does it maintain awareness of the user's skill level in this new domain?"], "Balance of Structure": ["Does it provide appropriate guidance while respecting the user's mood-driven choice?", "Does it avoid being overly prescriptive about the creative activity?", "Does it provide enough structure for a successful creative experience?"], "Multi-Domain Awareness": ["Does it demonstrate awareness of the user's skills in both analytical and creative domains?", "Does it leverage known interests in writing specifically?", "Does it avoid assumptions about the user's creative writing experience?"], "Engagement Promotion": ["Does it maintain enthusiasm despite the change in direction?", "Does it frame the creative option as equally valuable to analytical work?", "Does it use the preference change as an opportunity to deepen understanding of the user?"]}, "mock_tool_responses": {"get_user_profile": {"response": "{\"profile_name\":\"Versatile Explorer\",\"trust_phase\":\"Expansion\",\"trust_level\":75,\"communication_preferences\":{\"tone\":\"flexible\",\"detail_level\":\"moderate\"},\"skills\":{\"data_analysis\":4.2,\"writing\":3.8,\"creative_thinking\":3.5,\"coding\":4.0},\"interests\":[\"data science\",\"creative writing\",\"design\",\"philosophy\"]}"}, "search_activities": [{"condition": "tool_input.get('domain') == 'writing' and tool_input.get('creative') == True", "response": "{'activities': [{'id': 'act_creative_123', 'name': 'Structured Short Story Challenge', 'challenge_level': 55, 'domain': 'creative_writing'}, {'id': 'act_creative_124', 'name': 'Poetry Exploration Workshop', 'challenge_level': 40, 'domain': 'creative_writing'}, {'id': 'act_creative_125', 'name': 'Character Development Exercise', 'challenge_level': 45, 'domain': 'creative_writing'}]}"}], "update_user_preference": {"response": "{\"success\":true,\"updated_preference\":\"creative_writing\",\"preference_strength\":0.8}"}}, "warmup_runs": 1, "benchmark_runs": 3}, "is_active": true}, {"name": "Mentor - Clarification Request - Expansion", "description": "Tests mentor seeking clarification when an Expansion phase user makes a vague request, demonstrating effective information gathering without overwhelming the user.", "agent_role": "mentor", "input_data": {"user_message": "I want to work on something related to Python.", "context_packet": {"workflow_type": "discussion", "trust_level": 65}}, "metadata": {"workflow_type": "discussion", "user_profile_context": {"trust_phase": "Expansion", "communication_preferences": {"tone": "inquisitive", "detail_level": "moderate"}, "personality_traits": {"extraversion": 0.4, "neuroticism": 0.4, "openness": 0.7, "conscientiousness": 0.6, "agreeableness": 0.5}, "skills": {"python": 3.2, "data_analysis": 2.8, "web_development": 2.5, "general_programming": 3.5}, "python_specific_history": {"last_activity": "basic_data_visualization", "completed_activities": ["python_basics", "list_comprehension_exercise", "basic_data_visualization"], "abandoned_activities": ["flask_web_app"], "struggle_areas": ["classes_and_oop", "async_programming"]}}, "expected_quality_criteria": {"Clarification Skill": ["Does it ask specific, targeted questions to clarify the vague request?", "Do the questions address multiple dimensions (skill level, area of interest, project type)?", "Are the questions phrased to encourage detailed responses?"], "Prior Knowledge Integration": ["Does it reference the user's existing Python skill level (3.2)?", "Does it acknowledge previous Python activities completed?", "Does it consider areas of struggle identified in the user's history?"], "Option Presentation": ["Does it provide examples of possible directions without overwhelming?", "Are options calibrated to the user's demonstrated skill level?", "Do suggestions build appropriately on prior successes?"], "Information Gathering Strategy": ["Does it use progressive clarification (asking most important questions first)?", "Does it balance immediate suggestions with information gathering?", "Does it explain why the additional information will help provide better recommendations?"], "Empowerment Focus": ["Does it maintain user agency throughout the clarification process?", "Does it avoid making the clarification feel like an interrogation?", "Does it position itself as a collaborative partner rather than an evaluator?"]}, "mock_tool_responses": {"get_user_profile": {"response": "{\"profile_name\":\"Expanding Programmer\",\"trust_phase\":\"Expansion\",\"trust_level\":62,\"communication_preferences\":{\"tone\":\"inquisitive\",\"detail_level\":\"moderate\"},\"skills\":{\"python\":3.2,\"data_analysis\":2.8,\"web_development\":2.5,\"general_programming\":3.5}}"}, "get_user_activity_history": {"response": "{\"python_activities\":[{\"name\":\"Python Basics\",\"completion_date\":\"2024-01-15\",\"feedback_score\":4},{\"name\":\"List Comprehension Exercise\",\"completion_date\":\"2024-02-03\",\"feedback_score\":3.5},{\"name\":\"Basic Data Visualization\",\"completion_date\":\"2024-03-10\",\"feedback_score\":4},{\"name\":\"Flask Web App\",\"completion_date\":null,\"abandoned\":true,\"feedback\":\"Too complicated for me right now\"}],\"struggle_areas\":[\"classes_and_oop\",\"async_programming\"]}"}, "search_activities": [{"condition": "tool_input.get('domain') == 'python' and tool_input.get('level') == 'intermediate'", "response": "{'activities': [{'id': 'py_int_1', 'name': 'Data Analysis with Pandas', 'challenge_level': 45}, {'id': 'py_int_2', 'name': 'Building Command Line Tools', 'challenge_level': 40}, {'id': 'py_int_3', 'name': 'Python Testing Fundamentals', 'challenge_level': 50}]}"}, {"condition": "tool_input.get('domain') == 'python' and tool_input.get('level') == 'beginner'", "response": "{'activities': [{'id': 'py_beg_1', 'name': 'Dictionary Mastery', 'challenge_level': 30}, {'id': 'py_beg_2', 'name': 'File Handling in Python', 'challenge_level': 25}, {'id': 'py_beg_3', 'name': 'Simple Python Game', 'challenge_level': 35}]}"}]}, "warmup_runs": 1, "benchmark_runs": 3}, "is_active": true}, {"name": "Mentor - Long Conversation History Integration", "description": "Tests mentor maintaining context and providing relevant suggestions after a longer conversation, demonstrating effective conversation tracking and synthesis.", "agent_role": "mentor", "input_data": {"user_message": "Okay, after all that, what do you think is the best next step for me?", "context_packet": {"workflow_type": "discussion", "trust_level": 65}}, "metadata": {"workflow_type": "discussion", "user_profile_context": {"trust_phase": "Expansion", "communication_preferences": {"tone": "decisive", "detail_level": "moderate"}, "personality_traits": {"extraversion": 0.5, "neuroticism": 0.4, "openness": 0.7, "conscientiousness": 0.6, "agreeableness": 0.6}, "skills": {"python": 3.5, "web_development": 2.2, "html_css": 2.8, "databases": 2.0}, "learning_style": "practical", "preferred_challenge_level": "moderate", "time_availability": "limited"}, "expected_quality_criteria": {"Conversation Synthesis": ["Does it effectively summarize the key points from the previous conversation?", "Does it demonstrate clear understanding of the decisions already made in the conversation?", "Does it avoid asking questions that were already addressed?"], "Context Retention": ["Does it remember specific details from earlier in the conversation (Django, todo list, etc.)?", "Does it connect the current response to concerns previously raised (avoiding overwhelm)?", "Does it maintain awareness of the user's expressed preferences throughout?"], "Progressive Guidance": ["Does it provide a clear, specific next step rather than vague suggestions?", "Is the recommendation logically connected to the previous conversation flow?", "Does it respect earlier boundaries the user established (simplicity, manageability)?"], "Decision Clarity": ["Does it provide a decisive recommendation as requested?", "Is the recommendation specific enough to be actionable?", "Does it present a clear rationale for why this is the best next step?"], "Appropriate Detail": ["Does it provide moderate detail aligned with the user's preference?", "Does it avoid overwhelming with unnecessary technical jargon?", "Does it give enough information for the user to begin the suggested activity?"]}, "mock_tool_responses": {"get_user_profile": {"response": "{\"profile_name\":\"Learning Developer\",\"trust_phase\":\"Expansion\",\"trust_level\":68,\"communication_preferences\":{\"tone\":\"decisive\",\"detail_level\":\"moderate\"},\"skills\":{\"python\":3.5,\"web_development\":2.2,\"html_css\":2.8,\"databases\":2.0},\"learning_style\":\"practical\"}"}, "search_activities": [{"condition": "tool_input.get('keywords') and 'django' in tool_input.get('keywords') and 'beginner' in tool_input.get('keywords')", "response": "{'activities': [{'id': 'django_todo_intro', 'name': 'Django Todo List Tutorial', 'description': 'A step-by-step guide to building a simple todo list application with Django', 'challenge_level': 40, 'estimated_time': 120, 'prerequisites': ['basic_python']}, {'id': 'django_models_intro', 'name': 'Django Models Fundamentals', 'description': 'Learn how to create and work with Django models for data management', 'challenge_level': 35, 'estimated_time': 90, 'prerequisites': ['basic_python']}, {'id': 'django_urls_views', 'name': 'Django URLs and Views Workshop', 'description': 'Understanding how URLs connect to views in Django applications', 'challenge_level': 38, 'estimated_time': 100, 'prerequisites': ['basic_python']}]}"}]}, "warmup_runs": 1, "benchmark_runs": 3}, "is_active": true}, {"name": "Mentor - Activity Completion - Expansion", "description": "Tests mentor providing thoughtful reflection and next steps after a user completes an activity, focusing on growth integration and next steps.", "agent_role": "mentor", "input_data": {"user_message": "I finished the meditation activity. It was surprisingly helpful for focusing my mind before working on my coding project.", "context_packet": {"workflow_type": "activity_feedback", "trust_level": 70}}, "metadata": {"workflow_type": "activity_feedback", "user_profile_context": {"trust_phase": "Expansion", "communication_preferences": {"tone": "thoughtful", "detail_level": "moderate"}, "personality_traits": {"extraversion": 0.4, "neuroticism": 0.6, "openness": 0.7, "conscientiousness": 0.7, "agreeableness": 0.6}, "skills": {"programming": 3.8, "mindfulness": 2.1, "focus_techniques": 1.9, "problem_solving": 3.5}, "interests": ["coding", "productivity", "mental wellbeing"], "goals": ["improve_focus", "advance_programming_skills", "work_life_balance"]}, "activity_context": {"name": "Mindful Coding Preparation", "type": "guided_meditation", "duration": 10, "domains": ["mindfulness", "productivity"], "intended_benefits": ["improved focus", "reduced anxiety", "mental preparation"]}, "expected_quality_criteria": {"Feedback Integration": ["Does it meaningfully acknowledge the specific feedback about focus improvement?", "Does it recognize the 'surprisingly helpful' aspect mentioned by the user?", "Does it avoid generic responses that could apply to any completed activity?"], "Cross-Domain Connection": ["Does it explicitly connect the mindfulness practice to the coding domain?", "Does it help the user recognize the transferable value between different skill domains?", "Does it appropriately frame the connection in Expansion phase terms?"], "Growth Reinforcement": ["Does it emphasize what the user has learned from this experience?", "Does it highlight skill development in both mindfulness and productivity?", "Does it encourage reflection on how this experience relates to the user's goals?"], "Appropriate Next Steps": ["Does it suggest natural next steps based on this positive experience?", "Are the suggested next steps properly calibrated to Expansion phase challenge levels?", "Do the suggestions build on both the meditation success and the coding application?"], "Personalized Reflection": ["Does it connect this activity's success to the user's specific goals?", "Does it acknowledge the user's initial skepticism implied by 'surprisingly helpful'?", "Does it avoid one-size-fits-all reflections in favor of specific insights for this user?"]}, "mock_tool_responses": {"get_user_profile": {"response": "{\"profile_name\":\"Focused Coder\",\"trust_phase\":\"Expansion\",\"trust_level\":70,\"communication_preferences\":{\"tone\":\"thoughtful\",\"detail_level\":\"moderate\"},\"skills\":{\"programming\":3.8,\"mindfulness\":2.1,\"focus_techniques\":1.9},\"goals\":[\"improve_focus\",\"advance_programming_skills\",\"work_life_balance\"]}"}, "get_activity_details": [{"condition": "tool_input.get('activity_id') == 'mindful_coding_prep'", "response": "{'name': 'Mindful Coding Preparation', 'type': 'guided_meditation', 'duration': 10, 'domains': ['mindfulness', 'productivity'], 'intended_benefits': ['improved focus', 'reduced anxiety', 'mental preparation'], 'challenge_level': 35, 'follow_up_activities': ['extended_coding_meditation', 'mindful_debugging_practice', 'focus_building_routine']}"}], "update_user_skill": {"response": "{\"skill\":\"mindfulness\",\"previous_level\":2.1,\"new_level\":2.3,\"focus_techniques\":2.1}"}}, "warmup_runs": 1, "benchmark_runs": 3}, "is_active": true}, {"name": "Mentor - Trust Phase Transition - Foundation to Expansion", "description": "Tests mentor acknowledging and facilitating a user's transition from Foundation to Expansion trust phase, emphasizing new possibilities while maintaining safety.", "agent_role": "mentor", "input_data": {"user_message": "I've been using this system consistently for a few weeks now, and I'm feeling more comfortable. What else can I explore now?", "context_packet": {"workflow_type": "discussion", "trust_level": 60}}, "metadata": {"workflow_type": "discussion", "user_profile_context": {"trust_phase": "Foundation", "phase_transition": {"to": "Expansion", "date": "2024-04-14", "trust_level": 60}, "communication_preferences": {"tone": "encouraging", "detail_level": "moderate"}, "personality_traits": {"extraversion": 0.4, "neuroticism": 0.5, "openness": 0.6, "conscientiousness": 0.7, "agreeableness": 0.6}, "activity_history": {"completed_foundation_activities": 12, "highest_challenge_level": 38, "domain_engagement": {"learning": 5, "personal_development": 4, "physical_wellness": 3}, "preferred_activity_duration": "30-60min", "feedback_patterns": {"positivity": 0.8, "completion_rate": 0.9}}}, "expected_quality_criteria": {"Phase Transition Acknowledgment": ["Does it explicitly acknowledge the transition to Expansion phase?", "Does it frame this transition positively as a milestone of progress?", "Does it connect the transition to the user's demonstrated consistency?"], "New Possibilities Introduction": ["Does it effectively communicate what new types of activities are now available?", "Does it explain how the challenge calibration will evolve in Expansion phase?", "Does it introduce cross-domain connections as an Expansion phase opportunity?"], "Safety Maintenance": ["Does it reassure the user that safer, Foundation-like activities remain available?", "Does it emphasize that the user controls the pace of challenge increase?", "Does it avoid pressuring the user to immediately tackle maximum challenges?"], "Personalized Transition": ["Does it relate the transition to specific patterns in the user's history?", "Does it suggest next steps that build logically on previous activities?", "Does it acknowledge the user's domain preferences in its recommendations?"], "Agency Empowerment": ["Does it increase the level of choice offered to the user?", "Does it position the mentor as a collaborative partner rather than a guide?", "Does it invite the user to share preferences about the direction of growth?"]}, "mock_tool_responses": {"get_user_profile": {"response": "{\"profile_name\":\"Transitioning Learner\",\"trust_phase\":\"Foundation\",\"phase_transition\":{\"to\":\"Expansion\",\"date\":\"2024-04-14\",\"trust_level\":60},\"communication_preferences\":{\"tone\":\"encouraging\",\"detail_level\":\"moderate\"}}"}, "get_user_activity_history": {"response": "{\"completed_activities\":[{\"name\":\"Introduction to Mindfulness\",\"date\":\"2024-03-12\",\"challenge_level\":20,\"domain\":\"wellness\",\"rating\":4},{\"name\":\"Basic Creative Writing Exercise\",\"date\":\"2024-03-16\",\"challenge_level\":25,\"domain\":\"creativity\",\"rating\":5},{\"name\":\"Beginner's Guide to Time Management\",\"date\":\"2024-03-20\",\"challenge_level\":30,\"domain\":\"productivity\",\"rating\":4},{\"name\":\"Simple Programming Challenge\",\"date\":\"2024-03-25\",\"challenge_level\":35,\"domain\":\"technology\",\"rating\":4},{\"name\":\"Daily Gratitude Practice\",\"date\":\"2024-03-28\",\"challenge_level\":15,\"domain\":\"wellness\",\"rating\":5},{\"name\":\"Personal Values Reflection\",\"date\":\"2024-04-02\",\"challenge_level\":28,\"domain\":\"personal_development\",\"rating\":4},{\"name\":\"Reading Comprehension Exercise\",\"date\":\"2024-04-08\",\"challenge_level\":32,\"domain\":\"learning\",\"rating\":4},{\"name\":\"Memory Improvement Techniques\",\"date\":\"2024-04-12\",\"challenge_level\":38,\"domain\":\"learning\",\"rating\":5}],\"favorite_domains\":[\"learning\",\"wellness\",\"technology\"],\"completion_rate\":0.9}"}, "get_expansion_activities": {"response": "{\"recommended_activities\":[{\"name\":\"Interdisciplinary Learning Project\",\"challenge_level\":45,\"domains\":[\"learning\",\"technology\"],\"description\":\"Create connections between two different fields of knowledge\"},{\"name\":\"Advanced Time Blocking System\",\"challenge_level\":48,\"domains\":[\"productivity\",\"technology\"],\"description\":\"Develop a personalized productivity system using digital tools\"},{\"name\":\"Mindfulness-Based Problem Solving\",\"challenge_level\":50,\"domains\":[\"wellness\",\"learning\"],\"description\":\"Apply mindfulness techniques to approach complex problems\"}],\"challenge_range\":{\"foundation_max\":40,\"expansion_initial\":45,\"expansion_ceiling\":70}}"}}, "warmup_runs": 1, "benchmark_runs": 3}, "is_active": true}]