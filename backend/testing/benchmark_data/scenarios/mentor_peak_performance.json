{"name": "Mentor - Peak Performance - Integration", "description": "Tests mentor's response to a high-trust user in peak positive state seeking ambitious challenges. Demonstrates contextual evaluation adapting to optimal conditions for maximum growth and achievement.", "agent_role": "mentor", "input_data": {"user_message": "I'm feeling absolutely amazing today! I just achieved a major breakthrough and I'm ready to tackle something really ambitious. What's the most challenging thing we could work on together?", "context_packet": {"workflow_type": "wheel_generation", "trust_level": 95}}, "metadata": {"workflow_type": "wheel_generation", "evaluation_template_id": 1, "context": {"trust_level": 95, "mood": {"valence": 0.9, "arousal": 0.8}, "environment": {"stress_level": 10, "time_pressure": 15}}, "user_profile_context": {"trust_phase": "Integration", "communication_preferences": {"tone": "dynamic", "detail_level": "comprehensive"}, "personality_traits": {"extraversion": 0.8, "neuroticism": 0.2, "openness": 0.9, "conscientiousness": 0.9, "agreeableness": 0.7}, "achievement_level": "high_performer", "recent_successes": ["major_project_completion", "skill_mastery", "leadership_recognition"], "growth_trajectory": "accelerating"}, "expected_quality_criteria": {"Excellence_Orientation": ["Does it match the user's peak energy and enthusiasm?", "Does it suggest truly ambitious and transformational challenges?", "Does it demonstrate masterful adaptation and co-creative responses for highest trust level?", "Does it use inspiring and sophisticated language appropriate for Integration phase?"], "Energy_Matching": ["Does it respond with appropriate enthusiasm and celebration?", "Does it channel the user's high energy into productive directions?", "Does it demonstrate high-energy tone and stimulating language for high arousal state?", "Does it provide dynamic and energetic pacing matching the user's state?"], "Challenge_Calibration": ["Does it offer challenges that match the user's peak readiness?", "Does it suggest complex, multi-dimensional projects?", "Does it encourage breakthrough thinking and innovation?", "Does it provide ambitious goals and creative challenges suitable for mastery level?"], "Comprehensive_Planning": ["Does it take advantage of the low-stress environment for detailed exploration?", "Does it provide rich, comprehensive options and thorough analysis?", "Does it offer in-depth planning and complex project structures?", "Does it demonstrate comprehensive adaptation and detailed responses?"], "Long_term_Vision": ["Does it leverage the low time pressure for strategic, long-term planning?", "Does it connect immediate challenges to broader life vision?", "Does it encourage visionary thinking and future-oriented goals?", "Does it provide patient guidance and thorough exploration of possibilities?"], "Mastery_Development": ["Does it recognize and build upon the user's recent achievements?", "Does it suggest opportunities for leadership and knowledge sharing?", "Does it encourage the user to push the boundaries of their expertise?", "Does it frame activities in terms of mastery and excellence pursuit?"]}, "mock_tool_responses": {"get_user_profile": {"response": "{\"profile_name\":\"Peak Performance Integration User\",\"trust_phase\":\"Integration\",\"trust_level\":95,\"communication_preferences\":{\"tone\":\"dynamic\",\"detail_level\":\"comprehensive\"},\"achievement_level\":\"high_performer\",\"recent_achievements\":[{\"name\":\"Major Project Leadership\",\"impact\":\"high\",\"recognition\":\"company-wide\"},{\"name\":\"Advanced Skill Certification\",\"mastery_level\":\"expert\"},{\"name\":\"Mentoring Program Launch\",\"participants\":15,\"feedback_score\":4.8}],\"growth_metrics\":{\"skill_development_rate\":\"accelerating\",\"challenge_completion_rate\":0.95,\"innovation_index\":0.87}}"}, "get_achievement_history": {"response": "{\"recent_breakthroughs\":[{\"date\":\"today\",\"type\":\"project_completion\",\"description\":\"Successfully led cross-functional team to deliver innovative solution\",\"impact\":\"transformational\"}]}"}}, "warmup_runs": 1, "benchmark_runs": 3}, "is_active": true}