[{"name": "<PERSON><PERSON> - <PERSON><PERSON> - Get Profile", "description": "Tests mentor using the get_user_profile tool when user asks about their progress, demonstrating effective profile retrieval and meaningful analysis.", "agent_role": "mentor", "input_data": {"user_message": "How have I been doing lately? Am I making progress?", "context_packet": {"workflow_type": "discussion", "trust_level": 85}}, "metadata": {"workflow_type": "discussion", "user_profile_context": {"trust_phase": "Integration", "communication_preferences": {"tone": "analytical", "detail_level": "high"}, "personality_traits": {"extraversion": 0.5, "neuroticism": 0.3, "openness": 0.8, "conscientiousness": 0.8, "agreeableness": 0.6}, "user_history": {"total_activities": 42, "recent_activities": 8, "completion_rate": 0.85, "average_feedback": 4.2, "domain_distribution": {"technology": 12, "creative": 8, "wellness": 15, "learning": 7}, "challenge_progression": [{"month": "January", "avg_challenge": 35}, {"month": "February", "avg_challenge": 42}, {"month": "March", "avg_challenge": 55}, {"month": "April", "avg_challenge": 60}], "skill_improvements": [{"skill": "programming", "start_level": 3.2, "current_level": 4.1}, {"skill": "writing", "start_level": 2.8, "current_level": 3.5}, {"skill": "meditation", "start_level": 1.5, "current_level": 3.2}]}}, "expected_quality_criteria": {"Tool Usage": ["Does it use the get_user_profile tool or equivalent to retrieve user data?", "Does it use appropriate tools to gather activity history and progress data?", "Is the tool usage efficient and targeted to answer the specific question?"], "Data Presentation": ["Does it present the retrieved profile data in an organized, meaningful way?", "Does it highlight relevant metrics and trends rather than raw data?", "Does it prioritize information based on the user's high detail preference?"], "Progress Analysis": ["Does it provide substantive analysis of the user's progress over time?", "Does it identify specific areas of growth or achievement?", "Does it use quantitative measures (completion rates, challenge levels, etc.) to support observations?"], "Balanced Assessment": ["Does it offer a balanced view of strengths and growth opportunities?", "Does it avoid overly positive or negative assessments not supported by data?", "Does it provide appropriate context for any metrics shared?"], "Integration Alignment": ["Does it frame progress in terms appropriate for Integration phase?", "Does it highlight connections between different domains and skills?", "Does it encourage reflection on the user's own growth trajectory?"]}, "mock_tool_responses": {"get_user_profile": {"response": "{\"profile_name\":\"Integration Phase Achiever\",\"trust_phase\":\"Integration\",\"trust_level\":82,\"communication_preferences\":{\"tone\":\"analytical\",\"detail_level\":\"high\"},\"joined_date\":\"2023-09-15\",\"total_activities_completed\":42,\"completion_rate\":0.85}"}, "get_user_activity_history": {"response": "{\"activity_counts_by_month\":[{\"month\":\"2023-11\",\"count\":7,\"avg_challenge\":35,\"avg_rating\":3.8},{\"month\":\"2023-12\",\"count\":6,\"avg_challenge\":42,\"avg_rating\":4.0},{\"month\":\"2024-01\",\"count\":9,\"avg_challenge\":47,\"avg_rating\":4.1},{\"month\":\"2024-02\",\"count\":8,\"avg_challenge\":55,\"avg_rating\":4.3},{\"month\":\"2024-03\",\"count\":8,\"avg_challenge\":60,\"avg_rating\":4.2}],\"recent_activities\":[{\"date\":\"2024-03-25\",\"name\":\"Advanced Data Visualization Project\",\"challenge\":65,\"rating\":5},{\"date\":\"2024-03-18\",\"name\":\"Mindfulness Teaching Workshop\",\"challenge\":55,\"rating\":4},{\"date\":\"2024-03-10\",\"name\":\"Technical Writing Seminar\",\"challenge\":60,\"rating\":4.5}],\"domain_distribution\":{\"technology\":12,\"creative\":8,\"wellness\":15,\"learning\":7}}"}, "get_user_skill_progression": {"response": "{\"skill_trajectories\":[{\"skill\":\"programming\",\"assessments\":[{\"date\":\"2023-10\",\"level\":3.2},{\"date\":\"2023-12\",\"level\":3.5},{\"date\":\"2024-02\",\"level\":3.8},{\"date\":\"2024-03\",\"level\":4.1}]},{\"skill\":\"writing\",\"assessments\":[{\"date\":\"2023-10\",\"level\":2.8},{\"date\":\"2023-12\",\"level\":3.0},{\"date\":\"2024-02\",\"level\":3.2},{\"date\":\"2024-03\",\"level\":3.5}]},{\"skill\":\"meditation\",\"assessments\":[{\"date\":\"2023-10\",\"level\":1.5},{\"date\":\"2023-12\",\"level\":2.2},{\"date\":\"2024-02\",\"level\":2.8},{\"date\":\"2024-03\",\"level\":3.2}]}]}"}}, "warmup_runs": 1, "benchmark_runs": 3}, "is_active": true}]