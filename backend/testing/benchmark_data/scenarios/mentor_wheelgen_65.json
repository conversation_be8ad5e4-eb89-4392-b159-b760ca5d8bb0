[{"name": "Mentor - Initial Wheel Gen - Expansion", "description": "Tests mentor's initial response in a wheel generation workflow for a user in the Expansion trust phase, ready for a challenge and seeking growth opportunities. Uses contextual evaluation that adapts based on trust level, positive mood, and low stress environment.", "agent_role": "mentor", "input_data": {"user_message": "I'm feeling energetic and ready for a real challenge today!", "context_packet": {"workflow_type": "wheel_generation", "trust_level": 65}}, "metadata": {"workflow_type": "wheel_generation", "evaluation_template_id": 1, "context": {"trust_level": 65, "mood": {"valence": 0.7, "arousal": 0.6}, "environment": {"stress_level": 20, "time_pressure": 30}}, "user_profile_context": {"trust_phase": "Expansion", "communication_preferences": {"tone": "enthusiastic", "detail_level": "moderate"}, "personality_traits": {"extraversion": 0.7, "neuroticism": 0.3, "openness": 0.8, "conscientiousness": 0.7}, "completed_activities": ["basic_coding_exercise", "mindfulness_practice", "read_technical_article"]}, "expected_quality_criteria": {"Tone": ["Is the tone enthusiastic and energetic to match the user's mood?", "Does it convey appropriate excitement about challenge opportunities?", "Does it demonstrate collaborative and empowering language appropriate for Expansion trust phase?"], "Growth Orientation": ["Does it frame activities as growth opportunities?", "Does it acknowledge the user's readiness for challenges?", "Does it build on the user's past successes (completed activities)?", "Does it suggest balanced options with some stretch goals?"], "Calibrated Challenge": ["Does it suggest appropriate challenge levels for Expansion phase?", "Does it avoid defaulting to 'safe' activities when the user is ready for more?", "Does it offer ambitious goals and creative challenges suitable for high trust?"], "Process Clarity": ["Does it clearly explain the next steps in wheel generation?", "Is the explanation suitably detailed for moderate detail preference?", "Does it provide comprehensive options given the low stress environment?"], "Agency Support": ["Does it empower the user's sense of agency and choice?", "Does it support user-driven growth rather than prescribing it?", "Does it encourage independent exploration appropriate for high trust level?"]}, "mock_tool_responses": {"get_user_profile": {"response": "{\"profile_name\": \"Expansion Phase User\", \"trust_phase\": \"Expansion\", \"trust_level\": 72, \"communication_preferences\": {\"tone\": \"enthusiastic\", \"detail_level\": \"moderate\"}, \"personality\": {\"extraversion\": 0.7, \"neuroticism\": 0.3, \"openness\": 0.8, \"conscientiousness\": 0.7}, \"activity_history\": [{\"name\": \"Basic Coding Exercise\", \"completed\": true, \"feedback_score\": 4}, {\"name\": \"Mindfulness Practice\", \"completed\": true, \"feedback_score\": 3}, {\"name\": \"Read Technical Article\", \"completed\": true, \"feedback_score\": 5}]}"}}, "warmup_runs": 1, "benchmark_runs": 3}, "is_active": true}]