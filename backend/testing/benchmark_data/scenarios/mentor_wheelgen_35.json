[{"name": "Mentor - Initial Wheel Gen - Foundation", "description": "Tests mentor's initial response in a wheel generation workflow for a user in the Foundation trust phase with low detail preference, focusing on safety and gradual trust building.", "agent_role": "mentor", "input_data": {"user_message": "Feeling a bit unsure today, maybe something simple and not too demanding?", "context_packet": {"workflow_type": "wheel_generation", "trust_level": 35}}, "metadata": {"workflow_type": "wheel_generation", "user_profile_context": {"trust_phase": "Foundation", "communication_preferences": {"tone": "supportive", "detail_level": "low"}, "personality_traits": {"extraversion": 0.3, "neuroticism": 0.7, "openness": 0.5}}, "expected_quality_criteria": {"Tone": ["Is the tone supportive and gentle?", "Does it avoid being overly clinical or robotic?", "Does it match the user's uncertain emotional state?"], "Clarity": ["Is the core message easy to understand?", "Are instructions or explanations presented clearly?", "Is the language adapted to the user's low detail preference?"], "Acknowledgement": ["Does the response acknowledge the user's stated feeling of uncertainty?", "Does it validate the user's desire for something simple and not demanding?"], "Safety Focus": ["Does it emphasize safety and comfort appropriate for Foundation phase?", "Does it avoid pushing the user beyond their comfort zone?"], "Next Steps": ["Are next steps in the wheel generation process clearly outlined?", "Is the user given appropriate agency in the process?"]}, "mock_tool_responses": {"get_user_profile": {"response": "{\"profile_name\": \"Foundation Phase User\", \"trust_phase\": \"Foundation\", \"trust_level\": 35, \"communication_preferences\": {\"tone\": \"supportive\", \"detail_level\": \"low\"}, \"personality\": {\"extraversion\": 0.3, \"neuroticism\": 0.7, \"openness\": 0.5}}"}}, "warmup_runs": 1, "benchmark_runs": 3}, "is_active": true}]