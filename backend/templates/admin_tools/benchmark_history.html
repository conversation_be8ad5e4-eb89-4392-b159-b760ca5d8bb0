<!-- backend/templates/admin_tools/benchmark_history.html -->
{% extends "admin/base_site.html" %} {# Extend the correct base site template #}
{% load static %}

{% block title %}Benchmark History | {{ site_title|default:_('Django site admin') }}{% endblock %}

{% block extrahead %}
{{ block.super }}
<script>
    window.BENCHMARK_SCENARIOS_API_URL = "{% url 'game_of_life_admin:benchmark_scenarios_api' %}";
    window.modalToolChartInstance = null; // To manage modal chart - Made global
</script>
<!-- Quick Test module -->
<script src="{% static 'admin/js/quick_test.js' %}"></script>
<!-- Quick Test Modal Enhancements -->
<script src="{% static 'admin/js/quick_test_modal_enhancements.js' %}"></script>
<!-- Modal base styles -->
<link rel="stylesheet" type="text/css" href="{% static 'admin_tools/modals/modal_base_styles.css' %}">
{% endblock %}

{% block extrastyle %}
    {{ block.super }}
    <!-- Include Chart.js for visualizations -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns@next/dist/chartjs-adapter-date-fns.bundle.min.js"></script> <!-- Date adapter -->

    <style>
        .history-container {
            max-width: 1400px; /* Wider container for history */
            margin: 0 auto;
            padding: 20px;
        }

        .card {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }

        .filters {
            margin-bottom: 20px;
            background: #f8f8f8;
            padding: 15px;
            border-radius: 4px;
            border: 1px solid #eee;
        }

        .filter-form {
            display: flex;
            flex-wrap: wrap; /* Allow wrapping on smaller screens */
            gap: 20px; /* Increased gap */
            align-items: flex-end; /* Align items to bottom */
        }

        .form-group {
            margin-bottom: 10px; /* Add some bottom margin back */
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-group select,
        .form-group input[type="date"] { /* Style date inputs */
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            min-width: 180px; /* Adjust width */
        }

        .benchmark-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .benchmark-table th,
        .benchmark-table td {
            padding: 10px 12px;
            border-bottom: 1px solid #ddd;
            text-align: left;
            vertical-align: top; /* Align content to top */
        }

        .benchmark-table th {
            background-color: #f8f8f8;
            font-weight: bold;
        }

        .benchmark-table tr:hover {
            background-color: #f5f5f5;
        }

        #history-chart-container {
            margin-bottom: 30px;
            /* height: 350px; Remove fixed height */
            min-height: 350px; /* Set a minimum height for the main chart */
            position: relative; /* Needed for Chart.js responsiveness */
            height: 400px; /* Explicit height for testing graph flatness */
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1050; /* Higher z-index for admin */
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0,0,0,0.5); /* Darker background */
        }

        .modal-content {
            background-color: #fefefe;
            margin: 5% auto; /* More top margin */
            padding: 25px;
            border: 1px solid #888;
            width: 85%; /* Wider modal */
            max-width: 900px;
            border-radius: 8px;
            position: relative;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .close {
            color: #aaa;
            position: absolute; /* Position close button */
            top: 10px;
            right: 20px;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
        }

        .close:hover,
        .close:focus {
            color: black;
            text-decoration: none;
        }

        /* Specialized Modal Headers */
        .agent-evaluation-header, .workflow-evaluation-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            position: relative;
        }

        .workflow-evaluation-header {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        /* Evaluation Type Indicators */
        .evaluation-type-badge {
            position: absolute;
            top: 10px;
            right: 20px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            backdrop-filter: blur(10px);
        }

        .evaluation-type-badge.agent {
            background: rgba(102, 126, 234, 0.8);
        }

        .evaluation-type-badge.workflow {
            background: rgba(240, 147, 251, 0.8);
        }

        /* Evaluation Focus Indicators */
        .evaluation-focus {
            margin-top: 10px;
            padding: 8px 12px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            font-size: 13px;
            font-style: italic;
        }

        /* Tone Analysis Indicators */
        .tone-analysis-indicator {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            background: rgba(255, 255, 255, 0.15);
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            margin-left: 10px;
        }

        .tone-analysis-indicator.included {
            color: #4caf50;
        }

        .tone-analysis-indicator.excluded {
            color: #ff9800;
        }

        .evaluation-title h3 {
            margin: 0 0 10px 0;
            font-size: 1.8em;
        }

        .scenario-info {
            font-size: 1.1em;
            opacity: 0.9;
            margin-bottom: 15px;
        }

        .evaluation-meta {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            font-size: 0.9em;
            opacity: 0.8;
        }

        .evaluation-meta span {
            background: rgba(255, 255, 255, 0.2);
            padding: 4px 8px;
            border-radius: 4px;
        }

        /* Optimization Insights */
        .optimization-insights, .workflow-insights {
            margin-top: 20px;
        }

        .optimization-insights h4, .workflow-insights h4 {
            margin: 0 0 15px 0;
            color: white;
        }

        .insight-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }

        .insight-card {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 8px;
            padding: 15px;
            backdrop-filter: blur(10px);
        }

        .insight-card .card-icon {
            font-size: 1.5em;
            margin-bottom: 8px;
        }

        .insight-card h5 {
            margin: 0 0 8px 0;
            color: white;
            font-size: 1em;
        }

        .insight-card p {
            margin: 4px 0;
            font-size: 0.9em;
            opacity: 0.9;
        }

        .optimization-tip {
            font-style: italic;
            font-size: 0.8em !important;
            opacity: 0.7 !important;
            margin-top: 8px !important;
        }

        /* Optimization Recommendations */
        .optimization-recommendations {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin-top: 15px;
            border-radius: 4px;
        }

        .optimization-recommendations h4 {
            margin: 0 0 10px 0;
            color: #007bff;
        }

        .optimization-recommendations ul {
            margin: 0;
            padding-left: 20px;
        }

        .optimization-recommendations li {
            margin-bottom: 8px;
            line-height: 1.4;
        }

        /* JSON Viewer */
        .json-viewer {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            max-height: 400px;
            overflow-y: auto;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .metric {
            background-color: #f9f9f9;
            border: 1px solid #eee;
            border-radius: 4px;
            padding: 15px;
            text-align: center;
        }

        .metric-label {
            display: block;
            font-size: 13px;
            color: #666;
            margin-bottom: 5px;
        }

        .metric-value {
            display: block;
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }

        .tool-chart-container {
            /* height: 300px; Remove fixed height */
            min-height: 200px; /* Ensure a minimum height */
            margin-top: 20px;
            position: relative; /* Needed for Chart.js */
        }

        .modal-section h3 {
            margin-top: 25px;
            margin-bottom: 10px;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }

        .modal-section pre {
             background: #f5f5f5;
             padding: 10px;
             border: 1px solid #ddd;
             max-height: 250px;
             overflow-y: auto;
             white-space: pre-wrap; /* Wrap long lines */
             word-wrap: break-word;
        }

        .btn {
            padding: 8px 15px; /* Slightly larger buttons */
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px; /* Slightly larger font */
            text-decoration: none;
            display: inline-block;
            text-align: center;
            margin-right: 5px; /* Add some space between buttons if needed */
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background: #5a6268;
        }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid transparent;
            border-radius: 4px;
        }
        .alert-danger {
            color: #a94442;
            background-color: #f2dede;
            border-color: #ebccd1;
        }
        .errornote { /* Style Django error messages */
            color: #a94442;
            background-color: #f2dede;
            border: 1px solid #ebccd1;
            padding: 10px 15px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        .loader {
            border: 4px solid #f3f3f3; /* Light grey */
            border-top: 4px solid #3498db; /* Blue */
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 10px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Comparison Styles */
        .comparison-controls {
            margin-bottom: 15px;
            text-align: right; /* Align button to the right */
        }
        #compare-selected-btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        #comparison-modal .modal-content {
            max-width: 1200px; /* Wider modal for comparison */
            width: 95%;
        }
        .comparison-modal-body {
            display: flex;
            gap: 20px;
            overflow-x: auto; /* Allow horizontal scroll if needed */
        }
        .comparison-column {
            flex: 1;
            min-width: 400px; /* Ensure columns have minimum width */
            border: 1px solid #eee;
            padding: 15px;
            border-radius: 4px;
            background-color: #fdfdfd;
        }
        .comparison-column h4 {
            margin-top: 0;
            border-bottom: 1px solid #ddd;
            padding-bottom: 8px;
            margin-bottom: 15px;
            font-size: 1.1em;
        }
        .comparison-metric {
            margin-bottom: 12px;
            font-size: 0.95em;
        }
        .comparison-metric strong {
            display: inline-block;
            min-width: 150px; /* Align labels */
            color: #555;
        }
        .comparison-metric pre {
            background: #f0f0f0;
            padding: 8px;
            border: 1px solid #e0e0e0;
            max-height: 150px;
            overflow-y: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
            font-size: 0.9em;
            margin-top: 5px;
        }

        /* Context Package Styles */
        .context-tabs {
            margin-top: 15px;
        }

        .tab-buttons {
            display: flex;
            border-bottom: 2px solid #eee;
            margin-bottom: 15px;
        }

        .tab-button {
            background: #f8f8f8;
            border: 1px solid #ddd;
            border-bottom: none;
            padding: 10px 15px;
            cursor: pointer;
            margin-right: 2px;
            border-radius: 4px 4px 0 0;
            font-size: 14px;
            transition: background-color 0.2s;
        }

        .tab-button:hover {
            background: #e9ecef;
        }

        .tab-button.active {
            background: #fff;
            border-bottom: 2px solid #fff;
            margin-bottom: -2px;
            font-weight: bold;
            color: #007bff;
        }

        .tab-content {
            min-height: 200px;
        }

        .tab-pane {
            display: none;
        }

        .tab-pane.active {
            display: block;
        }

        .context-flow-step {
            border: 1px solid #e9ecef;
            border-radius: 6px;
            margin-bottom: 15px;
            overflow: hidden;
        }

        .step-header {
            background: #f8f9fa;
            padding: 12px 15px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .step-number {
            background: #007bff;
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }

        .step-stage {
            background: #6c757d;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .step-description {
            color: #495057;
            font-weight: 500;
        }

        .step-data {
            padding: 15px;
        }

        .step-data pre {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 12px;
            margin: 0;
            max-height: 300px;
            overflow-y: auto;
            font-size: 13px;
        }

        /* Enhanced Context Package Styles */
        .context-summary-cards {
            margin-bottom: 20px;
        }

        .summary-cards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .summary-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            display: flex;
            align-items: center;
            gap: 12px;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .summary-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .card-icon {
            font-size: 24px;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255,255,255,0.8);
            border-radius: 50%;
        }

        .card-content h4 {
            margin: 0 0 5px 0;
            font-size: 14px;
            color: #6c757d;
            font-weight: 600;
        }

        .card-value {
            margin: 0;
            font-size: 16px;
            font-weight: bold;
            color: #495057;
        }

        /* Quality and confidence indicators */
        .confidence-high, .quality-excellent { color: #28a745; }
        .confidence-moderate, .quality-good { color: #17a2b8; }
        .confidence-low, .quality-fair { color: #ffc107; }
        .quality-poor { color: #dc3545; }

        /* Execution type indicators */
        .agent-evaluation {
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
            border: 1px solid #bbdefb;
        }

        .workflow-evaluation {
            background: #f3e5f5;
            color: #7b1fa2;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
            border: 1px solid #e1bee7;
        }

        /* Runs count styling */
        .runs-count {
            background: #f8f9fa;
            color: #495057;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: 600;
            border: 1px solid #dee2e6;
        }

        /* Workflow Output Styles */
        .workflow-output {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .workflow-header h4 {
            margin: 0 0 15px 0;
        }

        /* Enhanced Timeline Styles */
        .enhanced-timeline-content {
            padding: 0;
        }

        .warnings-section {
            margin-bottom: 25px;
            padding: 15px;
            background: #fff8e1;
            border: 1px solid #ffcc02;
            border-radius: 6px;
        }

        .warnings-section h4 {
            margin: 0 0 15px 0;
            color: #f57c00;
            font-size: 16px;
        }

        .warnings-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 12px;
        }

        .warning-item {
            display: flex;
            align-items: flex-start;
            gap: 10px;
            padding: 12px;
            border-radius: 6px;
            border: 1px solid;
        }

        .warning-item.error {
            background: #ffebee;
            border-color: #f44336;
        }

        .warning-item.performance {
            background: #fff3e0;
            border-color: #ff9800;
        }

        .warning-item.mock {
            background: #e8f5e8;
            border-color: #4caf50;
        }

        .warning-item.fallback {
            background: #e3f2fd;
            border-color: #2196f3;
        }

        .warning-icon {
            font-size: 18px;
            flex-shrink: 0;
        }

        .warning-content {
            flex: 1;
        }

        .warning-title {
            font-weight: 600;
            font-size: 14px;
            margin-bottom: 4px;
        }

        .warning-message {
            font-size: 13px;
            color: #666;
        }

        .timeline-section {
            margin-top: 20px;
        }

        .timeline-section h4 {
            margin: 0 0 20px 0;
            color: #333;
            font-size: 16px;
        }

        .timeline-container {
            position: relative;
            padding-left: 40px;
        }

        .timeline-line {
            position: absolute;
            left: 20px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e0e0e0;
        }

        .timeline-event {
            position: relative;
            margin-bottom: 20px;
            background: #fff;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
        }

        .timeline-event.failed {
            border-color: #f44336;
        }

        .timeline-event.warning {
            border-color: #ff9800;
        }

        .timeline-event.mocked {
            border-left: 4px solid #4caf50;
        }

        .timeline-event.fallback {
            border-left: 4px solid #2196f3;
        }

        .timeline-marker {
            position: absolute;
            left: -30px;
            top: 15px;
            width: 40px;
            height: 40px;
            background: #fff;
            border: 2px solid #e0e0e0;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            z-index: 1;
        }

        .timeline-event.failed .timeline-marker {
            border-color: #f44336;
            background: #ffebee;
        }

        .timeline-event.mocked .timeline-marker {
            border-color: #4caf50;
            background: #e8f5e8;
        }

        .timeline-event.fallback .timeline-marker {
            border-color: #2196f3;
            background: #e3f2fd;
        }

        .marker-number {
            font-weight: bold;
            font-size: 10px;
            line-height: 1;
        }

        .marker-status {
            font-size: 12px;
            line-height: 1;
        }

        .timeline-content {
            padding: 0;
        }

        .event-header {
            padding: 15px 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e0e0e0;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .event-header:hover {
            background: #e9ecef;
        }

        .event-title {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .agent-name {
            font-weight: 600;
            color: #333;
        }

        .stage-badge {
            background: #6c757d;
            color: white;
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .warning-badge {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .warning-badge.mock {
            background: #4caf50;
            color: white;
        }

        .warning-badge.fallback {
            background: #2196f3;
            color: white;
        }

        .event-meta {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 13px;
            color: #666;
        }

        .duration {
            font-weight: 600;
        }

        .duration.fast {
            color: #4caf50;
        }

        .duration.medium {
            color: #ff9800;
        }

        .duration.slow {
            color: #f44336;
        }

        .toggle-icon {
            font-size: 12px;
            transition: transform 0.2s;
        }

        .event-details {
            display: none;
            padding: 20px;
        }

        .details-tabs {
            display: flex;
            border-bottom: 1px solid #e0e0e0;
            margin-bottom: 15px;
        }

        .detail-tab {
            background: none;
            border: none;
            padding: 8px 16px;
            cursor: pointer;
            font-size: 13px;
            color: #666;
            border-bottom: 2px solid transparent;
            transition: all 0.2s;
        }

        .detail-tab:hover {
            color: #333;
            background: #f8f9fa;
        }

        .detail-tab.active {
            color: #007bff;
            border-bottom-color: #007bff;
            font-weight: 600;
        }

        .detail-tab.error {
            color: #f44336;
        }

        .detail-tab.error.active {
            border-bottom-color: #f44336;
        }

        .detail-content {
            position: relative;
        }

        .detail-pane {
            display: none;
        }

        .detail-pane.active {
            display: block;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 12px;
            background: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #e9ecef;
        }

        .summary-item .label {
            font-weight: 600;
            color: #666;
        }

        .summary-item .value {
            font-weight: 600;
        }

        .summary-item .value.success {
            color: #28a745;
        }

        .summary-item .value.error {
            color: #dc3545;
        }

        .json-viewer {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        .error-content {
            background: #ffebee;
            border: 1px solid #f44336;
            border-radius: 4px;
            padding: 15px;
        }

        .error-message {
            color: #c62828;
            font-weight: 600;
        }

        /* Status indicators */
        .status-success {
            color: #2e7d32;
            font-weight: 600;
        }

        .status-warning {
            color: #f57c00;
            font-weight: 600;
        }

        .status-error {
            color: #d32f2f;
            font-weight: 600;
        }

        /* Row styling for errors */
        .error-row {
            background-color: #ffebee !important;
            border-left: 4px solid #f44336;
        }

        .warning-row {
            background-color: #fff8e1 !important;
            border-left: 4px solid #ff9800;
        }

        /* Execution Mode Styles */
        .execution-mode {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            display: inline-block;
        }

        .execution-mode.real {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #f44336;
        }

        .execution-mode.mock {
            background: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }

        .execution-mode.warning {
            background: #fff3e0;
            color: #f57c00;
            border: 1px solid #ff9800;
            position: relative;
        }

        .execution-mode.warning::after {
            content: "⚠️";
            margin-left: 4px;
        }

        /* Evaluation Context Indicators */
        .evaluation-context-indicator {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
            display: inline-block;
            margin-left: 4px;
            cursor: help;
        }

        .evaluation-context-indicator.agent {
            background: #e3f2fd;
            color: #1976d2;
            border: 1px solid #2196f3;
        }

        .evaluation-context-indicator.workflow {
            background: #fce4ec;
            color: #c2185b;
            border: 1px solid #e91e63;
        }

        /* Tooltip Styles */
        .tooltip {
            position: relative;
            display: inline-block;
        }

        .tooltip .tooltiptext {
            visibility: hidden;
            width: 300px;
            background-color: #333;
            color: #fff;
            text-align: left;
            border-radius: 6px;
            padding: 8px;
            position: absolute;
            z-index: 1000;
            bottom: 125%;
            left: 50%;
            margin-left: -150px;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 12px;
            line-height: 1.4;
        }

        .tooltip .tooltiptext::after {
            content: "";
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: #333 transparent transparent transparent;
        }

        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }

        /* Quick Test Styles */
        .hidden {
            display: none !important;
        }

        /* Agent Communications Styles */
        .agent-communications-summary {
            margin-bottom: 20px;
        }

        .agent-badge {
            background: #007bff;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
            margin-right: 8px;
            margin-bottom: 4px;
            display: inline-block;
        }

        .overview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .overview-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }

        .overview-label {
            font-weight: 600;
            color: #495057;
        }

        .overview-value {
            color: #6c757d;
        }

        .agents-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 20px;
        }

        .timeline {
            position: relative;
            padding-left: 30px;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 12px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #dee2e6;
        }

        .timeline-item {
            position: relative;
            margin-bottom: 20px;
        }

        .timeline-marker {
            position: absolute;
            left: -18px;
            top: 0;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            color: white;
        }

        .timeline-marker.success {
            background: #28a745;
        }

        .timeline-marker.failure {
            background: #dc3545;
        }

        .timeline-content {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 12px;
        }

        .timeline-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
        }

        .timeline-duration {
            background: #6c757d;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
        }

        .timeline-timestamp {
            font-size: 12px;
            color: #6c757d;
        }

        /* Agent Interactions Styles */
        .agent-interactions-content {
            margin-top: 15px;
        }

        .interactions-header {
            margin-bottom: 20px;
        }

        .interactions-header p {
            color: #6c757d;
            margin: 5px 0 0 0;
        }

        .interaction-item {
            border: 1px solid #e9ecef;
            border-radius: 6px;
            margin-bottom: 15px;
            overflow: hidden;
        }

        .interaction-header {
            background: #f8f9fa;
            padding: 12px 15px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #e9ecef;
        }

        .interaction-header:hover {
            background: #e9ecef;
        }

        .interaction-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .interaction-number {
            background: #007bff;
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }

        .agent-name {
            font-weight: 600;
            color: #495057;
        }

        .agent-stage {
            background: #6c757d;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .interaction-status.success {
            color: #28a745;
        }

        .interaction-status.failure {
            color: #dc3545;
        }

        .interaction-metrics {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .duration {
            background: #17a2b8;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
        }

        .timestamp {
            color: #6c757d;
            font-size: 12px;
        }

        .interaction-details {
            padding: 20px;
        }

        .details-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .detail-section h5 {
            margin: 0 0 10px 0;
            color: #495057;
        }

        .data-preview {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 12px;
        }

        .data-summary {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .data-count {
            background: #007bff;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: 600;
        }

        .data-keys {
            color: #6c757d;
            font-size: 12px;
        }

        .json-data {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }

        .execution-metadata h5 {
            margin: 0 0 15px 0;
            color: #495057;
        }

        .metadata-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }

        .metadata-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }

        .metadata-label {
            font-weight: 600;
            color: #495057;
        }

        .metadata-value {
            color: #6c757d;
        }

        .metadata-value.success {
            color: #28a745;
        }

        .metadata-value.failure {
            color: #dc3545;
        }

        .metadata-item.error {
            background: #f8d7da;
            color: #721c24;
            padding: 8px;
            border-radius: 4px;
            border: none;
        }

        /* State Transitions Styles */
        .state-transitions-content {
            margin-top: 15px;
        }

        .transitions-header {
            margin-bottom: 20px;
        }

        .transitions-header p {
            color: #6c757d;
            margin: 5px 0 0 0;
        }

        .transition-item {
            border: 1px solid #e9ecef;
            border-radius: 6px;
            margin-bottom: 15px;
            overflow: hidden;
        }

        .transition-header {
            background: #f8f9fa;
            padding: 12px 15px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #e9ecef;
        }

        .transition-header:hover {
            background: #e9ecef;
        }

        .transition-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .transition-number {
            background: #6c757d;
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }

        .transition-agent {
            font-weight: 600;
            color: #495057;
        }

        .transition-timestamp {
            color: #6c757d;
            font-size: 12px;
        }

        .transition-details {
            padding: 20px;
        }

        .states-comparison {
            display: grid;
            grid-template-columns: 1fr auto 1fr;
            gap: 20px;
            align-items: start;
            margin-bottom: 20px;
        }

        .state-section h5 {
            margin: 0 0 10px 0;
            color: #495057;
        }

        .state-arrow {
            display: flex;
            align-items: center;
            justify-content: center;
            padding-top: 30px;
        }

        .arrow {
            font-size: 24px;
            color: #007bff;
            font-weight: bold;
        }

        .transition-metadata h5 {
            margin: 0 0 15px 0;
            color: #495057;
            border-bottom: 2px solid #007bff;
            padding-bottom: 8px;
        }

        .wheel-section {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .wheel-section h5 {
            margin: 0 0 10px 0;
            color: #6c757d;
        }

        .wheel-info {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .wheel-stat {
            background: #e9ecef;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
            color: #495057;
        }

        .wheel-items {
            display: grid;
            gap: 10px;
        }

        .wheel-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 12px;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .wheel-item:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .item-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
        }

        .item-number {
            background: #007bff;
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }

        .item-name {
            font-weight: 600;
            color: #495057;
            flex: 1;
        }

        .item-difficulty {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: 600;
        }

        .item-difficulty.easy {
            background: #d4edda;
            color: #155724;
        }

        .item-difficulty.medium {
            background: #fff3cd;
            color: #856404;
        }

        .item-difficulty.hard {
            background: #f8d7da;
            color: #721c24;
        }

        .item-description {
            margin: 0 0 8px 0;
            color: #6c757d;
            font-size: 14px;
        }

        .item-meta {
            display: flex;
            gap: 15px;
            font-size: 12px;
            color: #6c757d;
        }

        .user-response-section {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .user-response-section h5 {
            margin: 0 0 10px 0;
            color: #6c757d;
        }

        .user-response {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 12px;
            border-radius: 4px;
            font-style: italic;
            color: #495057;
        }

        .agent-outputs-section {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
        }

        .agent-outputs-section h5 {
            margin: 0 0 15px 0;
            color: #6c757d;
        }

        .agent-outputs-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 12px;
        }

        .agent-output-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
        }

        .agent-output-item h6 {
            margin: 0 0 8px 0;
            color: #495057;
            font-size: 14px;
            font-weight: 600;
        }

        .agent-output-details {
            font-size: 12px;
            color: #6c757d;
        }

        .agent-output-details div {
            margin-bottom: 4px;
        }

        .no-output {
            text-align: center;
            color: #6c757d;
            font-style: italic;
            padding: 20px;
        }

        /* Overview content */
        .overview-content {
            padding: 15px 0;
        }

        .performance-indicators {
            margin-bottom: 25px;
        }

        .indicators-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 10px;
        }

        .indicator {
            background: #f8f9fa;
            padding: 12px;
            border-radius: 6px;
            border-left: 4px solid #007bff;
        }

        .indicator-label {
            display: block;
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 5px;
            font-weight: 600;
        }

        .indicator-value {
            font-size: 16px;
            font-weight: bold;
            color: #495057;
        }

        .recommendations {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 6px;
            padding: 15px;
        }

        .recommendations h4 {
            margin-top: 0;
            color: #0056b3;
        }

        .recommendations-list {
            margin: 10px 0 0 0;
            padding-left: 20px;
        }

        .recommendations-list li {
            margin-bottom: 8px;
            color: #495057;
        }

        /* Analysis content */
        .analysis-content {
            padding: 15px 0;
        }

        .analysis-section {
            margin-bottom: 25px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #6c757d;
        }

        .analysis-section h4 {
            margin-top: 0;
            margin-bottom: 15px;
            color: #495057;
        }

        .impact-grid, .behavior-grid, .patterns-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 10px;
        }

        .impact-item, .behavior-item, .pattern-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            background: white;
            border-radius: 4px;
            border: 1px solid #e9ecef;
        }

        .impact-item strong, .behavior-item strong, .pattern-item strong {
            color: #495057;
            font-size: 14px;
        }

        .impact-item span, .behavior-item span, .pattern-item span {
            color: #6c757d;
            font-size: 14px;
            text-align: right;
            max-width: 60%;
        }

        /* Enhanced context data formatting */
        .context-data {
            background: #f8f9fa;
            border-radius: 6px;
            padding: 15px;
        }

        .context-item {
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e9ecef;
        }

        .context-item:last-child {
            margin-bottom: 0;
            padding-bottom: 0;
            border-bottom: none;
        }

        .context-key {
            font-weight: bold;
            color: #495057;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .context-value {
            color: #6c757d;
        }

        .context-value pre {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            margin: 0;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }

        .context-value span {
            font-size: 14px;
            padding: 6px 10px;
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            display: inline-block;
        }

        .formatted-json {
            background: #f8f9fa;
            border-radius: 6px;
            padding: 15px;
        }

        /* Expandable sections */
        .expandable-section {
            margin-bottom: 15px;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            overflow: hidden;
        }

        .section-header {
            background: #f8f9fa;
            padding: 12px 15px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background-color 0.2s;
            user-select: none;
        }

        .section-header:hover {
            background: #e9ecef;
        }

        .section-header h4 {
            margin: 0;
            font-size: 16px;
            color: #495057;
        }

        .toggle-icon {
            font-size: 14px;
            color: #6c757d;
            transition: transform 0.2s;
        }

        .section-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
            background: white;
        }

        .section-content.expanded {
            max-height: 1000px;
            padding: 15px;
        }

        /* Raw data styles */
        .raw-data-content {
            padding: 10px 0;
        }

        .json-viewer {
            background: #f8f9fa;
            border-radius: 4px;
            max-height: 400px;
            overflow-y: auto;
        }

        .json-viewer pre {
            margin: 0;
            padding: 15px;
            font-size: 12px;
            line-height: 1.4;
            color: #495057;
            background: transparent;
            border: none;
        }

        /* Enhanced tab styling */
        .tab-button {
            position: relative;
        }

        .tab-button.active::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            right: 0;
            height: 2px;
            background: #007bff;
        }

        /* Improved spacing for analysis content */
        .analysis-content .expandable-section:first-child .section-content {
            border-top: none;
        }

        /* Better visual hierarchy */
        .impact-item, .behavior-item, .pattern-item {
            transition: background-color 0.2s;
        }

        .impact-item:hover, .behavior-item:hover, .pattern-item:hover {
            background: #f8f9fa;
        }
    </style>
{% endblock %}

{# Remove the overridden breadcrumbs block entirely to inherit from base_site.html #}
{# {% block breadcrumbs %} ... {% endblock %} #}

{% block content %}
<div id="content-main" class="history-container">
    <h1>{{ title }}</h1>
    <p class="help">View benchmark execution sessions grouped by scenario, agent, and execution parameters. Each row represents an execution session that may contain multiple individual runs for statistical significance.</p>

    <!-- Quick Test Section -->
    <div class="card" style="background: linear-gradient(135deg, #e3f2fd 0%, #f8f9fa 100%); border-left: 4px solid #2196f3; margin-bottom: 20px;">
        <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 15px;">
            <div>
                <h2 style="margin: 0; color: #1976d2;">
                    <i class="fas fa-rocket" style="margin-right: 8px;"></i>
                    Quick Test
                </h2>
                <p style="margin: 5px 0 0 0; color: #666; font-size: 14px;">
                    Run a benchmark test with your saved preferences
                </p>
            </div>
            <div style="display: flex; gap: 10px; align-items: center;">
                <button id="configure-quick-test-btn" class="btn btn-secondary" title="Configure test parameters">
                    <i class="fas fa-cog"></i> Configure
                </button>
                <button id="quick-test-btn" class="btn btn-primary" style="font-size: 16px; padding: 10px 20px;" title="Run test with saved parameters">
                    <i class="fas fa-play"></i> Run Quick Test
                </button>
            </div>
        </div>

        <!-- Quick Test Status -->
        <div id="quick-test-status" class="hidden" style="margin-top: 15px;">
            <div style="background: #fff; border-radius: 6px; padding: 15px; border: 1px solid #e0e0e0;">
                <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                    <div class="loader" style="width: 20px; height: 20px; margin: 0;"></div>
                    <span id="quick-test-status-text">Preparing test...</span>
                </div>
                <div style="background: #f5f5f5; border-radius: 4px; height: 8px; overflow: hidden;">
                    <div id="quick-test-progress" style="background: #2196f3; height: 100%; width: 0%; transition: width 0.3s ease;"></div>
                </div>
                <div style="margin-top: 10px; font-size: 12px; color: #666;">
                    <span id="quick-test-details">Using saved configuration...</span>
                </div>
            </div>
        </div>

        <!-- Quick Test Results -->
        <div id="quick-test-results" class="hidden" style="margin-top: 15px;">
            <div style="background: #fff; border-radius: 6px; padding: 15px; border: 1px solid #e0e0e0;">
                <h4 style="margin: 0 0 10px 0; color: #333;">Test Results</h4>
                <div id="quick-test-results-content"></div>
            </div>
        </div>
    </div>

     {% if error %}
        <p class="errornote">{{ error }}</p>
    {% endif %}

    <div class="filters">
        {# Use the correct admin namespace for the form action #}
        <form id="filter-form" class="filter-form"> {# Removed action, added ID #}
            <div class="form-group">
                <label for="agent-role">Agent Role:</label>
                <select id="agent-role" name="agent_role"> {# Removed onchange #}
                    <option value="">-- All Agents --</option>
                    {% for role in agent_roles %}
                    <option value="{{ role }}" {% if selected_role == role %}selected{% endif %}>
                        {{ role|title }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="form-group">
                <label for="scenario">Scenario:</label> {# New Scenario Filter #}
                <select id="scenario" name="scenario_id">
                    <option value="">-- All Scenarios (Aggregated View) --</option>
                    {% for scenario in available_scenarios %}
                    <option value="{{ scenario.id }}" {% if selected_scenario_id == scenario.id|stringformat:"s" %}selected{% endif %}>
                        {{ scenario.name }} (v{{ scenario.version }}) ({{ scenario.run_count }} runs) {# Display run count #}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="form-group">
                <label for="tag">Scenario Tag:</label>
                <select id="tag" name="tag">
                    <option value="">-- All Tags --</option>
                    {% for tag in available_tags %}
                    <option value="{{ tag.id }}" {% if selected_tag_id == tag.id|stringformat:"s" %}selected{% endif %}>
                        {{ tag.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="form-group">
                <label for="start-date">Start Date:</label>
                <input type="date" id="start-date" name="start_date" value="{{ selected_start_date|default:'' }}">
            </div>
            <div class="form-group">
                <label for="end-date">End Date:</label>
                <input type="date" id="end-date" name="end_date" value="{{ selected_end_date|default:'' }}">
            </div>
            <div class="form-group">
                 <button type="submit" class="btn btn-secondary">Apply Filters</button>
            </div>
        </form>
    </div>

    <div class="card">
        {# Add placeholders for dynamic content update #}
        <div id="history-chart-container">
            <canvas id="history-chart"></canvas>
            <div id="chart-loading-message" style="display: none; text-align: center; padding: 20px;">Loading chart data...</div>
            <div id="chart-no-data-message" style="display: none; text-align: center; padding: 20px;">No data available for the selected filters.</div>
        </div>

        <div class="comparison-controls">
            <button id="compare-selected-btn" class="btn btn-secondary" disabled>Compare Selected (0)</button>
        </div>

        <div id="table-container">
            {# Initial table structure will be replaced by JS #}
            <table class="benchmark-table">
                <thead>
                <tr>
                    <th><input type="checkbox" id="select-all-checkbox" title="Select/Deselect All Visible"></th> {# Checkbox Header #}
                    <th>Scenario</th>
                    <th>Evaluation Type</th> {# New Column to distinguish Agent vs Workflow #}
                    <th>Agent Role</th>
                    <th>Runs Count</th> {# New Column to show number of runs in execution session #}
                    <th>LLM Model</th>
                    <th>LLM Temp</th>
                    <th>Execution Mode</th>
                    <th>Date</th>
                    <th>Mean Duration (ms)</th>
                    <th>Success Rate</th>
                    <th>Semantic Score</th>
                    <th>Tokens In</th>
                    <th>Tokens Out</th>
                    <th>Est. Cost</th>
                    <th>Actions</th>
                </tr>
                </thead>
                <tbody>
                    {# Rows will be populated by JS #}
                </tbody>
            </table>
            <p id="table-no-data-message" style="display: none; text-align: center; padding: 20px;">No benchmark runs match the selected filters.</p>
        </div>
        <div id="table-loading-message" style="display: none; text-align: center; padding: 20px;">Loading table data...</div>
    </div>

    <!-- Include specialized modals -->
    {% include 'admin_tools/modals/agent_evaluation_modal.html' %}
    {% include 'admin_tools/modals/workflow_evaluation_modal.html' %}
    {% include 'admin_tools/modals/quick_test_config_modal.html' %}
    {% include 'admin_tools/modals/comparison_modal.html' %}
</div>


<script>
document.addEventListener('DOMContentLoaded', function() {
    // --- Modal Element Declarations (Moved to top) ---
    const agentDetailsModal = document.getElementById('agent-details-modal');
    const agentCloseButton = agentDetailsModal ? agentDetailsModal.querySelector('.close') : null;
    const workflowDetailsModal = document.getElementById('workflow-details-modal');
    const workflowCloseButton = workflowDetailsModal ? workflowDetailsModal.querySelector('.close') : null;
    const comparisonModal = document.getElementById('comparison-modal');
    const comparisonModalBody = document.getElementById('comparison-modal-body');
    const comparisonCloseButton = comparisonModal ? comparisonModal.querySelector('.close') : null;
    // modalToolChartInstance is already defined globally in the head section

    // --- Other Element Declarations ---
    const historyChartEl = document.getElementById('history-chart');
    const chartLoadingMessage = document.getElementById('chart-loading-message');
    const chartNoDataMessage = document.getElementById('chart-no-data-message');
    const tableContainer = document.getElementById('table-container');
    const tableLoadingMessage = document.getElementById('table-loading-message');
    const tableNoDataMessage = document.getElementById('table-no-data-message');
    const filterForm = document.getElementById('filter-form');
    const compareButton = document.getElementById('compare-selected-btn');
    const selectAllCheckbox = document.getElementById('select-all-checkbox');

    let historyChartInstance = null; // To manage chart instance
    let selectedRunIds = new Set(); // To track selected runs for comparison

    // --- Initialize Quick Test ---
    if (typeof window.QuickTest !== 'undefined') {
        const quickTestConfig = {
            scenariosApiUrl: window.BENCHMARK_SCENARIOS_API_URL,
            benchmarkRunApiUrl: '/admin/benchmarks/api/run/',
            taskStatusApiUrl: '/admin/benchmarks/api/task/',  // Correct URL pattern
            templatesApiUrl: '/admin/benchmarks/api/templates/',
            showSuccessMessage: function(message) {
                // Create a simple success message
                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert alert-success';
                alertDiv.textContent = message;
                alertDiv.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 9999; padding: 15px; background: #d4edda; color: #155724; border: 1px solid #c3e6cb; border-radius: 4px;';
                document.body.appendChild(alertDiv);
                setTimeout(() => alertDiv.remove(), 5000);
            },
            showErrorMessage: function(message) {
                // Create a simple error message
                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert alert-danger';
                alertDiv.textContent = message;
                alertDiv.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 9999; padding: 15px; background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; border-radius: 4px;';
                document.body.appendChild(alertDiv);
                setTimeout(() => alertDiv.remove(), 5000);
            },
            storageKey: 'quickTestConfig_history' // Use different storage key for history page
        };

        window.quickTestInstance = new window.QuickTest(quickTestConfig);
    } else {
        console.warn('QuickTest module not loaded');
    }

    // --- Chart Drawing Function ---
    // Updated to handle both aggregated and dimensional views
    // Added llmModels and agentVersions for scenario view tooltips
    function drawChart(isScenarioView, labelsISO, dimensionData, meanDurations, medianDurations, successRates, semanticScores, llmModels = [], agentVersions = []) {
        const chartLabels = labelsISO.map(isoString => new Date(isoString));
        const hasChartData = chartLabels.length > 0;

        // Show/hide messages based on data
        chartNoDataMessage.style.display = hasChartData ? 'none' : 'block';
        historyChartEl.style.display = hasChartData ? 'block' : 'none'; // Hide canvas if no data

        if (historyChartInstance) {
            historyChartInstance.destroy(); // Destroy previous instance
            historyChartInstance = null;
        }

        if (!hasChartData) {
            return; // Don't draw if no data
        }

        let chartConfigData;
        let chartConfigOptions;

        // Define distinct colors for dimensions
        const dimensionColors = [
            'rgb(255, 99, 132)', 'rgb(54, 162, 235)', 'rgb(255, 206, 86)',
            'rgb(75, 192, 192)', 'rgb(153, 102, 255)', 'rgb(255, 159, 64)',
            'rgb(199, 199, 199)', 'rgb(83, 102, 255)', 'rgb(40, 159, 64)',
            'rgb(210, 99, 132)'
        ];
        let colorIndex = 0;

        if (isScenarioView) {
            // --- Scenario View: Plot Dimensions AND Duration ---
            const datasets = [];
            // Add Dimension datasets
            for (const dimName in dimensionData) {
                const color = dimensionColors[colorIndex % dimensionColors.length];
                datasets.push({
                    label: dimName, // Dimension Name (e.g., Clarity)
                    data: dimensionData[dimName], // Array of scores for this dimension
                    borderColor: color,
                    backgroundColor: color.replace('rgb', 'rgba').replace(')', ', 0.1)'),
                    yAxisID: 'yDimensions', // Use the left Y axis for dimensions
                    tension: 0.1,
                    spanGaps: true // Connect lines across null data points
                });
                colorIndex++;
            }
            // Add Mean Duration dataset
            const durationColor = 'rgb(255, 99, 71)'; // Example: Tomato color for duration
            datasets.push({
                label: 'Mean Duration (ms)',
                data: meanDurations, // Use the meanDurations array passed in
                borderColor: durationColor,
                backgroundColor: durationColor.replace('rgb', 'rgba').replace(')', ', 0.1)'),
                yAxisID: 'yDuration', // Use the new right Y axis for duration
                tension: 0.1,
                spanGaps: true // Or false, depending on preference for missing duration
            });


            chartConfigData = {
                labels: chartLabels, // X-axis labels (dates)
                datasets: datasets
            };

            chartConfigOptions = { // Options for Scenario View
                responsive: true,
                maintainAspectRatio: false,
                interaction: { mode: 'index', intersect: false }, // Show tooltip for all datasets at the same x-index
                stacked: false,
                plugins: {
                    title: { display: true, text: 'Scenario Trend: Dimensions & Duration' }, // Updated title
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        callbacks: {
                            // Custom label callback to format each line in the tooltip
                            label: function(tooltipItem) {
                                let label = tooltipItem.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (tooltipItem.parsed.y !== null) {
                                    // Format dimension scores (0-1) differently from duration (ms)
                                    if (tooltipItem.dataset.yAxisID === 'yDimensions') {
                                        label += tooltipItem.parsed.y.toFixed(2); // 2 decimal places for scores
                                    } else if (tooltipItem.dataset.yAxisID === 'yDuration') {
                                        label += tooltipItem.parsed.y.toFixed(0) + ' ms'; // 0 decimal places for ms
                                    } else {
                                        label += tooltipItem.formattedValue; // Fallback
                                    }
                                } else {
                                    label += 'N/A';
                                }
                                return label;
                            },
                            // Custom afterBody callback to add LLM model and Agent Version
                            afterBody: function(tooltipItems) {
                                // Get the index of the data point from the first item
                                const dataIndex = tooltipItems[0].dataIndex;
                                const model = llmModels[dataIndex] || 'N/A';
                                const version = agentVersions[dataIndex] || 'N/A';
                                // Return an array of strings, each will be a new line
                                return [
                                    `LLM: ${model}`,
                                    `Version: ${version}`
                                ];
                            }
                        } // End callbacks
                    } // End tooltip
                }, // End plugins
                scales: {
                    x: { // X-axis (Time)
                        type: 'time',
                        time: { unit: 'day', tooltipFormat: 'PPpp', displayFormats: { day: 'MMM d, yyyy' } },
                        title: { display: true, text: 'Execution Date' }
                    },
                    yDimensions: { // Y-axis for Semantic Scores (Left)
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: { display: true, text: 'Semantic Score (0-1)' },
                        min: 0,
                        max: 1, // Scores are 0-1
                        beginAtZero: true
                    },
                    yDuration: { // Y-axis for Duration (Right)
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: { display: true, text: 'Mean Duration (ms)' },
                        beginAtZero: true,
                        // Prevent grid lines from overlapping with the dimension grid lines
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                } // End scales
            }; // End Scenario View Options

        } else {
            // --- Aggregated View: Plot Original Metrics ---
            // Tooltip customization could be added here too if needed,
            // using the llmModels and agentVersions arrays passed from the backend.
            chartConfigData = {
                labels: chartLabels,
                datasets: [
                    {
                        label: 'Mean Duration (ms)',
                        data: meanDurations,
                        borderColor: 'rgb(54, 162, 235)',
                        backgroundColor: 'rgba(54, 162, 235, 0.1)',
                        yAxisID: 'yDuration',
                        tension: 0.1
                    },
                    {
                        label: 'Median Duration (ms)',
                        data: medianDurations,
                        borderColor: 'rgb(153, 102, 255)',
                        backgroundColor: 'rgba(153, 102, 255, 0.1)',
                        yAxisID: 'yDuration',
                        tension: 0.1
                    },
                    {
                        label: 'Success Rate (%)',
                        data: successRates,
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.1)',
                        yAxisID: 'ySuccess',
                        tension: 0.1
                    },
                    {
                        label: 'Semantic Score (0-1)', // Overall score
                        data: semanticScores,
                        borderColor: 'rgb(255, 159, 64)',
                        backgroundColor: 'rgba(255, 159, 64, 0.1)',
                        yAxisID: 'ySemantic',
                        tension: 0.1,
                        spanGaps: true // Connect lines across null data points
                    }
                ]
            };

            chartConfigOptions = { // Options for Aggregated View
                responsive: true,
                maintainAspectRatio: false,
                interaction: { mode: 'index', intersect: false },
                stacked: false,
                plugins: {
                    title: { display: true, text: 'Benchmark Execution Sessions Trend' },
                    tooltip: { mode: 'index', intersect: false }
                },
                scales: {
                    x: {
                        type: 'time',
                        time: { unit: 'day', tooltipFormat: 'PPpp', displayFormats: { day: 'MMM d, yyyy' } },
                        title: { display: true, text: 'Execution Date' }
                    },
                    yDuration: {
                        type: 'linear', display: true, position: 'left',
                        title: { display: true, text: 'Duration (ms)' },
                        beginAtZero: true
                    },
                    ySuccess: {
                        type: 'linear', display: true, position: 'right',
                        title: { display: true, text: 'Success Rate (%)' },
                        min: 0, max: 100,
                        grid: { drawOnChartArea: false }
                    },
                    ySemantic: {
                        type: 'linear', display: true, position: 'right',
                        title: { display: true, text: 'Overall Semantic Score (0-1)' },
                        min: 0, max: 1,
                        grid: { drawOnChartArea: false }
                    }
                }
            }; // End Aggregated View Options
        } // End else (Aggregated View)

        // --- Draw the chart using the determined config ---
        const ctx = historyChartEl.getContext('2d');
        historyChartInstance = new Chart(ctx, {
            type: 'line',
            data: chartConfigData,
            options: chartConfigOptions
        });

    } // End drawChart function


    // --- Update Table Function ---
    function updateTable(runs) {
        const table = tableContainer.querySelector('table');
        let tbody = table ? table.querySelector('tbody') : null;

        // Ensure table and tbody exist, and add checkbox header if missing
        if (!table) {
            tableContainer.innerHTML = `
                <table class="benchmark-table">
                    <thead>
                        <tr>
                            <th><input type="checkbox" id="select-all-checkbox" title="Select/Deselect All Visible"></th>
                            <th>Scenario</th><th>Evaluation Type</th><th>Agent Role</th><th>Runs Count</th>
                            <th>LLM Model</th><th>LLM Temp</th><th>Execution Mode</th><th>Date</th>
                            <th>Mean Duration (ms)</th><th>Success Rate</th><th>Semantic Score</th>
                            <th>Tokens In</th><th>Tokens Out</th><th>Est. Cost</th><th>Status</th><th>Actions</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
                <p id="table-no-data-message" style="display: none; text-align: center; padding: 20px;">No benchmark execution sessions match the selected filters.</p>`;
            tbody = tableContainer.querySelector('tbody');
        } else if (!tbody) {
            tbody = document.createElement('tbody');
            table.appendChild(tbody);
        }

        // Clear existing rows
        tbody.innerHTML = '';
        const currentNoDataMsg = tableContainer.querySelector('#table-no-data-message');
        if (currentNoDataMsg) currentNoDataMsg.style.display = 'none'; // Hide no data message initially

        if (runs && runs.length > 0) {
            table.style.display = ''; // Show table
            runs.forEach(execution => {
                const row = tbody.insertRow();
                const semanticScoreDisplay = execution.semantic_score !== null ? parseFloat(execution.semantic_score).toFixed(2) : 'N/A';
                const meanDurationDisplay = execution.mean_duration !== null ? parseFloat(execution.mean_duration).toFixed(2) : 'N/A';
                // Success rate is already a percentage (0-1 scale from backend)
                const successRateDisplay = execution.success_rate !== null ? (parseFloat(execution.success_rate) * 100).toFixed(1) + '%' : 'N/A';
                const executionDate = new Date(execution.execution_date).toLocaleString(); // Format date
                const llmTempDisplay = execution.llm_temperature !== null ? parseFloat(execution.llm_temperature).toFixed(1) : 'N/A';
                const tokensInDisplay = execution.total_input_tokens ?? 'N/A';
                const tokensOutDisplay = execution.total_output_tokens ?? 'N/A';
                const costDisplay = execution.estimated_cost !== null ? '$' + parseFloat(execution.estimated_cost).toFixed(4) : 'N/A';

                // Helper function to get execution mode display
                function getExecutionModeDisplay(execution) {
                    // Check if this is a workflow execution with real operation flags
                    if (execution.execution_type && execution.execution_type.includes('Workflow')) {
                        const params = execution.parameters || {};

                        // Get requested execution mode
                        const requestedRealLlm = params.use_real_llm || false;
                        const requestedRealTools = params.use_real_tools || false;
                        const requestedRealDb = params.use_real_db || false;

                        // Get actual execution mode (if available)
                        const actualRealLlm = params.actual_real_llm_used || false;
                        const actualRealTools = params.actual_real_tools_used || false;
                        const actualRealDb = params.actual_real_db_used || false;

                        // Check for silent fallbacks
                        const hasSilentFallbacks = params.has_silent_fallbacks || false;
                        const silentFallbacks = params.silent_fallbacks || [];

                        // Determine display based on actual usage (if available) or requested usage
                        const useActual = params.actual_execution_mode !== undefined;
                        const realLlm = useActual ? actualRealLlm : requestedRealLlm;
                        const realTools = useActual ? actualRealTools : requestedRealTools;
                        const realDb = useActual ? actualRealDb : requestedRealDb;

                        if (realLlm || realTools || realDb) {
                            const realComponents = [];
                            if (realLlm) realComponents.push('LLM');
                            if (realTools) realComponents.push('Tools');
                            if (realDb) realComponents.push('DB');

                            // Show warning if there were silent fallbacks
                            if (hasSilentFallbacks && silentFallbacks.length > 0) {
                                const fallbackText = silentFallbacks.join(', ');
                                return `<span class="execution-mode warning" title="Requested real mode but fell back to mock for: ${fallbackText}">🔴 Real (${realComponents.join(', ')}) ⚠️</span>`;
                            } else {
                                return `<span class="execution-mode real" title="Real: ${realComponents.join(', ')}">🔴 Real (${realComponents.join(', ')})</span>`;
                            }
                        } else {
                            // Check if real mode was requested but everything fell back to mock
                            if ((requestedRealLlm || requestedRealTools || requestedRealDb) && hasSilentFallbacks) {
                                const fallbackText = silentFallbacks.join(', ');
                                return `<span class="execution-mode warning" title="Requested real mode but fell back to mock for: ${fallbackText}">🟡 Mock (Fallback) ⚠️</span>`;
                            } else {
                                return `<span class="execution-mode mock" title="All components mocked">🟡 Mock</span>`;
                            }
                        }
                    } else {
                        // Agent evaluation - check if real LLM was used
                        const params = execution.parameters || {};
                        const requestedRealLlm = params.use_real_llm || false;
                        const actualRealLlm = params.actual_real_llm_used;
                        const hasSilentFallbacks = params.has_silent_fallbacks || false;

                        // Use actual if available, otherwise use requested
                        const realLlm = actualRealLlm !== undefined ? actualRealLlm : requestedRealLlm;

                        if (realLlm) {
                            if (hasSilentFallbacks) {
                                return `<span class="execution-mode warning" title="Requested real LLM but encountered fallbacks">🔴 Real LLM ⚠️</span>`;
                            } else {
                                return `<span class="execution-mode real" title="Real LLM used">🔴 Real LLM</span>`;
                            }
                        } else {
                            if (requestedRealLlm && hasSilentFallbacks) {
                                return `<span class="execution-mode warning" title="Requested real LLM but fell back to mock">🟡 Mock LLM (Fallback) ⚠️</span>`;
                            } else {
                                return `<span class="execution-mode mock" title="Mock LLM used">🟡 Mock LLM</span>`;
                            }
                        }
                    }
                }

                // Format execution type with appropriate styling and evaluation context indicators
                const executionTypeDisplay = execution.execution_type || 'Agent Evaluation';
                const executionTypeClass = execution.execution_type && execution.execution_type.includes('Workflow') ? 'workflow-evaluation' : 'agent-evaluation';

                // For workflow evaluations, agent role should be empty
                const isWorkflowEvaluation = execution.execution_type && execution.execution_type.includes('Workflow');
                const agentRoleDisplay = isWorkflowEvaluation ? '' : (execution.agent_role ? execution.agent_role.charAt(0).toUpperCase() + execution.agent_role.slice(1) : 'N/A');

                // Create evaluation context indicator with tooltip
                const evaluationContextType = isWorkflowEvaluation ? 'workflow' : 'agent';
                const evaluationFocus = isWorkflowEvaluation
                    ? 'Output Quality & System Performance'
                    : 'Communication Style & Decision Making';
                const toneAnalysisStatus = isWorkflowEvaluation ? 'Excluded' : 'Included';

                const evaluationContextIndicator = `
                    <div class="tooltip">
                        <span class="evaluation-context-indicator ${evaluationContextType}">
                            ${evaluationContextType.toUpperCase()}
                        </span>
                        <span class="tooltiptext">
                            <strong>Evaluation Focus:</strong> ${evaluationFocus}<br>
                            <strong>Tone Analysis:</strong> ${toneAnalysisStatus}<br>
                            <strong>Type:</strong> ${isWorkflowEvaluation ? 'Multi-agent workflow coordination' : 'Individual agent performance'}
                        </span>
                    </div>
                `;

                // Enhanced execution type display with context indicator
                const enhancedExecutionTypeDisplay = `
                    <span class="${executionTypeClass}">${executionTypeDisplay}</span>
                    ${evaluationContextIndicator}
                `;

                // Check for errors in the execution
                let statusDisplay = '<span class="status-success">✅ Success</span>';
                let statusClass = 'status-success';

                if (execution.raw_results && execution.raw_results.errors && execution.raw_results.errors.length > 0) {
                    const errors = execution.raw_results.errors;
                    const criticalErrors = errors.filter(e => e.type === 'critical' || e.level === 'error').length;
                    const warnings = errors.filter(e => e.type === 'warning' || e.level === 'warning').length;

                    if (criticalErrors > 0) {
                        statusDisplay = `<span class="status-error" title="${criticalErrors} critical error(s), ${warnings} warning(s)">❌ ${criticalErrors} Error(s)</span>`;
                        statusClass = 'status-error';
                    } else if (warnings > 0) {
                        statusDisplay = `<span class="status-warning" title="${warnings} warning(s)">⚠️ ${warnings} Warning(s)</span>`;
                        statusClass = 'status-warning';
                    }
                } else if (execution.success_rate !== null && execution.success_rate < 0.5) {
                    // Low success rate indicates issues
                    statusDisplay = '<span class="status-warning" title="Low success rate">⚠️ Low Success</span>';
                    statusClass = 'status-warning';
                }

                row.innerHTML = `
                    <td><input type="checkbox" class="run-checkbox" data-run-id="${execution.id}"></td>
                    <td>${execution.scenario_name || 'N/A'}</td>
                    <td>${enhancedExecutionTypeDisplay}</td>
                    <td>${agentRoleDisplay}</td>
                    <td><span class="runs-count" title="Number of individual runs in this execution session">${execution.runs_count || 1}</span></td>
                    <td>${execution.agent_llm_model_name || 'N/A'}</td>
                    <td>${llmTempDisplay}</td>
                    <td>${getExecutionModeDisplay(execution)}</td>
                    <td>${executionDate}</td>
                    <td>${meanDurationDisplay}</td>
                    <td>${successRateDisplay}</td>
                    <td>${semanticScoreDisplay}</td>
                    <td>${tokensInDisplay}</td>
                    <td>${tokensOutDisplay}</td>
                    <td>${costDisplay}</td>
                    <td class="${statusClass}">${statusDisplay}</td>
                    <td>
                        <a href="#" class="view-details btn btn-secondary" data-run-id="${execution.id}">View Details</a>
                    </td>
                `;

                // Add error styling to the entire row if there are critical errors
                if (statusClass === 'status-error') {
                    row.classList.add('error-row');
                } else if (statusClass === 'status-warning') {
                    row.classList.add('warning-row');
                }
            }); // End of forEach loop

            // Ensure listeners are attached *after* rows are added
            attachModalListeners();
            attachCheckboxListeners();
            updateCompareButtonState(); // Update button state after table redraw

        } else {
            // Show no data message
            table.style.display = 'none'; // Hide table
            const noDataMsg = tableContainer.querySelector('#table-no-data-message');
            if (noDataMsg) noDataMsg.style.display = 'block';
        }
    }

    // Make toggleSection globally accessible
    window.toggleSection = function(header) {
        const content = header.nextElementSibling;
        const icon = header.querySelector('.toggle-icon');

        if (content.classList.contains('expanded')) {
            content.classList.remove('expanded');
            icon.textContent = '▶';
        } else {
            content.classList.add('expanded');
            icon.textContent = '▼';
        }
    };


    // --- Fetch Data and Update UI ---
    async function fetchDataAndUpdateUI() {
        const formData = new FormData(filterForm);
        const params = new URLSearchParams(formData);

        // Debug: Log filter parameters
        console.log('Filter parameters being sent:', Object.fromEntries(params));

        // Corrected API URL to fetch filtered run data AND chart data
        const apiUrl = `{% url 'game_of_life_admin:benchmark_runs_api' %}?${params.toString()}&include_chart_data=true`;
        console.log('API URL:', apiUrl);

        // Show loading indicators
        chartLoadingMessage.style.display = 'block';
        tableLoadingMessage.style.display = 'block';
        historyChartEl.style.display = 'none'; // Hide chart canvas
        tableContainer.style.display = 'none'; // Hide table container
        const currentNoDataMsg = tableContainer.querySelector('#table-no-data-message');
        if (currentNoDataMsg) currentNoDataMsg.style.display = 'none'; // Hide no data message
        chartNoDataMessage.style.display = 'none'; // Hide chart no data message


        try {
            const response = await fetch(apiUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = await response.json();

            // Determine if it's scenario view based on API response or filters
            // The backend view doesn't explicitly return is_scenario_view in the API response yet.
            // We can infer it based on whether dimension_data exists and has keys.
            // Or, more reliably, check the scenario filter value directly.
            const scenarioSelect = document.getElementById('scenario');
            const isScenarioView = scenarioSelect && scenarioSelect.value !== '';

            // Update UI with fetched data
            // Pass the correct arguments based on the view type
            drawChart(
                isScenarioView,
                data.chart_data?.labels || [],
                data.chart_data?.dimension_data || {},
                data.chart_data?.mean_durations || [],
                data.chart_data?.median_durations || [],
                data.chart_data?.success_rates || [],
                data.chart_data?.semantic_scores || [],
                data.chart_data?.llm_models || [],      // Pass LLM models
                data.chart_data?.agent_versions || [] // Pass agent versions
            );

            const runs = data.runs || [];
            console.log('Received runs data:', runs);
            console.log('Number of runs:', runs.length);
            updateTable(runs); // Update table with runs data

        } catch (error) {
            console.error('Error fetching benchmark data:', error);
            // Show error messages
            chartNoDataMessage.textContent = 'Error loading chart data.';
            chartNoDataMessage.style.display = 'block';
            tableContainer.innerHTML = '<p class="errornote">Error loading table data.</p>';
            tableContainer.style.display = 'block';
        } finally {
            // Hide loading indicators
            chartLoadingMessage.style.display = 'none';
            tableLoadingMessage.style.display = 'none';
        }
    }

    // --- Initial Chart Draw (using data from Django context) ---
    // Ensure llm_models_json and agent_versions_json are added to the context in views.py
    const isScenarioViewInitial = "{{ is_scenario_view|yesno:'true,false' }}";
    drawChart(
        isScenarioViewInitial,
        JSON.parse('{{ chart_labels_json|escapejs|default:"[]" }}'),
        JSON.parse('{{ dimension_data_json|escapejs|default:"{}" }}'),
        JSON.parse('{{ mean_durations_json|escapejs|default:"[]" }}'), // Pass mean durations for scenario view too
        JSON.parse('{{ median_durations_json|escapejs|default:"[]" }}'),
        JSON.parse('{{ success_rates_json|escapejs|default:"[]" }}'),
        JSON.parse('{{ semantic_scores_json|escapejs|default:"[]" }}'),
        JSON.parse('{{ llm_models_json|escapejs|default:"[]" }}'),      // Pass LLM models
        JSON.parse('{{ agent_versions_json|escapejs|default:"[]" }}') // Pass agent versions
    );

    // Update table with initial data
    const initialRuns = JSON.parse('{{ benchmark_runs_json|escapejs|default:"[]" }}');
    console.log("Initial Runs parsed:", initialRuns); // DEBUG: Log parsed initial runs
    try {
        updateTable(initialRuns);
        console.log("updateTable called successfully with initialRuns."); // DEBUG: Confirm call
    } catch (e) {
        console.error("Error calling updateTable with initialRuns:", e); // DEBUG: Catch errors
    }


    // --- Event Listeners ---

    // Filter form submission
    filterForm.addEventListener('submit', function(event) {
        event.preventDefault(); // Prevent default form submission
        fetchDataAndUpdateUI();
    });


    // --- Modal Functionality (Needs to be re-attached after table update) ---
    // Variable declarations moved to the top of the DOMContentLoaded scope

    function attachModalListeners() {
        // --- Agent Details Modal ---
        if (agentCloseButton) {
            agentCloseButton.onclick = function() {
                agentDetailsModal.style.display = 'none';
                if (window.modalToolChartInstance) {
                    window.modalToolChartInstance.destroy();
                    window.modalToolChartInstance = null;
                }
            };
        }

        // --- Workflow Details Modal ---
        if (workflowCloseButton) {
            workflowCloseButton.onclick = function() {
                workflowDetailsModal.style.display = 'none';
                if (window.modalToolChartInstance) {
                    window.modalToolChartInstance.destroy();
                    window.modalToolChartInstance = null;
                }
            };
        }

        // Close modal when clicking outside (handle all modals)
        window.onclick = function(event) {
            if (event.target == agentDetailsModal) {
                agentDetailsModal.style.display = 'none';
                if (window.modalToolChartInstance) {
                    window.modalToolChartInstance.destroy();
                    window.modalToolChartInstance = null;
                }
            } else if (event.target == workflowDetailsModal) {
                workflowDetailsModal.style.display = 'none';
                if (window.modalToolChartInstance) {
                    window.modalToolChartInstance.destroy();
                    window.modalToolChartInstance = null;
                }
            } else if (event.target == comparisonModal) {
                comparisonModal.style.display = 'none';
            }
        };

        // --- Comparison Modal ---
        comparisonCloseButton.onclick = function() {
            comparisonModal.style.display = 'none';
        };

        // --- Common for both ---
        // Use event delegation for dynamically added elements
        tableContainer.removeEventListener('click', handleTableClick); // Remove previous listener if any
        tableContainer.addEventListener('click', handleTableClick);
    }

    // --- Event Handler for Table Clicks (Delegation) ---
    function handleTableClick(event) {
        // Check if the clicked element is a "View Details" link
        if (event.target.matches('.view-details')) {
            handleViewDetailsClick.call(event.target, event); // Call the original handler with correct 'this' context
        }
        // Add other delegated listeners here if needed (e.g., for checkboxes)
        else if (event.target.matches('.run-checkbox')) {
             handleCheckboxChange.call(event.target, event);
        }
    }

    // --- Checkbox and Comparison Logic ---
    function updateCompareButtonState() {
        const count = selectedRunIds.size;
        compareButton.textContent = `Compare Selected (${count})`;
        compareButton.disabled = count !== 2; // Enable only when exactly 2 are selected
    }

    function attachCheckboxListeners() {
        const checkboxes = tableContainer.querySelectorAll('.run-checkbox');
        checkboxes.forEach(checkbox => {
            // Remove existing listener first
            checkbox.removeEventListener('change', handleCheckboxChange);
            // Add listener
            checkbox.addEventListener('change', handleCheckboxChange);
            // Sync checkbox state with the selectedRunIds set
            checkbox.checked = selectedRunIds.has(checkbox.getAttribute('data-run-id'));
        });

        // Select All Checkbox Listener
        if (selectAllCheckbox) {
            selectAllCheckbox.removeEventListener('change', handleSelectAllChange); // Remove first
            selectAllCheckbox.addEventListener('change', handleSelectAllChange);
            // Determine initial state of select-all
            const allVisibleChecked = checkboxes.length > 0 && Array.from(checkboxes).every(cb => cb.checked);
            selectAllCheckbox.checked = allVisibleChecked;
            selectAllCheckbox.indeterminate = !allVisibleChecked && selectedRunIds.size > 0 && selectedRunIds.size < checkboxes.length;
        }
    }

    function handleCheckboxChange(event) {
        const runId = event.target.getAttribute('data-run-id');
        if (event.target.checked) {
            selectedRunIds.add(runId);
        } else {
            selectedRunIds.delete(runId);
        }
        updateCompareButtonState();
        // Update select-all checkbox state
        const checkboxes = tableContainer.querySelectorAll('.run-checkbox');
        const allVisibleChecked = checkboxes.length > 0 && Array.from(checkboxes).every(cb => cb.checked);
        selectAllCheckbox.checked = allVisibleChecked;
        selectAllCheckbox.indeterminate = !allVisibleChecked && selectedRunIds.size > 0;
    }

    function handleSelectAllChange(event) {
        const isChecked = event.target.checked;
        const checkboxes = tableContainer.querySelectorAll('.run-checkbox');
        checkboxes.forEach(checkbox => {
            const runId = checkbox.getAttribute('data-run-id');
            checkbox.checked = isChecked;
            if (isChecked) {
                selectedRunIds.add(runId);
            } else {
                selectedRunIds.delete(runId);
            }
        });
        updateCompareButtonState();
        selectAllCheckbox.indeterminate = false; // Reset indeterminate state
    }

    // Compare Button Listener
    compareButton.addEventListener('click', async function() {
        if (selectedRunIds.size !== 2) return; // Should be disabled, but double-check

        comparisonModalBody.innerHTML = '<div class="loader"></div> Loading comparison...';
        comparisonModal.style.display = 'block';

        const ids = Array.from(selectedRunIds).join(',');
        const apiUrl = `{% url 'game_of_life_admin:benchmark_runs_api' %}?run_ids=${ids}`;

        try {
            const response = await fetch(apiUrl);
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || `Failed to fetch comparison data (HTTP ${response.status})`);
            }
            const data = await response.json();

            if (!data.runs || data.runs.length !== 2) {
                throw new Error("API did not return exactly two runs for comparison.");
            }

            // Sort runs by date ascending for consistent column order
            data.runs.sort((a, b) => new Date(a.execution_date) - new Date(b.execution_date));

            // Build comparison HTML
            comparisonModalBody.innerHTML = ''; // Clear loader
            data.runs.forEach(run => {
                const column = document.createElement('div');
                column.classList.add('comparison-column');

                // Helper to format values
                const formatVal = (val, decimals = 2, suffix = '') => val !== null && val !== undefined ? parseFloat(val).toFixed(decimals) + suffix : 'N/A';
                const formatPercent = (val) => val !== null && val !== undefined ? (parseFloat(val) * 100).toFixed(1) + '%' : 'N/A';
                const formatDate = (iso) => iso ? new Date(iso).toLocaleString() : 'N/A';

                column.innerHTML = `
                    <h4>Run: ${run.id.substring(0, 8)}...</h4>
                    <div class="comparison-metric"><strong>Scenario:</strong> ${run.scenario || 'N/A'}</div>
                    <div class="comparison-metric"><strong>Scenario:</strong> ${run.scenario || 'N/A'}</div>
                    <div class="comparison-metric"><strong>Agent Role:</strong> ${run.agent_role || 'N/A'}</div>
                    <div class="comparison-metric"><strong>Agent Version:</strong> ${run.agent_version || 'N/A'}</div>
                    <div class="comparison-metric"><strong>LLM Model:</strong> ${run.llm_model || 'N/A'}</div>
                    <div class="comparison-metric"><strong>LLM Temp:</strong> ${formatVal(run.llm_temperature, 1)}</div> {# Added Temp #}
                    <div class="comparison-metric"><strong>Date:</strong> ${formatDate(run.execution_date)}</div>
                    <hr>
                    <div class="comparison-metric"><strong>Mean Duration:</strong> ${formatVal(run.mean_duration, 2, 'ms')}</div>
                    <div class="comparison-metric"><strong>Median Duration:</strong> ${formatVal(run.median_duration, 2, 'ms')}</div>
                    <div class="comparison-metric"><strong>Min Duration:</strong> ${formatVal(run.min_duration, 2, 'ms')}</div>
                    <div class="comparison-metric"><strong>Max Duration:</strong> ${formatVal(run.max_duration, 2, 'ms')}</div>
                    <div class="comparison-metric"><strong>Std Dev:</strong> ${formatVal(run.std_dev)}</div>
                    <div class="comparison-metric"><strong>Success Rate:</strong> ${formatPercent(run.success_rate)}</div>
                    <hr>
                    <div class="comparison-metric"><strong>LLM Calls:</strong> ${run.llm_calls ?? 'N/A'}</div>
                    <div class="comparison-metric"><strong>Tool Calls:</strong> ${run.tool_calls ?? 'N/A'}</div>
                    <div class="comparison-metric"><strong>Tokens In:</strong> ${run.total_input_tokens ?? 'N/A'}</div> {# Added Tokens In #}
                    <div class="comparison-metric"><strong>Tokens Out:</strong> ${run.total_output_tokens ?? 'N/A'}</div> {# Added Tokens Out #}
                    <div class="comparison-metric"><strong>Est. Cost:</strong> ${formatVal(run.estimated_cost, 4, '$')}</div> {# Added Cost #}
                    <div class="comparison-metric"><strong>Tool Breakdown:</strong> <pre>${JSON.stringify(run.tool_breakdown || {}, null, 2)}</pre></div>
                    <hr>
                    <div class="comparison-metric"><strong>Semantic Score:</strong> ${formatVal(run.semantic_score, 2)}</div> {# Changed format to 0-1 #}
                    <div class="comparison-metric"><strong>Semantic Details:</strong> <pre>${JSON.stringify(run.semantic_evaluation_details || {}, null, 2)}</pre></div>
                    <div class="comparison-metric"><strong>Multi-Model Eval:</strong> <pre>${JSON.stringify(run.semantic_evaluations || {}, null, 2)}</pre></div>
                    <hr>
                    <div class="comparison-metric"><strong>Parameters:</strong> <pre>${JSON.stringify(run.parameters || {}, null, 2)}</pre></div>
                    <div class="comparison-metric"><strong>Stage Perf:</strong> <pre>${JSON.stringify(run.stage_performance_details || {}, null, 2)}</pre></div>
                    ${run.comparison_results ? `
                        <hr>
                        <div class="comparison-metric"><strong>Stat Comp. vs:</strong> ${run.comparison_results.compared_to_run_id ? run.comparison_results.compared_to_run_id.substring(0,8)+'...' : 'N/A'}</div>
                        <div class="comparison-metric"><strong>P-Value:</strong> ${formatVal(run.comparison_results.performance_p_value, 4)}</div>
                        <div class="comparison-metric"><strong>Significant?:</strong> ${run.comparison_results.is_performance_significant !== null ? (run.comparison_results.is_performance_significant ? 'Yes' : 'No') : 'N/A'}</div>
                    ` : ''}
                `;
                comparisonModalBody.appendChild(column);
            });

        } catch (error) {
            console.error('Error fetching comparison data:', error);
            comparisonModalBody.innerHTML = `<div class="alert alert-danger" style="width: 100%;">Error loading comparison: ${error.message}</div>`;
        }
    });


    // --- Details Modal Logic (Updated for specialized modals) ---
    async function handleViewDetailsClick(e) {
        e.preventDefault(); // Prevent default link behavior immediately
        console.log("View Details clicked for run ID:", this.getAttribute('data-run-id'));

        const runId = this.getAttribute('data-run-id');

        try {
            // Fetch data first to determine evaluation type
            const response = await fetch(`{% url 'game_of_life_admin:benchmark_runs_detail_api' run_id=0 %}`.replace('0', runId));
             if (!response.ok) {
                 const errorData = await response.json();
                 throw new Error(errorData.error || `Failed to fetch details (HTTP ${response.status})`);
             }
            const data = await response.json();

            // Determine if this is a workflow or agent evaluation
            const isWorkflowEvaluation = data.execution_type && data.execution_type.includes('Workflow');

            if (isWorkflowEvaluation) {
                // Show workflow modal
                const workflowModalBody = document.getElementById('workflow-modal-body');
                if (workflowModalBody) {
                    workflowModalBody.innerHTML = '<div class="loader"></div> Loading...';
                    workflowDetailsModal.style.display = 'block';
                    await renderWorkflowDetails(workflowModalBody, data, runId);
                } else {
                    console.error('Workflow modal body element not found.');
                    alert('Error: Workflow modal not available.');
                }
            } else {
                // Show agent modal
                const agentModalBody = document.getElementById('agent-modal-body');
                if (agentModalBody) {
                    agentModalBody.innerHTML = '<div class="loader"></div> Loading...';
                    agentDetailsModal.style.display = 'block';
                    await renderAgentDetails(agentModalBody, data, runId);
                } else {
                    console.error('Agent modal body element not found.');
                    alert('Error: Agent modal not available.');
                }
            }

        } catch (error) {
            console.error('Error fetching details:', error);
            const errorMessage = `<div class="alert alert-danger">Error fetching benchmark details: ${error.message}</div>`;

            // Attempt to display error in the currently active modal or default
            const currentAgentModalBody = document.getElementById('agent-modal-body');
            const currentWorkflowModalBody = document.getElementById('workflow-modal-body');

            if (agentDetailsModal && agentDetailsModal.style.display === 'block' && currentAgentModalBody) {
                currentAgentModalBody.innerHTML = errorMessage;
            } else if (workflowDetailsModal && workflowDetailsModal.style.display === 'block' && currentWorkflowModalBody) {
                currentWorkflowModalBody.innerHTML = errorMessage;
            } else {
                // Fallback: if no modal is open or elements are missing, show a general alert
                alert('Error fetching benchmark details: ' + error.message);
            }
        }
    }

    // Note: renderAgentDetails and renderWorkflowDetails functions are now in separate modal files

    // Shared utility functions for modals - Made global for modal access
    window.renderSemanticEvaluationDetails = function(container, data) {
        container.innerHTML = '';

        if (data.semantic_evaluation_details && Object.keys(data.semantic_evaluation_details).length > 0) {
            const primaryDetailsDiv = document.createElement('div');
            primaryDetailsDiv.innerHTML = `
                <h4>Primary Evaluator Details</h4>
                <pre class="json-viewer">${JSON.stringify(data.semantic_evaluation_details, null, 2)}</pre>
            `;
            container.appendChild(primaryDetailsDiv);
        }

        if (data.semantic_evaluations && typeof data.semantic_evaluations === 'object' && Object.keys(data.semantic_evaluations).length > 0) {
            const multiModelDiv = document.createElement('div');
            multiModelDiv.innerHTML = '<h4>Multi-Model Dimensional Evaluation</h4>';

            for (const modelName in data.semantic_evaluations) {
                const evaluation = data.semantic_evaluations[modelName];
                const modelSection = document.createElement('div');
                modelSection.style.marginBottom = '15px';
                modelSection.style.paddingLeft = '10px';
                modelSection.style.borderLeft = '2px solid #eee';

                let dimensionsHtml = '<h5>Dimensions:</h5><ul>';
                if (evaluation && evaluation.dimensions && typeof evaluation.dimensions === 'object') {
                    for (const dimName in evaluation.dimensions) {
                        const dimData = evaluation.dimensions[dimName] || {};
                        const score = dimData.score !== null && dimData.score !== undefined ? parseFloat(dimData.score).toFixed(2) : 'N/A';
                        const reasoning = dimData.reasoning ? dimData.reasoning : 'No reasoning provided.';
                        dimensionsHtml += `<li><strong>${dimName}</strong> (Score: ${score}): ${reasoning}</li>`;
                    }
                } else {
                    dimensionsHtml += '<li>No dimensional data available.</li>';
                }
                dimensionsHtml += '</ul>';

                const overallScore = evaluation && evaluation.overall_score !== null && evaluation.overall_score !== undefined ? parseFloat(evaluation.overall_score).toFixed(2) : 'N/A';
                const overallReasoning = evaluation && evaluation.overall_reasoning ? evaluation.overall_reasoning : 'No overall reasoning provided.';
                const errorStatus = evaluation && evaluation.error ? '<span style="color: red;"> (Error during evaluation)</span>' : '';

                modelSection.innerHTML = `
                    <h5 style="margin-bottom: 5px;">Evaluator: ${modelName}${errorStatus}</h5>
                    <p><strong>Overall Score:</strong> ${overallScore}</p>
                    <p><strong>Overall Reasoning:</strong> ${overallReasoning}</p>
                    ${dimensionsHtml}
                `;
                multiModelDiv.appendChild(modelSection);
            }
            container.appendChild(multiModelDiv);
        }
    }

    // Helper function to render context package details - Made global for modal access
    window.renderContextPackageDetails = function(container, contextPackage) {
        // Implementation would be similar to the existing context package rendering
        // but simplified for the agent modal
        container.innerHTML = `
            <div class="context-summary">
                <h4>Context Summary</h4>
                <pre class="json-viewer">${JSON.stringify(contextPackage.summary || {}, null, 2)}</pre>
            </div>
        `;
    }

    // Helper function to render workflow agent communications
    function renderWorkflowAgentCommunications(container, agentComms) {
        container.innerHTML = '';

        // Create summary cards first
        if (agentComms.summary) {
            const summaryContainer = document.createElement('div');
            summaryContainer.className = 'agent-communications-summary';
            summaryContainer.innerHTML = renderAgentCommunicationsSummary(agentComms.summary, agentComms.workflow_id);
            container.appendChild(summaryContainer);
        }

        // Create tabbed interface for agent communications
        const tabsContainer = document.createElement('div');
        tabsContainer.className = 'context-tabs';
        tabsContainer.innerHTML = `
            <div class="tab-buttons">
                <button class="tab-button active" data-tab="overview">Overview</button>
                <button class="tab-button" data-tab="agents">Agent Timeline</button>
                <button class="tab-button" data-tab="transitions">State Transitions</button>
                <button class="tab-button" data-tab="raw">Raw Data</button>
            </div>
            <div class="tab-content">
                <div id="overview-tab" class="tab-pane active">
                    <!-- Overview will be rendered here -->
                </div>
                <div id="agents-tab" class="tab-pane">
                    <!-- Agent interactions will be rendered here -->
                </div>
                <div id="transitions-tab" class="tab-pane">
                    <!-- State transitions will be rendered here -->
                </div>
                <div id="raw-tab" class="tab-pane">
                    <!-- Raw data will be rendered here -->
                </div>
            </div>
        `;

        container.appendChild(tabsContainer);

        // Render overview tab
        const overviewTab = container.querySelector('#overview-tab');
        overviewTab.innerHTML = renderAgentCommunicationsOverview(agentComms);

        // Render agent interactions tab with enhanced timeline
        const agentsTab = container.querySelector('#agents-tab');
        if (agentComms.agents && agentComms.agents.length > 0) {
            agentsTab.innerHTML = renderEnhancedAgentTimeline(agentComms.agents);
        } else {
            agentsTab.innerHTML = '<p>No agent interactions recorded.</p>';
        }

        // Render state transitions tab
        const transitionsTab = container.querySelector('#transitions-tab');
        if (agentComms.state_transitions && agentComms.state_transitions.length > 0) {
            transitionsTab.innerHTML = renderStateTransitions(agentComms.state_transitions);
        } else {
            transitionsTab.innerHTML = '<p>No state transitions recorded.</p>';
        }

        // Render raw data tab
        const rawTab = container.querySelector('#raw-tab');
        rawTab.innerHTML = `
            <div class="expandable-section">
                <div class="section-header" onclick="toggleSection(this)">
                    <h4>📋 Complete Agent Communications Data</h4>
                    <span class="toggle-icon">▼</span>
                </div>
                <div class="section-content">
                    <div class="json-viewer">
                        <pre>${JSON.stringify(agentComms, null, 2)}</pre>
                    </div>
                </div>
            </div>
        `;

        // Add tab switching functionality
        const tabButtons = container.querySelectorAll('.tab-button');
        const tabPanes = container.querySelectorAll('.tab-pane');

        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const targetTab = button.getAttribute('data-tab');

                // Remove active class from all buttons and panes
                tabButtons.forEach(btn => btn.classList.remove('active'));
                tabPanes.forEach(pane => pane.classList.remove('active'));

                // Add active class to clicked button and corresponding pane
                button.classList.add('active');
                const targetPane = container.querySelector(`#${targetTab}-tab`);
                if (targetPane) {
                    targetPane.classList.add('active');
                }
            });
        });
    }

    function renderAnalysisTab(analysis) {
        const contextImpact = analysis.context_impact || {};
        const agentBehavior = analysis.agent_behavior || {};
        const performancePatterns = analysis.performance_patterns || {};
        const recommendations = analysis.recommendations || [];

        return `
            <div class="analysis-content">
                <div class="expandable-section">
                    <div class="section-header" onclick="toggleSection(this)">
                        <h4>🎯 Context Impact Analysis</h4>
                        <span class="toggle-icon">▼</span>
                    </div>
                    <div class="section-content expanded">
                        <div class="impact-grid">
                            <div class="impact-item">
                                <strong>Trust Influence:</strong>
                                <span>${contextImpact.trust_influence || 'N/A'}</span>
                            </div>
                            <div class="impact-item">
                                <strong>Stress Influence:</strong>
                                <span>${contextImpact.stress_influence || 'N/A'}</span>
                            </div>
                            <div class="impact-item">
                                <strong>Mood Influence:</strong>
                                <span>${contextImpact.mood_influence || 'N/A'}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="expandable-section">
                    <div class="section-header" onclick="toggleSection(this)">
                        <h4>🤖 Agent Behavior Analysis</h4>
                        <span class="toggle-icon">▼</span>
                    </div>
                    <div class="section-content expanded">
                        <div class="behavior-grid">
                            <div class="behavior-item">
                                <strong>Response Length:</strong>
                                <span>${agentBehavior.response_length || 0} characters</span>
                            </div>
                            <div class="behavior-item">
                                <strong>Reasoning Depth:</strong>
                                <span>${agentBehavior.reasoning_depth || 0} characters</span>
                            </div>
                            <div class="behavior-item">
                                <strong>Response Type:</strong>
                                <span>${agentBehavior.response_type || 'N/A'}</span>
                            </div>
                            <div class="behavior-item">
                                <strong>Confidence Level:</strong>
                                <span>${agentBehavior.confidence_level || 'N/A'}</span>
                            </div>
                        </div>
                    </div>
                </div>

                ${Object.keys(performancePatterns).length > 0 ? `
                <div class="expandable-section">
                    <div class="section-header" onclick="toggleSection(this)">
                        <h4>📊 Performance Patterns</h4>
                        <span class="toggle-icon">▼</span>
                    </div>
                    <div class="section-content">
                        <div class="patterns-grid">
                            <div class="pattern-item">
                                <strong>Consistency:</strong>
                                <span>${performancePatterns.consistency || 'N/A'}</span>
                            </div>
                            <div class="pattern-item">
                                <strong>Improvement Trend:</strong>
                                <span>${performancePatterns.improvement_trend || 'N/A'}</span>
                            </div>
                            <div class="pattern-item">
                                <strong>Variability:</strong>
                                <span>${performancePatterns.variability ? performancePatterns.variability.toFixed(0) + 'ms' : 'N/A'}</span>
                            </div>
                        </div>
                    </div>
                </div>
                ` : ''}

                ${recommendations.length > 0 ? `
                <div class="expandable-section">
                    <div class="section-header" onclick="toggleSection(this)">
                        <h4>💡 Recommendations</h4>
                        <span class="toggle-icon">▼</span>
                    </div>
                    <div class="section-content">
                        <ul class="recommendations-list">
                            ${recommendations.map(rec => `<li>${rec}</li>`).join('')}
                        </ul>
                    </div>
                </div>
                ` : ''}
            </div>
        `;
    }

    function renderAgentOutput(agentOutput) {
        if (!agentOutput || typeof agentOutput !== 'object') {
            return '<div class="no-output">No agent output available</div>';
        }

        let html = '<div class="agent-output-content">';

        // Check if this is a workflow output with wheel data
        if (agentOutput.wheel_data) {
            html += renderWorkflowOutput(agentOutput);
        } else {
            html += renderStandardAgentOutput(agentOutput);
        }

        html += '</div>';
        return html;
    }

    function renderWorkflowOutput(agentOutput) {
        const wheelData = agentOutput.wheel_data;
        const workflowType = agentOutput.workflow_type || 'unknown';
        const finalOutput = agentOutput.final_output || {};

        let html = `
            <div class="workflow-output">
                <div class="workflow-header">
                    <h4>🎯 Workflow Output (${workflowType})</h4>
                </div>
        `;

        // Render wheel data
        if (wheelData && wheelData.items) {
            html += `
                <div class="wheel-section">
                    <h5>🎡 Generated Wheel: ${wheelData.name || 'Unnamed Wheel'}</h5>
                    <div class="wheel-info">
                        <span class="wheel-stat">Items: ${wheelData.items.length}</span>
                        <span class="wheel-stat">Trust Level: ${wheelData.trust_level || 'N/A'}</span>
                        <span class="wheel-stat">Personalization: ${wheelData.personalization_level || 'N/A'}</span>
                    </div>
                    <div class="wheel-items">
            `;

            wheelData.items.forEach((item, index) => {
                html += `
                    <div class="wheel-item">
                        <div class="item-header">
                            <span class="item-number">${index + 1}</span>
                            <span class="item-name">${item.name || 'Unnamed Activity'}</span>
                            <span class="item-difficulty ${item.difficulty || 'unknown'}">${item.difficulty || 'N/A'}</span>
                        </div>
                        <div class="item-details">
                            <p class="item-description">${item.description || 'No description'}</p>
                            <div class="item-meta">
                                <span class="item-domain">Domain: ${item.domain || 'N/A'}</span>
                                <span class="item-time">Time: ${item.estimated_time || 'N/A'}</span>
                            </div>
                        </div>
                    </div>
                `;
            });

            html += `
                    </div>
                </div>
            `;
        }

        // Render user response if available
        if (finalOutput.output_data && finalOutput.output_data.user_response) {
            html += `
                <div class="user-response-section">
                    <h5>💬 User Response</h5>
                    <div class="user-response">
                        ${finalOutput.output_data.user_response}
                    </div>
                </div>
            `;
        }

        // Render agent outputs if available
        if (finalOutput.agent_outputs) {
            html += `
                <div class="agent-outputs-section">
                    <h5>🤖 Agent Outputs</h5>
                    <div class="agent-outputs-grid">
            `;

            Object.entries(finalOutput.agent_outputs).forEach(([agentName, output]) => {
                html += `
                    <div class="agent-output-item">
                        <h6>${agentName.charAt(0).toUpperCase() + agentName.slice(1)}</h6>
                        <div class="agent-output-details">
                            ${typeof output === 'object' ?
                                Object.entries(output).map(([key, value]) =>
                                    `<div><strong>${key}:</strong> ${value}</div>`
                                ).join('') :
                                `<div>${output}</div>`
                            }
                        </div>
                    </div>
                `;
            });

            html += `
                    </div>
                </div>
            `;
        }

        html += '</div>';
        return html;
    }

    function renderStandardAgentOutput(agentOutput) {
        let html = `
            <div class="standard-agent-output">
                <h4>🤖 Agent Output</h4>
        `;

        // Render final output
        if (agentOutput.final_output) {
            html += `
                <div class="final-output-section">
                    <h5>Final Output</h5>
                    <div class="formatted-json">${formatContextData(agentOutput.final_output)}</div>
                </div>
            `;
        }

        // Render individual runs if available
        if (agentOutput.individual_runs && agentOutput.individual_runs.length > 0) {
            html += `
                <div class="individual-runs-section">
                    <h5>Individual Runs (${agentOutput.individual_runs.length})</h5>
                    <div class="runs-list">
            `;

            agentOutput.individual_runs.forEach((run, index) => {
                html += `
                    <div class="run-item">
                        <h6>Run ${index + 1}</h6>
                        <div class="formatted-json">${formatContextData(run)}</div>
                    </div>
                `;
            });

            html += `
                    </div>
                </div>
            `;
        }

        html += '</div>';
        return html;
    }

    function formatContextData(data) {
        if (!data || typeof data !== 'object') {
            return '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
        }

        // Create a more readable format for context data
        let html = '<div class="context-data">';

        for (const [key, value] of Object.entries(data)) {
            html += `
                <div class="context-item">
                    <div class="context-key">${key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</div>
                    <div class="context-value">
                        ${typeof value === 'object' ?
                            '<pre>' + JSON.stringify(value, null, 2) + '</pre>' :
                            '<span>' + String(value) + '</span>'
                        }
                    </div>
                </div>
            `;
        }

        html += '</div>';
        return html;
    }

    function getConfidenceClass(confidence) {
        switch(confidence) {
            case 'High': return 'confidence-high';
            case 'Moderate': return 'confidence-moderate';
            case 'Low': return 'confidence-low';
            default: return '';
        }
    }

    function getQualityClass(quality) {
        switch(quality) {
            case 'Excellent': return 'quality-excellent';
            case 'Good': return 'quality-good';
            case 'Fair': return 'quality-fair';
            case 'Poor': return 'quality-poor';
            default: return '';
        }
    }

    function renderRawDataTab(contextPackage) {
        return `
            <div class="raw-data-content">
                <div class="expandable-section">
                    <div class="section-header" onclick="toggleSection(this)">
                        <h4>📋 Complete Context Package</h4>
                        <span class="toggle-icon">▼</span>
                    </div>
                    <div class="section-content">
                        <div class="json-viewer">
                            <pre>${JSON.stringify(contextPackage, null, 2)}</pre>
                        </div>
                    </div>
                </div>

                <div class="expandable-section">
                    <div class="section-header" onclick="toggleSection(this)">
                        <h4>🔍 Input Context (Raw)</h4>
                        <span class="toggle-icon">▼</span>
                    </div>
                    <div class="section-content">
                        <div class="json-viewer">
                            <pre>${JSON.stringify(contextPackage.input_context || {}, null, 2)}</pre>
                        </div>
                    </div>
                </div>

                <div class="expandable-section">
                    <div class="section-header" onclick="toggleSection(this)">
                        <h4>🤖 Agent Output (Raw)</h4>
                        <span class="toggle-icon">▼</span>
                    </div>
                    <div class="section-content">
                        <div class="json-viewer">
                            <pre>${JSON.stringify(contextPackage.agent_output || {}, null, 2)}</pre>
                        </div>
                    </div>
                </div>

                <div class="expandable-section">
                    <div class="section-header" onclick="toggleSection(this)">
                        <h4>⚙️ Evaluation Context (Raw)</h4>
                        <span class="toggle-icon">▼</span>
                    </div>
                    <div class="section-content">
                        <div class="json-viewer">
                            <pre>${JSON.stringify(contextPackage.evaluation_context || {}, null, 2)}</pre>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    function toggleSection(header) {
        const content = header.nextElementSibling;
        const icon = header.querySelector('.toggle-icon');

        if (content.classList.contains('expanded')) {
            content.classList.remove('expanded');
            icon.textContent = '▶';
        } else {
            content.classList.add('expanded');
            icon.textContent = '▼';
        }
    }

    // Helper functions for agent communications rendering
    function renderAgentCommunicationsSummary(summary, workflowId) {
        return `
            <div class="summary-cards-grid">
                <div class="summary-card workflow-info">
                    <div class="card-icon">🔄</div>
                    <div class="card-content">
                        <h4>Workflow ID</h4>
                        <p class="card-value" title="${workflowId || 'N/A'}">${workflowId ? workflowId.substring(0, 8) + '...' : 'N/A'}</p>
                    </div>
                </div>
                <div class="summary-card total-communications">
                    <div class="card-icon">💬</div>
                    <div class="card-content">
                        <h4>Total Communications</h4>
                        <p class="card-value">${summary.total_communications || 0}</p>
                    </div>
                </div>
                <div class="summary-card success-rate">
                    <div class="card-icon">✅</div>
                    <div class="card-content">
                        <h4>Success Rate</h4>
                        <p class="card-value">${summary.total_communications > 0 ? Math.round((summary.successful_communications / summary.total_communications) * 100) : 0}%</p>
                    </div>
                </div>
                <div class="summary-card total-duration">
                    <div class="card-icon">⏱️</div>
                    <div class="card-content">
                        <h4>Total Duration</h4>
                        <p class="card-value">${(summary.total_duration_ms || 0).toFixed(2)}ms</p>
                    </div>
                </div>
                <div class="summary-card agents-involved">
                    <div class="card-icon">🤖</div>
                    <div class="card-content">
                        <h4>Agents Involved</h4>
                        <p class="card-value">${(summary.agents_involved || []).length}</p>
                    </div>
                </div>
                <div class="summary-card state-transitions">
                    <div class="card-icon">🔄</div>
                    <div class="card-content">
                        <h4>State Transitions</h4>
                        <p class="card-value">${summary.state_transitions || 0}</p>
                    </div>
                </div>
            </div>
        `;
    }

    function renderAgentCommunicationsOverview(agentComms) {
        const summary = agentComms.summary || {};
        const agents = agentComms.agents || [];

        return `
            <div class="overview-content">
                <div class="workflow-overview">
                    <h4>Workflow Overview</h4>
                    <div class="overview-grid">
                        <div class="overview-item">
                            <span class="overview-label">Workflow ID:</span>
                            <span class="overview-value" title="${agentComms.workflow_id || 'N/A'}">${agentComms.workflow_id ? agentComms.workflow_id.substring(0, 16) + '...' : 'N/A'}</span>
                        </div>
                        <div class="overview-item">
                            <span class="overview-label">Total Communications:</span>
                            <span class="overview-value">${summary.total_communications || 0}</span>
                        </div>
                        <div class="overview-item">
                            <span class="overview-label">Successful:</span>
                            <span class="overview-value">${summary.successful_communications || 0}</span>
                        </div>
                        <div class="overview-item">
                            <span class="overview-label">Failed:</span>
                            <span class="overview-value">${summary.failed_communications || 0}</span>
                        </div>
                        <div class="overview-item">
                            <span class="overview-label">Total Duration:</span>
                            <span class="overview-value">${(summary.total_duration_ms || 0).toFixed(2)}ms</span>
                        </div>
                        <div class="overview-item">
                            <span class="overview-label">Average Duration:</span>
                            <span class="overview-value">${summary.total_communications > 0 ? ((summary.total_duration_ms || 0) / summary.total_communications).toFixed(2) : 0}ms</span>
                        </div>
                    </div>
                </div>

                <div class="agents-overview">
                    <h4>Agents Involved</h4>
                    <div class="agents-list">
                        ${(summary.agents_involved || []).map(agent => `
                            <span class="agent-badge">${agent}</span>
                        `).join('')}
                    </div>
                </div>

                ${agents.length > 0 ? `
                <div class="execution-timeline">
                    <h4>Execution Timeline</h4>
                    <div class="timeline">
                        ${agents.map((agent, index) => `
                            <div class="timeline-item">
                                <div class="timeline-marker ${agent.success ? 'success' : 'failure'}">
                                    ${index + 1}
                                </div>
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <strong>${agent.agent}</strong> (${agent.stage})
                                        <span class="timeline-duration">${agent.duration_ms.toFixed(2)}ms</span>
                                    </div>
                                    <div class="timeline-timestamp">${agent.timestamp}</div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
                ` : ''}
            </div>
        `;
    }

    function renderEnhancedAgentTimeline(agents) {
        // Sort agents by timestamp to create a proper timeline
        const sortedAgents = [...agents].sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));

        // Detect warnings and issues
        const warnings = [];
        const mockedTools = [];
        const fallbacks = [];

        sortedAgents.forEach((agent, index) => {
            // Check for mocked tools
            if (agent.input && typeof agent.input === 'object') {
                const inputStr = JSON.stringify(agent.input);
                if (inputStr.includes('mock') || inputStr.includes('Mock')) {
                    mockedTools.push(`${agent.agent} (${agent.stage})`);
                }
            }

            // Check for fallbacks
            if (agent.output && typeof agent.output === 'object') {
                const outputStr = JSON.stringify(agent.output);
                if (outputStr.includes('fallback') || outputStr.includes('default') || outputStr.includes('backup')) {
                    fallbacks.push(`${agent.agent} (${agent.stage})`);
                }
            }

            // Check for errors or failures
            if (!agent.success || agent.error) {
                warnings.push({
                    type: 'error',
                    agent: agent.agent,
                    stage: agent.stage,
                    message: agent.error || 'Execution failed',
                    index: index
                });
            }

            // Check for unusually long execution times
            if (agent.duration_ms > 5000) {
                warnings.push({
                    type: 'performance',
                    agent: agent.agent,
                    stage: agent.stage,
                    message: `Long execution time: ${agent.duration_ms.toFixed(0)}ms`,
                    index: index
                });
            }
        });

        return `
            <div class="enhanced-timeline-content">
                <!-- Warnings and Issues Section -->
                ${warnings.length > 0 || mockedTools.length > 0 || fallbacks.length > 0 ? `
                <div class="warnings-section">
                    <h4>⚠️ Warnings & Issues</h4>
                    <div class="warnings-grid">
                        ${warnings.map(warning => `
                            <div class="warning-item ${warning.type}">
                                <div class="warning-icon">
                                    ${warning.type === 'error' ? '❌' : warning.type === 'performance' ? '⏱️' : '⚠️'}
                                </div>
                                <div class="warning-content">
                                    <div class="warning-title">${warning.agent} - ${warning.stage}</div>
                                    <div class="warning-message">${warning.message}</div>
                                </div>
                            </div>
                        `).join('')}

                        ${mockedTools.length > 0 ? `
                            <div class="warning-item mock">
                                <div class="warning-icon">🎭</div>
                                <div class="warning-content">
                                    <div class="warning-title">Mocked Tools Detected</div>
                                    <div class="warning-message">Tools were mocked in: ${mockedTools.join(', ')}</div>
                                </div>
                            </div>
                        ` : ''}

                        ${fallbacks.length > 0 ? `
                            <div class="warning-item fallback">
                                <div class="warning-icon">🔄</div>
                                <div class="warning-content">
                                    <div class="warning-title">Fallback Mechanisms Used</div>
                                    <div class="warning-message">Fallbacks detected in: ${fallbacks.join(', ')}</div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
                ` : ''}

                <!-- Enhanced Timeline -->
                <div class="timeline-section">
                    <h4>📅 Execution Timeline</h4>
                    <div class="timeline-container">
                        <div class="timeline-line"></div>
                        ${sortedAgents.map((agent, index) => {
                            const hasWarning = warnings.some(w => w.index === index);
                            const hasMock = mockedTools.includes(`${agent.agent} (${agent.stage})`);
                            const hasFallback = fallbacks.includes(`${agent.agent} (${agent.stage})`);

                            return `
                                <div class="timeline-event ${!agent.success ? 'failed' : ''} ${hasWarning ? 'warning' : ''} ${hasMock ? 'mocked' : ''} ${hasFallback ? 'fallback' : ''}">
                                    <div class="timeline-marker">
                                        <div class="marker-number">${index + 1}</div>
                                        <div class="marker-status">
                                            ${!agent.success ? '❌' : hasMock ? '🎭' : hasFallback ? '🔄' : '✅'}
                                        </div>
                                    </div>
                                    <div class="timeline-content">
                                        <div class="event-header" onclick="toggleTimelineEvent(this)">
                                            <div class="event-title">
                                                <span class="agent-name">${agent.agent}</span>
                                                <span class="stage-badge">${agent.stage}</span>
                                                ${hasMock ? '<span class="warning-badge mock">MOCKED</span>' : ''}
                                                ${hasFallback ? '<span class="warning-badge fallback">FALLBACK</span>' : ''}
                                            </div>
                                            <div class="event-meta">
                                                <span class="duration ${agent.duration_ms > 5000 ? 'slow' : agent.duration_ms > 1000 ? 'medium' : 'fast'}">
                                                    ${agent.duration_ms.toFixed(0)}ms
                                                </span>
                                                <span class="timestamp">${new Date(agent.timestamp).toLocaleTimeString()}</span>
                                                <span class="toggle-icon">▼</span>
                                            </div>
                                        </div>
                                        <div class="event-details">
                                            <div class="details-tabs">
                                                <button class="detail-tab active" onclick="switchDetailTab(this, 'summary')">Summary</button>
                                                <button class="detail-tab" onclick="switchDetailTab(this, 'input')">Input</button>
                                                <button class="detail-tab" onclick="switchDetailTab(this, 'output')">Output</button>
                                                ${agent.error ? '<button class="detail-tab error" onclick="switchDetailTab(this, \'error\')">Error</button>' : ''}
                                            </div>
                                            <div class="detail-content">
                                                <div class="detail-pane summary active">
                                                    <div class="summary-grid">
                                                        <div class="summary-item">
                                                            <span class="label">Status:</span>
                                                            <span class="value ${agent.success ? 'success' : 'error'}">${agent.success ? 'Success' : 'Failed'}</span>
                                                        </div>
                                                        <div class="summary-item">
                                                            <span class="label">Duration:</span>
                                                            <span class="value">${agent.duration_ms.toFixed(2)}ms</span>
                                                        </div>
                                                        <div class="summary-item">
                                                            <span class="label">Input Fields:</span>
                                                            <span class="value">${Object.keys(agent.input || {}).length}</span>
                                                        </div>
                                                        <div class="summary-item">
                                                            <span class="label">Output Fields:</span>
                                                            <span class="value">${Object.keys(agent.output || {}).length}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="detail-pane input">
                                                    <pre class="json-viewer">${JSON.stringify(agent.input || {}, null, 2)}</pre>
                                                </div>
                                                <div class="detail-pane output">
                                                    <pre class="json-viewer">${JSON.stringify(agent.output || {}, null, 2)}</pre>
                                                </div>
                                                ${agent.error ? `
                                                <div class="detail-pane error">
                                                    <div class="error-content">
                                                        <div class="error-message">${agent.error}</div>
                                                    </div>
                                                </div>
                                                ` : ''}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            `;
                        }).join('')}
                    </div>
                </div>
            </div>
        `;
    }

    function renderAgentInteractions(agents) {
        return `
            <div class="agent-interactions-content">
                <div class="interactions-header">
                    <h4>Agent Interactions (${agents.length})</h4>
                    <p>Detailed view of each agent's input, output, and execution metrics.</p>
                </div>
                <div class="interactions-list">
                    ${agents.map((agent, index) => `
                        <div class="interaction-item">
                            <div class="interaction-header" onclick="toggleSection(this)">
                                <div class="interaction-info">
                                    <span class="interaction-number">${index + 1}</span>
                                    <span class="agent-name">${agent.agent}</span>
                                    <span class="agent-stage">${agent.stage}</span>
                                    <span class="interaction-status ${agent.success ? 'success' : 'failure'}">
                                        ${agent.success ? '✅' : '❌'}
                                    </span>
                                </div>
                                <div class="interaction-metrics">
                                    <span class="duration">${agent.duration_ms.toFixed(2)}ms</span>
                                    <span class="timestamp">${new Date(agent.timestamp).toLocaleTimeString()}</span>
                                    <span class="toggle-icon">▼</span>
                                </div>
                            </div>
                            <div class="interaction-details section-content expanded">
                                <div class="details-grid">
                                    <div class="detail-section">
                                        <h5>📥 Input Data</h5>
                                        <div class="data-preview">
                                            <div class="data-summary">
                                                <span class="data-count">${Object.keys(agent.input || {}).length} fields</span>
                                                <span class="data-keys">${Object.keys(agent.input || {}).slice(0, 3).join(', ')}${Object.keys(agent.input || {}).length > 3 ? '...' : ''}</span>
                                            </div>
                                            <div class="expandable-section">
                                                <div class="section-header" onclick="toggleSection(this)">
                                                    <span>View Full Input</span>
                                                    <span class="toggle-icon">▶</span>
                                                </div>
                                                <div class="section-content">
                                                    <pre class="json-data">${JSON.stringify(agent.input || {}, null, 2)}</pre>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="detail-section">
                                        <h5>📤 Output Data</h5>
                                        <div class="data-preview">
                                            <div class="data-summary">
                                                <span class="data-count">${Object.keys(agent.output || {}).length} fields</span>
                                                <span class="data-keys">${Object.keys(agent.output || {}).slice(0, 3).join(', ')}${Object.keys(agent.output || {}).length > 3 ? '...' : ''}</span>
                                            </div>
                                            <div class="expandable-section">
                                                <div class="section-header" onclick="toggleSection(this)">
                                                    <span>View Full Output</span>
                                                    <span class="toggle-icon">▶</span>
                                                </div>
                                                <div class="section-content">
                                                    <pre class="json-data">${JSON.stringify(agent.output || {}, null, 2)}</pre>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="execution-metadata">
                                    <h5>⚙️ Execution Metadata</h5>
                                    <div class="metadata-grid">
                                        <div class="metadata-item">
                                            <span class="metadata-label">Agent:</span>
                                            <span class="metadata-value">${agent.agent}</span>
                                        </div>
                                        <div class="metadata-item">
                                            <span class="metadata-label">Stage:</span>
                                            <span class="metadata-value">${agent.stage}</span>
                                        </div>
                                        <div class="metadata-item">
                                            <span class="metadata-label">Duration:</span>
                                            <span class="metadata-value">${agent.duration_ms.toFixed(2)}ms</span>
                                        </div>
                                        <div class="metadata-item">
                                            <span class="metadata-label">Success:</span>
                                            <span class="metadata-value ${agent.success ? 'success' : 'failure'}">${agent.success ? 'Yes' : 'No'}</span>
                                        </div>
                                        <div class="metadata-item">
                                            <span class="metadata-label">Timestamp:</span>
                                            <span class="metadata-value">${agent.timestamp}</span>
                                        </div>
                                        ${agent.error ? `
                                        <div class="metadata-item error">
                                            <span class="metadata-label">Error:</span>
                                            <span class="metadata-value">${agent.error}</span>
                                        </div>
                                        ` : ''}
                                    </div>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    function renderStateTransitions(transitions) {
        return `
            <div class="state-transitions-content">
                <div class="transitions-header">
                    <h4>State Transitions (${transitions.length})</h4>
                    <p>Workflow state changes throughout the execution process.</p>
                </div>
                <div class="transitions-list">
                    ${transitions.map((transition, index) => `
                        <div class="transition-item">
                            <div class="transition-header" onclick="toggleSection(this)">
                                <div class="transition-info">
                                    <span class="transition-number">${index + 1}</span>
                                    <span class="transition-agent">${transition.agent}</span>
                                    <span class="transition-timestamp">${new Date(transition.timestamp).toLocaleString()}</span>
                                </div>
                                <span class="toggle-icon">▼</span>
                            </div>
                            <div class="transition-details section-content expanded">
                                <div class="states-comparison">
                                    <div class="state-section">
                                        <h5>📋 From State</h5>
                                        <div class="expandable-section">
                                            <div class="section-header" onclick="toggleSection(this)">
                                                <span>View State Data (${Object.keys(transition.from_state || {}).length} fields)</span>
                                                <span class="toggle-icon">▶</span>
                                            </div>
                                            <div class="section-content">
                                                <pre class="json-data">${JSON.stringify(transition.from_state || {}, null, 2)}</pre>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="state-arrow">
                                        <span class="arrow">→</span>
                                    </div>
                                    <div class="state-section">
                                        <h5>📋 To State</h5>
                                        <div class="expandable-section">
                                            <div class="section-header" onclick="toggleSection(this)">
                                                <span>View State Data (${Object.keys(transition.to_state || {}).length} fields)</span>
                                                <span class="toggle-icon">▶</span>
                                            </div>
                                            <div class="section-content">
                                                <pre class="json-data">${JSON.stringify(transition.to_state || {}, null, 2)}</pre>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="transition-metadata">
                                    <h5>⚙️ Transition Metadata</h5>
                                    <div class="metadata-grid">
                                        <div class="metadata-item">
                                            <span class="metadata-label">Triggered by:</span>
                                            <span class="metadata-value">${transition.agent}</span>
                                        </div>
                                        <div class="metadata-item">
                                            <span class="metadata-label">Timestamp:</span>
                                            <span class="metadata-value">${transition.timestamp}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    // Initial attachment of modal and checkbox listeners
    // attachModalListeners(); // Called within updateTable now
    // attachCheckboxListeners(); // Called within updateTable now
    updateCompareButtonState(); // Initial button state is fine here

    // Pre-populate modal if URL has #run-<id>
    if (window.location.hash && window.location.hash.startsWith('#run-')) {
        const runId = window.location.hash.substring(5); // Get ID after #run-
        const viewButton = document.querySelector(`.view-details[data-run-id="${runId}"]`);
        if (viewButton) {
            viewButton.click(); // Simulate click to open modal
        }
    }
});

// Helper functions for enhanced timeline
function toggleTimelineEvent(header) {
    const details = header.nextElementSibling;
    const icon = header.querySelector('.toggle-icon');

    if (details.style.display === 'none' || !details.style.display) {
        details.style.display = 'block';
        icon.textContent = '▲';
    } else {
        details.style.display = 'none';
        icon.textContent = '▼';
    }
}

function switchDetailTab(button, tabName) {
    const tabContainer = button.closest('.details-tabs');
    const contentContainer = button.closest('.event-details').querySelector('.detail-content');

    // Remove active class from all tabs and panes
    tabContainer.querySelectorAll('.detail-tab').forEach(tab => tab.classList.remove('active'));
    contentContainer.querySelectorAll('.detail-pane').forEach(pane => pane.classList.remove('active'));

    // Add active class to clicked tab and corresponding pane
    button.classList.add('active');
    const targetPane = contentContainer.querySelector(`.detail-pane.${tabName}`);
    if (targetPane) {
        targetPane.classList.add('active');
    }
}
</script>
{% endblock %}
